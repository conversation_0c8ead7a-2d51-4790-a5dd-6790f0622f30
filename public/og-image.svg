<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630" fill="none">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0" y1="0" x2="1200" y2="630" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#3B82F6" /> <!-- blue-500 -->
      <stop offset="100%" stop-color="#2563EB" /> <!-- blue-600 -->
    </linearGradient>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="15" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)" />
  
  <!-- Subtle grid pattern -->
  <path d="M0,0 L1200,630" stroke="white" stroke-width="1" stroke-opacity="0.05" />
  <path d="M0,630 L1200,0" stroke="white" stroke-width="1" stroke-opacity="0.05" />
  <path d="M600,0 L600,630" stroke="white" stroke-width="1" stroke-opacity="0.05" />
  <path d="M0,315 L1200,315" stroke="white" stroke-width="1" stroke-opacity="0.05" />
  
  <!-- Left side: Logo -->
  <g transform="translate(200, 245)">
    <!-- Logo wrapper with subtle glow -->
    <g filter="url(#glow)">
      <!-- Face outline -->
      <polygon 
        points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25" 
        stroke="white" 
        stroke-width="4" 
        fill="white"
        fill-opacity="0.1"
        stroke-linejoin="round"
      />
      
      <!-- Biometric grid lines -->
      <path d="M50,10 L50,90" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      <path d="M15,50 L85,50" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      <path d="M25,25 L75,75" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      <path d="M25,75 L75,25" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
      
      <!-- Eyes -->
      <rect x="32" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" fill="none" />
      <rect x="60" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" fill="none" />
      
      <!-- Mouth -->
      <path d="M35,65 L65,65" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" />
    </g>
  </g>
  
  <!-- Right side: Features with modern icons -->
  <!-- Feature 1: Find Stolen Images -->
  <g transform="translate(450, 180)">
    <circle cx="25" cy="25" r="25" fill="white" fill-opacity="0.15" />
    <path d="M15,25 L22,32 L35,18" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
    <text x="70" y="32" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" font-weight="500">Find Stolen Images</text>
  </g>
  
  <!-- Feature 2: Verify Online Identities -->
  <g transform="translate(450, 280)">
    <circle cx="25" cy="25" r="25" fill="white" fill-opacity="0.15" />
    <path d="M15,25 L22,32 L35,18" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
    <text x="70" y="32" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" font-weight="500">Verify Online Identities</text>
  </g>
  
  <!-- Feature 3: Protect Your Privacy -->
  <g transform="translate(450, 380)">
    <circle cx="25" cy="25" r="25" fill="white" fill-opacity="0.15" />
    <path d="M15,25 L22,32 L35,18" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
    <text x="70" y="32" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" font-weight="500">Protect Your Privacy</text>
  </g>
  
  <!-- Brand text with modern typography -->
  <g transform="translate(600, 470)">
    <text x="0" y="0" font-family="'Electrolize', system-ui, -apple-system, sans-serif" font-size="72" font-weight="700" fill="white" text-anchor="middle">FaceTrace Pro</text>
  </g>
  
  <!-- URL at the bottom -->
  <text x="600" y="550" font-family="system-ui, -apple-system, sans-serif" font-size="28" fill="white" fill-opacity="0.9" text-anchor="middle">https://facetrace.pro</text>
</svg> 