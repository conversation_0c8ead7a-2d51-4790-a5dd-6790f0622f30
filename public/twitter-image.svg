<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="630" viewBox="0 0 1200 630" fill="none">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0" y1="0" x2="1200" y2="630" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#3B82F6" /> <!-- blue-500 -->
      <stop offset="100%" stop-color="#2563EB" /> <!-- blue-600 -->
    </linearGradient>
    <linearGradient id="text-gradient" x1="300" y1="300" x2="800" y2="400" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#FFFFFF" />
      <stop offset="100%" stop-color="#E5E7EB" /> <!-- gray-200 -->
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)" />
  
  <!-- Geometric pattern in background -->
  <path d="M0,0 L1200,630" stroke="white" stroke-width="1" stroke-opacity="0.1" />
  <path d="M0,630 L1200,0" stroke="white" stroke-width="1" stroke-opacity="0.1" />
  <path d="M600,0 L600,630" stroke="white" stroke-width="1" stroke-opacity="0.1" />
  <path d="M0,315 L1200,315" stroke="white" stroke-width="1" stroke-opacity="0.1" />
  
  <!-- Logo centered in the image -->
  <g transform="translate(360, 165) scale(1.5)">
    <!-- Face outline - geometric with bolder stroke -->
    <polygon 
      points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25" 
      stroke="white" 
      stroke-width="4" 
      stroke-opacity="1" 
      fill="none" 
      stroke-linejoin="round"
    />
    
    <!-- Biometric identification grid -->
    <path d="M50,10 L50,90" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    <path d="M15,50 L85,50" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    <path d="M25,25 L75,75" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    <path d="M25,75 L75,25" stroke="white" stroke-width="0.9" stroke-opacity="0.5" stroke-dasharray="3 2" />
    
    <!-- Eyes - geometric with thicker stroke -->
    <rect x="32" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" stroke-opacity="1" fill="none" />
    <rect x="60" y="40" width="8" height="7" rx="1" stroke="white" stroke-width="2.5" stroke-opacity="1" fill="none" />
    
    <!-- Mouth - straight line for serious expression -->
    <path d="M35,65 L65,65" stroke="white" stroke-width="2" stroke-opacity="0.9" fill="none" stroke-linecap="round" />
    
    <!-- Intersection points with larger dots -->
    <circle cx="50" cy="10" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="25" cy="25" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="15" cy="50" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="25" cy="75" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="50" cy="90" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="75" cy="75" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="85" cy="50" r="1.5" fill="white" fill-opacity="1" />
    <circle cx="75" cy="25" r="1.5" fill="white" fill-opacity="1" />
    
    <!-- Biometric measurement points -->
    <circle cx="50" cy="45" r="1" fill="white" fill-opacity="0.7" />
    <circle cx="50" cy="65" r="1" fill="white" fill-opacity="0.7" />
    <circle cx="35" cy="50" r="1" fill="white" fill-opacity="0.7" />
    <circle cx="65" cy="50" r="1" fill="white" fill-opacity="0.7" />
  </g>
  
  <!-- Brand text -->
  <text x="600" y="450" font-family="'Electrolize', monospace" font-size="64" font-weight="bold" fill="url(#text-gradient)" text-anchor="middle">FaceTrace Pro</text>
  <text x="600" y="500" font-family="sans-serif" font-size="24" fill="white" fill-opacity="0.8" text-anchor="middle">Find Where Faces Appear Across The Web</text>
</svg> 