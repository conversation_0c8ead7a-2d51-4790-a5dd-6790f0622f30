/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#5158f6",
          dark: "#3D40CC",
          light: "#7C81FF",
        },
        secondary: {
          DEFAULT: "#5158f6",
          dark: "#3D40CC",
          light: "#7C81FF",
        },
        background: "#0F172A",
        foreground: "#F8FAFC",
        border: "#334155",
        input: "#1E293B",
        ring: "#3D40CC",
      },
      fontFamily: {
        sans: ["var(--font-inter)", "sans-serif"],
      },
      boxShadow: {
        glow: "0 0 20px 5px rgba(81, 88, 246, 0.25)",
      },
      borderRadius: {
        xl: "1rem",
        "2xl": "1.5rem",
      },
    },
  },
  plugins: [],
}

