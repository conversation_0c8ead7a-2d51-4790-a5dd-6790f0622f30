# Payment System Toggle

This document explains how to enable or disable the payment system in the FaceTrace application.

## Current Status: PAYMENT DISABLED ✅

Payment is currently disabled. Users can unlock results for free.

## How to Toggle Payment

### To Disable Payment (Current Setting)

Set the following in your `.env` file:
```
DISABLE_PAYMENT=true
```

**What happens when payment is disabled:**
- Users can unlock search results for free
- The unlock modal shows a "Free Access" option instead of payment
- No Stripe payment processing occurs
- Results are unlocked immediately without payment verification

### To Enable Payment

Set the following in your `.env` file:
```
DISABLE_PAYMENT=false
```
or remove the `DISABLE_PAYMENT` variable entirely.

**What happens when payment is enabled:**
- Users must pay $5.00 to unlock search results
- The unlock modal shows payment options via Stripe
- Payment verification is required before unlocking
- Users with tokens can still use tokens to unlock

## Implementation Details

### Files Modified for Payment Toggle

1. **`src/app/api/reports/unlock-free/route.ts`** - New endpoint for free unlocking
2. **`src/components/UnlockModal.tsx`** - Modified to show free unlock option
3. **`src/app/api/reports/unlock/route.ts`** - Original payment unlock endpoint (unchanged)

### How It Works

When `DISABLE_PAYMENT=true`:
- The UnlockModal component displays a prominent "Free Access" section
- Clicking "Unlock Results for Free" calls the `/api/reports/unlock-free` endpoint
- The free unlock endpoint bypasses all payment verification
- Reports are unlocked immediately with `price: '0.00'`

When payment is enabled:
- The UnlockModal shows the original payment interface
- Users must complete Stripe payment to unlock
- Payment verification is required via the original unlock endpoint

### Search Behavior

The search functionality remains unchanged:
- All searches create private reports initially
- Users see redacted results until they unlock
- The unlock process is where payment/free access is determined

## Testing

To test the free unlock functionality:
1. Ensure `DISABLE_PAYMENT=true` in your `.env` file
2. Restart the development server
3. Perform a search
4. Click "Unlock Full Results" on the results page
5. You should see the "Free Access" option in the unlock modal
6. Click "Unlock Results for Free" to unlock without payment

## Re-enabling Payment

To re-enable payment in the future:
1. Set `DISABLE_PAYMENT=false` in `.env`
2. Restart the application
3. The original payment flow will be restored
4. Users will need to pay or use tokens to unlock results

## Notes

- The payment infrastructure (Stripe) remains intact and functional
- This is a feature toggle, not a permanent removal of payment functionality
- Token-based unlocking still works when users have tokens
- The free unlock creates audit trails with `price: '0.00'` for tracking
