'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import dynamic from 'next/dynamic';
import { PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { Button } from '@/app/components/Button';
import confetti from 'canvas-confetti';

// Dynamically import Stripe-related components for client-side only rendering
const StripeElements = dynamic(() => import('./stripe/StripeElements'), { ssr: false });

// No Stripe-specific code here - all moved to the client component

// Step 1: Email Form
const EmailForm = ({
  email,
  setEmail,
  onSubmit,
  error,
  isLoading
}: {
  email: string;
  setEmail: (email: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  error: string | null;
  isLoading: boolean;
}) => {
  const [formErrors, setFormErrors] = useState<{ email?: string }>({});

  const validateEmail = () => {
    if (!email) {
      setFormErrors({ email: 'Email is required' });
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setFormErrors({ email: 'Please enter a valid email address' });
      return false;
    }
    setFormErrors({});
    return true;
  };
  
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Email Address
        </label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          onBlur={validateEmail}
          className={`w-full px-3 py-2 border ${
            formErrors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white`}
          placeholder="<EMAIL>"
          disabled={isLoading}
          required
        />
        {formErrors.email && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.email}</p>
        )}
      </div>
      
      <div className="mt-6">
        <Button
          type="submit"
          disabled={isLoading || !email}
          isLoading={isLoading}
          className="w-full"
        >
          Continue
        </Button>
      </div>
    </form>
  );
};

// Step 2: Billing Details Form
const BillingForm = ({
  fullName,
  setFullName,
  zipCode,
  setZipCode,
  onSubmit,
  onBack,
  error,
  isLoading,
  email
}: {
  fullName: string;
  setFullName: (name: string) => void;
  zipCode: string;
  setZipCode: (zip: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onBack: () => void;
  error: string | null;
  isLoading: boolean;
  email: string;
}) => {
  const [formErrors, setFormErrors] = useState<{ fullName?: string; zipCode?: string }>({});

  const validateForm = () => {
    const errors: { fullName?: string; zipCode?: string } = {};
    
    if (!fullName) {
      errors.fullName = 'Name is required';
    }
    
    if (!zipCode) {
      errors.zipCode = 'ZIP code is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      if (validateForm()) {
        onSubmit(e);
      }
    }} className="space-y-4">
      {/* Display Email */}
      <div>
        <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Email</label>
        <p className="text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-md">{email}</p>
      </div>
      
      {/* Full Name Input */}
      <div>
        <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Full Name
        </label>
        <input
          id="fullName"
          type="text"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
          className={`w-full px-3 py-2 border ${
            formErrors.fullName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white`}
          placeholder="John Doe"
          disabled={isLoading}
          required
        />
        {formErrors.fullName && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.fullName}</p>
        )}
      </div>
      
      {/* ZIP Code Input */}
      <div>
        <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Billing Postal Code
        </label>
        <input
          id="zipCode"
          type="text"
          value={zipCode}
          onChange={(e) => setZipCode(e.target.value)}
          className={`w-full px-3 py-2 border ${
            formErrors.zipCode ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white`}
          placeholder="90210"
          disabled={isLoading}
          required
        />
        {formErrors.zipCode && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.zipCode}</p>
        )}
      </div>
      
      <div className="flex justify-between mt-6">
        <Button
          onClick={onBack}
          disabled={isLoading}
          variant="outline"
        >
          Back
        </Button>
        <Button
          type="submit"
          disabled={isLoading || !fullName || !zipCode}
          isLoading={isLoading}
        >
          Continue to Payment
        </Button>
      </div>
    </form>
  );
};

// Step 3: Payment Form (Client Component)
const PaymentForm = ({
  email,
  zipCode,
  fullName,
  onBack,
  onSuccess,
  onError,
  isLoading,
  setIsLoading,
  setClientSecret
}: {
  email: string;
  zipCode: string;
  fullName: string;
  onBack: () => void;
  onSuccess: (email: string, paymentIntentId: string) => void;
  onError: (message: string) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  setClientSecret: (secret: string) => void;
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [formComplete, setFormComplete] = useState(false);
  const [paymentIntentCreated, setPaymentIntentCreated] = useState(false);

  // Create payment intent once the component mounts
  useEffect(() => {
    const createPaymentIntent = async () => {
      if (paymentIntentCreated) return;
      
      try {
        setIsLoading(true);
        
        // Create payment intent for guest user search ($5.00)
        const response = await fetch('/api/core?action=create-payment-intent', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: email,
            context: 'guest', // Important: indicates this is for a guest user
            service: 'FaceTrace Search',
            billingDetails: {
              name: fullName,
              address: {
                postal_code: zipCode
              }
            }
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to prepare payment');
        }

        const data = await response.json();
        
        if (!data.clientSecret) {
          throw new Error('Payment preparation failed. Missing client secret.');
        }
        
        setClientSecret(data.clientSecret);
        setPaymentIntentCreated(true);
      } catch (err: any) {
        console.error("Error creating payment intent:", err);
        onError(err.message || "Failed to prepare payment form");
      } finally {
        setIsLoading(false);
      }
    };

    if (email && zipCode && fullName && !paymentIntentCreated) {
      createPaymentIntent();
    }
  }, [email, zipCode, fullName, paymentIntentCreated, setClientSecret, setIsLoading, onError]);
  
  // Update form complete state when elements are ready
  useEffect(() => {
    if (!stripe || !elements) {
      setFormComplete(false);
      return;
    }
    
    setFormComplete(agreedToTerms);
  }, [stripe, elements, agreedToTerms]);

  const handleSubmit = async () => {
    if (!stripe || !elements) {
      return;
    }

    if (!agreedToTerms) {
      onError('Please agree to the terms and conditions');
      return;
    }

    setIsLoading(true);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin,
          receipt_email: email,
        },
        redirect: 'if_required',
      });

      if (error) {
        throw new Error(error.message || 'Something went wrong with your payment');
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Payment succeeded!
        onSuccess(email, paymentIntent.id);
      } else {
        // Payment status is unknown
        throw new Error('Payment status unknown. Please contact support.');
      }
    } catch (err: unknown) {
      const error = err as Error;
      onError(error.message || 'Payment processing failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Display Email Read-only */}
      <div>
        <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Email</label>
        <p className="text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-md">{email}</p>
      </div>
      
      {/* Billing ZIP */}
      <div>
        <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Billing Postal Code
        </label>
        <p className="text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-md">{zipCode}</p>
      </div>
      
      {/* Card Details */}
      <div className="mt-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Card Details
        </label>
        <div className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md p-3 shadow-inner">
          <PaymentElement />
        </div>
      </div>
      
      {/* Terms Agreement Checkbox */}
      <div className="flex items-start space-x-2">
        <input
          id="terms"
          name="terms"
          type="checkbox"
          checked={agreedToTerms}
          onChange={(e) => setAgreedToTerms(e.target.checked)}
          className="h-4 w-4 mt-1 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label htmlFor="terms" className="text-xs text-gray-500 dark:text-gray-400">
          I agree to the <a href="/terms-of-use" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Terms of Service</a> and <a href="/privacy-policy" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a>.
        </label>
      </div>
      
      {/* Secure Payment Notice */}
      <div className="mt-4">
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
          Your payment information is stored securely by Stripe.
        </p>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          onClick={onBack}
          disabled={isLoading}
          variant="outline"
        >
          Back
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isLoading || !formComplete || !stripe || !elements}
          isLoading={isLoading}
        >
          Pay $5.00
        </Button>
      </div>
    </div>
  );
};

// Step 4: Success Component
const SuccessScreen = ({
  closeTimer,
  email
}: {
  closeTimer: number;
  email: string;
}) => {
  return (
    <div className="text-center py-8">
      <div className="mb-4 flex justify-center">
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
          <svg className="w-10 h-10 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      </div>
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
        Payment Successful!
      </h2>
      <p className="text-gray-600 dark:text-gray-300 mb-6">
        Your payment was successful. A receipt has been sent to {email}.<br />
        Your search is now processing.
      </p>
      <div className="w-full h-2 bg-blue-100 dark:bg-blue-900/30 rounded-full overflow-hidden">
        <motion.div 
          className="h-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]"
          initial={{ width: "0%" }}
          animate={{ width: "100%" }}
          transition={{ duration: closeTimer }}
        />
      </div>
    </div>
  );
};

// Main Modal Component
interface GuestPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (email: string, clientSecret: string) => void;
  error?: string | null;
}

export default function GuestPaymentModal({
  isOpen,
  onClose,
  onSuccess,
  error: initialError = null
}: GuestPaymentModalProps) {
  // Guest information
  const [email, setEmail] = useState<string>('');
  const [fullName, setFullName] = useState<string>('');
  const [zipCode, setZipCode] = useState<string>('');
  
  // Payment info
  const [clientSecret, setClientSecret] = useState<string>('');
  const [currentStep, setCurrentStep] = useState<'email' | 'billing' | 'payment' | 'success'>('email');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(initialError);
  const [closeTimer, setCloseTimer] = useState(5); // 5 seconds countdown
  const [paymentIntentId, setPaymentIntentId] = useState('');
  
  // Reset error when parent error changes & handle modal close
  useEffect(() => {
    setError(initialError);
    
    // Reset modal state when it closes
    if (!isOpen) {
      setCurrentStep('email');
      setClientSecret('');
      setError(null);
      setEmail('');
      setFullName('');
      setZipCode('');
    }
  }, [initialError, isOpen]);
  
  // Success timer for step 4
  useEffect(() => {
    if (currentStep === 'success') {
      // Trigger confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
      
      const timer = setInterval(() => {
        setCloseTimer((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            // Pass the payment intent ID to callback
            onSuccess(email, paymentIntentId);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      return () => clearInterval(timer);
    }
  }, [currentStep, email, paymentIntentId, onSuccess]);

  // Handle email form submission (Step 1 -> Step 2)
  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    setError(null);
    setCurrentStep('billing');
  };
  
  // Handle billing form submission (Step 2 -> Step 3)
  const handleBillingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setCurrentStep('payment');
  };
  
  // Handle payment success (Step 3 -> Step 4)
  const handlePaymentSuccess = (email: string, paymentIntentId: string) => {
    setPaymentIntentId(paymentIntentId);
    setCurrentStep('success');
  };
  
  // Handle payment error
  const handlePaymentError = (message: string) => {
    setError(message);
  };

  // Options for Stripe Elements appearance
  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#92A5FF',
      colorBackground: '#ffffff',
      colorText: '#30313d',
      colorDanger: '#df1b41',
      fontFamily: 'Inter, system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '8px',
    },
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
      >
        {/* Close button - don't allow closing during processing */}
        {currentStep !== 'success' ? (
          <button
            onClick={() => {
              if (!isLoading) onClose();
            }}
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
            disabled={isLoading}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        ) : (
          <div className="absolute top-3 right-3 flex items-center justify-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full w-8 h-8 text-sm font-semibold">
            {closeTimer}
          </div>
        )}

        {/* Header */}
        <div className="text-center mb-6">
          <div className="h-12 w-12 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1">Complete Your Search</h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">
            Guest Search
          </p>
          <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
            $5.00
          </p>
        </div>

        {/* Progress indicator */}
        <div className="flex mb-6 w-full max-w-xs mx-auto">
          <div className={`flex-1 h-1 ${currentStep === 'email' ? 'bg-blue-500' : 'bg-blue-200 dark:bg-blue-900'}`}></div>
          <div className={`flex-1 h-1 ${currentStep === 'billing' ? 'bg-blue-500' : 'bg-blue-200 dark:bg-blue-900'}`}></div>
          <div className={`flex-1 h-1 ${currentStep === 'payment' ? 'bg-blue-500' : 'bg-blue-200 dark:bg-blue-900'}`}></div>
          <div className={`flex-1 h-1 ${currentStep === 'success' ? 'bg-blue-500' : 'bg-blue-200 dark:bg-blue-900'}`}></div>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Form Steps */}
        <AnimatePresence mode="wait">
          {currentStep === 'email' && (
            <motion.div
              key="email"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <EmailForm
                email={email}
                setEmail={setEmail}
                onSubmit={handleEmailSubmit}
                error={error}
                isLoading={isLoading}
              />
            </motion.div>
          )}
          
          {currentStep === 'billing' && (
            <motion.div
              key="billing"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <BillingForm
                fullName={fullName}
                setFullName={setFullName}
                zipCode={zipCode}
                setZipCode={setZipCode}
                onSubmit={handleBillingSubmit}
                onBack={() => setCurrentStep('email')}
                error={error}
                isLoading={isLoading}
                email={email}
              />
            </motion.div>
          )}
          
          {currentStep === 'payment' && (
            <motion.div
              key="payment"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {clientSecret ? (
                <div className="client-only-stripe-wrapper">
                  <StripeElements clientSecret={clientSecret}>
                    <PaymentForm
                      email={email}
                      zipCode={zipCode}
                      fullName={fullName}
                      onBack={() => setCurrentStep('billing')}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                      isLoading={isLoading}
                      setIsLoading={setIsLoading}
                      setClientSecret={setClientSecret}
                    />
                  </StripeElements>
                </div>
              ) : (
                <div className="flex justify-center items-center py-10">
                  <svg className="animate-spin h-8 w-8 text-[#92A5FF]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              )}
            </motion.div>
          )}
          
          {currentStep === 'success' && (
            <motion.div
              key="success"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SuccessScreen
                closeTimer={closeTimer}
                email={email}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Security notice */}
        <div className="flex items-center justify-center mt-6 text-xs text-gray-500 dark:text-gray-400">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          Secure payment processed by Stripe
        </div>
      </motion.div>
    </div>
  );
}
