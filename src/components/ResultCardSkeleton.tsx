import React from 'react';

export function ResultCardSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 animate-pulse">
      {/* Image Placeholder */}
      <div className="relative aspect-video bg-gray-200"></div>
      
      <div className="p-4">
        {/* Title Placeholder */}
        <div className="h-5 bg-gray-200 rounded w-3/4 mb-4"></div>
        
        {/* Source URL Placeholder */}
        <div className="space-y-2 mb-4">
          <div className="h-3 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-full"></div>
        </div>
        
        {/* Button Placeholder */}
        <div className="flex justify-end">
          <div className="h-8 bg-gray-200 rounded w-28"></div>
        </div>
      </div>
    </div>
  );
} 