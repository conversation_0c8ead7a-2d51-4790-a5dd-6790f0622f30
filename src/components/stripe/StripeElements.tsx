'use client';

import { ReactNode } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe promise with the publishable key - only on client side
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_your_key');

// Define the appearance options for Stripe Elements
const defaultAppearance = {
  theme: 'stripe' as const,
  variables: {
    colorPrimary: '#92A5FF',
    colorBackground: '#ffffff',
    colorText: '#30313d',
    colorDanger: '#df1b41',
    fontFamily: 'Inter, system-ui, sans-serif',
    spacingUnit: '4px',
    borderRadius: '8px',
  },
};

// Stripe element types
export type StripeElementsOptions = {
  clientSecret: string;
  appearance?: typeof defaultAppearance;
};

// Create a client-side-only wrapper for Stripe Elements
interface StripeElementsProps {
  clientSecret: string;
  children: ReactNode;
  appearance?: typeof defaultAppearance;
}

export default function StripeElements({ 
  clientSecret, 
  children,
  appearance = defaultAppearance 
}: StripeElementsProps) {
  return (
    <Elements stripe={stripePromise} options={{ clientSecret, appearance }}>
      {children}
    </Elements>
  );
}
