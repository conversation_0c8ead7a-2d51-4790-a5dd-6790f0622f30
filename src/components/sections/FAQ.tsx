"use client"

import React, { useState } from "react";
import { ChevronDown, Search, HelpCircle, Shield, BookOpen } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Link from "next/link";

// Define proper TypeScript interfaces for our data
interface FAQ {
  question: string;
  answer: string;
}

interface Category {
  title: string;
  icon: React.ElementType;
  color: string;
  bgColor: string;
  faqs: FAQ[];
}

type CategoryKey = 'general' | 'privacy' | 'legal';

// Type the categories object
const faqCategories: Record<CategoryKey, Category> = {
  general: {
    title: "General Information",
    icon: HelpCircle,
    color: "text-blue-500",
    bgColor: "bg-blue-100",
    faqs: [
      {
        question: "What is FaceTrace?",
        answer:
          "Advanced image search platform that helps you verify where photos appear online. By uploading a photo, you can discover websites, social media profiles, and other digital sources where similar images have been published to enhance your digital awareness and safety."
      },
      {
        question: "How does FaceTrace operate?",
        answer:
          "FaceTrace uses visual characteristics, compares similarities between photos, and efficiently search through vast amounts of online data."
      },
      {
        question: "What does the score indicate?",
        answer:
          "Each search result displays a quality score from 0 to 100, measuring match accuracy. Scores above 83 generally signify a reliable match."
      }
    ]
  },
  privacy: {
    title: "Privacy & Security",
    icon: Shield,
    color: "text-blue-500",
    bgColor: "bg-blue-100",
    faqs: [
      {
        question: "How is my search kept private?",
        answer:
          "FaceTrace is built with privacy in mind: images you search are not permanently stored, search history is cleared within 24 hours, and we don't log your IP address."
      },
      {
        question: "Can I remove my photo(s) from the database?",
        answer:
          "Yes, removing your image is straightforward. Follow the instructions on our removal page to quickly delete your photo from our system."
      },
      {
        question: "What information is stored?",
        answer:
          "FaceTrace only keeps a small, low-resolution thumbnail and source URL. The original image is never stored."
      }
    ]
  },
  legal: {
    title: "Legal & Ethics",
    icon: BookOpen,
    color: "text-blue-500",
    bgColor: "bg-blue-100",
    faqs: [
      {
        question: "What are FaceTrace's legal guidelines?",
        answer:
          "FaceTrace operates within legal boundaries by indexing only publicly available images, respecting copyright, and following ethical standards."
      },
      {
        question: "Is FaceTrace copyright compliant?",
        answer:
          "Yes, FaceTrace only stores altered, low-resolution thumbnails under fair use guidelines."
      },
      {
        question: "Can FaceTrace verify identities?",
        answer:
          "FaceTrace is not designed to confirm identities. It should not be used as the sole basis for verification."
      }
    ]
  }
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

const FAQ = () => {
  const [openCategory, setOpenCategory] = useState<CategoryKey>("general");
  const [searchQuery, setSearchQuery] = useState("");

  // Filter FAQs based on search query
  const getFilteredFaqs = () => {
    if (!searchQuery.trim()) return faqCategories;

    const filtered: Partial<Record<CategoryKey, Category>> = {};
    Object.entries(faqCategories).forEach(([key, category]) => {
      const matchingFaqs = category.faqs.filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      );

      if (matchingFaqs.length > 0) {
        filtered[key as CategoryKey] = { ...category, faqs: matchingFaqs };
      }
    });
    return filtered;
  };

  const filteredCategories = getFilteredFaqs();
  const hasResults = Object.keys(filteredCategories).length > 0;

  return (
    <section
      id="faq"
      className="py-16 bg-gradient-to-br from-blue-50 via-blue-50/30 to-blue-50 relative overflow-hidden"
    >
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-blue-900 mb-4"
            variants={itemVariants}
          >
            Frequently Asked Questions
          </motion.h2>

          <motion.p
            className="text-blue-700 text-lg max-w-2xl mx-auto mb-8"
            variants={itemVariants}
          >
            Find answers to common questions about FaceTrace
          </motion.p>

          <motion.div
            className="max-w-md mx-auto relative mb-12"
            variants={itemVariants}
          >
            <div className="relative">
              <input
                type="text"
                placeholder="Search questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-white/70 backdrop-blur-sm py-3 px-5 pr-12 rounded-full shadow-md border border-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-blue-500">
                <Search className="w-5 h-5" />
              </div>
            </div>
          </motion.div>
        </motion.div>

        {hasResults ? (
          <motion.div
            className="max-w-4xl mx-auto"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={containerVariants}
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              {Object.entries(filteredCategories).map(([key, category]) => {
                const Icon = category.icon;
                return (
                  <motion.div
                    key={key}
                    variants={itemVariants}
                    className={`p-4 rounded-xl cursor-pointer transition-all ${
                      openCategory === key
                        ? "bg-white shadow-md border border-blue-200"
                        : "bg-white/40 hover:bg-white/80 border border-transparent"
                    }`}
                    onClick={() => setOpenCategory(key as CategoryKey)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${category.bgColor}`}>
                        <Icon className={`w-5 h-5 ${category.color}`} />
                      </div>
                      <h3
                        className={`font-semibold ${
                          openCategory === key ? "text-blue-900" : "text-blue-700"
                        }`}
                      >
                        {category.title}
                      </h3>
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {filteredCategories[openCategory] && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg"
              >
                <h3 className="text-2xl font-semibold text-blue-900 mb-6 flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${filteredCategories[openCategory]?.bgColor}`}>
                    {filteredCategories[openCategory]?.icon && React.createElement(filteredCategories[openCategory]!.icon, {
                      className: `w-6 h-6 ${filteredCategories[openCategory]?.color}`
                    })}
                  </div>
                  {filteredCategories[openCategory]?.title}
                </h3>

                <Accordion type="single" collapsible className="space-y-4">
                  {filteredCategories[openCategory]?.faqs.map((faq, index) => (
                    <AccordionItem
                      key={index}
                      value={`${openCategory}-${index}`}
                      className="border border-blue-100 rounded-lg overflow-hidden mb-3 shadow-sm"
                    >
                      <AccordionTrigger className="hover:no-underline bg-blue-50/50 px-4 py-3">
                        <div className="flex items-center justify-between w-full">
                          <span className="text-blue-900 font-medium text-left">
                            {faq.question}
                          </span>
                          <ChevronDown className="w-5 h-5 text-blue-500 shrink-0 transition-transform duration-200" />
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-4 py-3 text-blue-700 bg-white">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </motion.div>
            )}
          </motion.div>
        ) : (
          <div className="text-center py-8 bg-white/80 backdrop-blur-sm rounded-2xl shadow-md">
            <p className="text-blue-800">
              No questions found matching "{searchQuery}"
            </p>
            <Button
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => setSearchQuery("")}
            >
              Clear Search
            </Button>
          </div>
        )}

        {/* CTA Section */}
        <motion.div
          className="mt-12 mx-auto max-w-4xl bg-white p-8 rounded-xl shadow-xl text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-3xl font-bold text-blue-900 mb-4">
            Still have questions?
          </h2>
          <p className="text-blue-700 text-lg mb-6">
            If you didn't find the answer you're looking for, our support team is here to help.
          </p>
          <Button
            asChild
            className="bg-gradient-to-r from-blue-600 to-blue-600 hover:from-blue-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl shadow-md transition-all duration-300"
          >
            <Link href="/contact">Contact Us</Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default FAQ; 