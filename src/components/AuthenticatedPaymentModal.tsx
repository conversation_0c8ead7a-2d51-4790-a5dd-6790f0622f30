'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import dynamic from 'next/dynamic';
import { PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { Button } from '@/app/components/Button';
import confetti from 'canvas-confetti';

// Dynamically import Stripe-related components for client-side only rendering
const StripeElements = dynamic(() => import('./stripe/StripeElements'), { ssr: false });

// Payment Form component that uses Stripe Elements (Client-side only)
const PaymentForm = ({
  onBack,
  onSuccess,
  onError,
  isLoading,
  setIsLoading
}: {
  onBack: () => void;
  onSuccess: (paymentIntentId: string) => void;
  onError: (message: string) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [zipCode, setZipCode] = useState('');
  const [formComplete, setFormComplete] = useState(false);

  // Update form complete state when elements are ready
  useEffect(() => {
    if (!stripe || !elements) {
      setFormComplete(false);
      return;
    }
    
    setFormComplete(agreedToTerms && !!zipCode);
  }, [stripe, elements, agreedToTerms, zipCode]);

  const handleSubmit = async () => {
    if (!stripe || !elements) {
      return;
    }

    if (!agreedToTerms) {
      onError('Please agree to the terms and conditions');
      return;
    }

    setIsLoading(true);

    try {
      // Create the payment method with the current elements
      const { error: submitError } = await elements.submit();
      if (submitError) {
        throw new Error(submitError.message);
      }

      const result = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin,
          payment_method_data: {
            billing_details: {
              address: {
                postal_code: zipCode
              }
            }
          }
        },
        redirect: 'if_required',
      });

      if (result.error) {
        throw new Error(result.error.message || 'Something went wrong with your payment');
      }

      // TypeScript safety: check if we have paymentIntent and it has succeeded
      if (result.paymentIntent?.status === 'succeeded') {
        // Payment succeeded!
        onSuccess(result.paymentIntent.id);
      } else {
        // Payment status is unknown
        throw new Error('Payment status unknown. Please contact support.');
      }
    } catch (err: unknown) {
      const error = err as Error;
      onError(error.message || 'Payment processing failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Card Details */}
      <div className="mt-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Card Details
        </label>
        <div className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md p-3 shadow-inner">
          <PaymentElement />
        </div>
      </div>
      
      {/* ZIP Code Input */}
      <div>
        <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Billing Postal Code
        </label>
        <input
          id="zipCode"
          type="text"
          value={zipCode}
          onChange={(e) => setZipCode(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="90210"
          disabled={isLoading}
          required
        />
      </div>
      
      {/* Terms Agreement Checkbox */}
      <div className="flex items-start space-x-2">
        <input
          id="terms"
          name="terms"
          type="checkbox"
          checked={agreedToTerms}
          onChange={(e) => setAgreedToTerms(e.target.checked)}
          className="h-4 w-4 mt-1 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label htmlFor="terms" className="text-xs text-gray-500 dark:text-gray-400">
          I agree to the <a href="/terms-of-use" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Terms of Service</a> and <a href="/privacy-policy" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a>.
        </label>
      </div>
      
      {/* Secure Payment Notice */}
      <div className="mt-4">
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
          Your payment information is stored securely by Stripe.
        </p>
      </div>

      <div className="flex justify-end mt-6">
        <Button
          onClick={handleSubmit}
          disabled={isLoading || !formComplete || !stripe || !elements}
          isLoading={isLoading}
        >
          Pay $5.00
        </Button>
      </div>
    </div>
  );
};

// Success Component
const SuccessScreen = ({
  closeTimer
}: {
  closeTimer: number;
}) => {
  return (
    <div className="text-center py-8">
      <div className="mb-4 flex justify-center">
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
          <svg className="w-10 h-10 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      </div>
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
        Payment Successful!
      </h2>
      <p className="text-gray-600 dark:text-gray-300 mb-6">
        Your payment was successful.<br />
        Your search is now processing.
      </p>
      <div className="w-full h-2 bg-blue-100 dark:bg-blue-900/30 rounded-full overflow-hidden">
        <motion.div 
          className="h-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]"
          initial={{ width: "0%" }}
          animate={{ width: "100%" }}
          transition={{ duration: closeTimer }}
        />
      </div>
    </div>
  );
};

// Main Modal Component
interface AuthenticatedPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (paymentIntentId: string) => void;
  error?: string | null;
}

export default function AuthenticatedPaymentModal({
  isOpen,
  onClose,
  onSuccess,
  error: initialError = null
}: AuthenticatedPaymentModalProps) {
  // Payment info
  const [clientSecret, setClientSecret] = useState<string>('');
  const [currentStep, setCurrentStep] = useState<'payment' | 'success'>('payment');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(initialError);
  const [closeTimer, setCloseTimer] = useState(3); // 3 seconds countdown
  const [paymentIntentId, setPaymentIntentId] = useState('');
  const [isCreatingPaymentIntent, setIsCreatingPaymentIntent] = useState(false);

  // Reset error when parent error changes & handle modal close
  useEffect(() => {
    setError(initialError);
    
    // Reset modal state when it closes
    if (!isOpen) {
      setCurrentStep('payment');
      setClientSecret('');
      setError(null);
    }
  }, [initialError, isOpen]);

  // Create payment intent when modal opens
  useEffect(() => {
    if (isOpen && !clientSecret) {
      createPaymentIntent();
    }
  }, [isOpen, clientSecret]);
  
  // Success timer for success step
  useEffect(() => {
    if (currentStep === 'success') {
      // Trigger confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
      
      const timer = setInterval(() => {
        setCloseTimer((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            // Pass the payment intent ID to callback
            onSuccess(paymentIntentId);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      return () => clearInterval(timer);
    }
  }, [currentStep, paymentIntentId, onSuccess]);

  // Create payment intent
  const createPaymentIntent = async () => {
    try {
      setIsCreatingPaymentIntent(true);
      setError(null);

      // Create payment intent for authenticated user search ($5.00)
      const response = await fetch('/api/core?action=create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          context: 'authenticated', // Important: indicates this is for an authenticated user
          service: 'FaceTrace Search',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to prepare payment');
      }

      const data = await response.json();
      
      if (!data.clientSecret) {
        throw new Error('Payment preparation failed. Missing client secret.');
      }
      
      setClientSecret(data.clientSecret);
    } catch (err: any) {
      console.error("Error creating payment intent:", err);
      setError(err.message || "Failed to prepare payment form");
    } finally {
      setIsCreatingPaymentIntent(false);
    }
  };
  
  // Handle payment success
  const handlePaymentSuccess = (paymentIntentId: string) => {
    setPaymentIntentId(paymentIntentId);
    setCurrentStep('success');
  };
  
  // Handle payment error
  const handlePaymentError = (message: string) => {
    setError(message);
  };

  // Options for Stripe Elements appearance
  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#92A5FF',
      colorBackground: '#ffffff',
      colorText: '#30313d',
      colorDanger: '#df1b41',
      fontFamily: 'Inter, system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '8px',
    },
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
      >
        {/* Close button - don't allow closing during processing */}
        {currentStep !== 'success' ? (
          <button
            onClick={() => {
              if (!isLoading && !isCreatingPaymentIntent) onClose();
            }}
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
            disabled={isLoading || isCreatingPaymentIntent}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        ) : (
          <div className="absolute top-3 right-3 flex items-center justify-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full w-8 h-8 text-sm font-semibold">
            {closeTimer}
          </div>
        )}

        {/* Header */}
        <div className="text-center mb-6">
          <div className="h-12 w-12 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1">Complete Your Search</h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">
            One-time Payment
          </p>
          <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
            $5.00
          </p>
        </div>

        {/* Progress indicator */}
        <div className="flex mb-6 w-full max-w-xs mx-auto">
          <div className={`flex-1 h-1 ${currentStep === 'payment' ? 'bg-blue-500' : 'bg-blue-200 dark:bg-blue-900'}`}></div>
          <div className={`flex-1 h-1 ${currentStep === 'success' ? 'bg-blue-500' : 'bg-blue-200 dark:bg-blue-900'}`}></div>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Form Steps */}
        <AnimatePresence mode="wait">
          {currentStep === 'payment' && (
            <motion.div
              key="payment"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {clientSecret ? (
                <div className="client-only-stripe-wrapper">
                  <StripeElements clientSecret={clientSecret}>
                    <PaymentForm
                      onBack={() => onClose()}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                      isLoading={isLoading}
                      setIsLoading={setIsLoading}
                    />
                  </StripeElements>
                </div>
              ) : (
                <div className="flex justify-center items-center py-10">
                  <svg className="animate-spin h-8 w-8 text-[#92A5FF]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              )}
            </motion.div>
          )}
          
          {currentStep === 'success' && (
            <motion.div
              key="success"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SuccessScreen
                closeTimer={closeTimer}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Security notice */}
        <div className="flex items-center justify-center mt-6 text-xs text-gray-500 dark:text-gray-400">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          Secure payment processed by Stripe
        </div>
      </motion.div>
    </div>
  );
}
