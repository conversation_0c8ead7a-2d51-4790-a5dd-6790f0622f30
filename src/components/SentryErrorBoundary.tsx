import { ErrorBoundary } from '@sentry/nextjs';
import React from 'react';

interface ErrorFallbackProps {
  error: Error;
  componentStack: string | null;
  eventId: string | null;
  resetError(): void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  componentStack, 
  eventId, 
  resetError 
}) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-6 text-center">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
      <p className="mb-2">We've been notified of the issue and we're working on a fix.</p>
      {eventId && (
        <p className="text-sm text-gray-500 mb-4">
          Error ID: {eventId}
        </p>
      )}
      <button
        onClick={resetError}
        className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
      >
        Try again
      </button>
    </div>
  );
};

export const SentryErrorBoundary: React.FC<{children: React.ReactNode}> = ({ children }) => {
  return (
    <ErrorBoundary fallback={ErrorFallback}>
      {children}
    </ErrorBoundary>
  );
};

export default SentryErrorBoundary;
