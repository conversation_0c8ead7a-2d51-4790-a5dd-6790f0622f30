'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/app/components/Button';

interface AuthenticatedFlowProps {
  onRunWithCredit: () => void;
  onRunWithPayment: () => void;
  isDisabled: boolean;
}

export default function AuthenticatedFlow({
  onRunWithCredit,
  onRunWithPayment,
  isDisabled = false
}: AuthenticatedFlowProps) {
  // State for user tokens
  const [tokens, setTokens] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user token information
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        console.log("AuthenticatedFlow: Fetching user tokens...");
        setIsLoading(true);
        setError(null);
        
        // Call the API to get user data (including tokens)
        const response = await fetch('/api/data?action=user-data', {
          method: 'GET',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user tokens');
        }

        const data = await response.json();
        console.log("AuthenticatedFlow: API response:", data);
        
        // User tokens might be stored as 'tokens' in the API
        setTokens(data.tokens || 0);
        console.log("AuthenticatedFlow: Set tokens to", data.tokens || 0);
      } catch (err: any) {
        console.error('Error fetching user tokens:', err);
        setError(err.message || 'Failed to load token information');
        setTokens(0); // Assume no tokens on error for a safer fallback
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center space-y-4 mt-6">
        <div className="flex justify-center w-full max-w-md">
          <Button disabled className="relative z-10 px-8 py-3 rounded-full text-white transition-all duration-300 text-lg font-bold shadow-xl flex-1 bg-gray-400 cursor-wait opacity-70">
            <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading Account Info...
          </Button>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center space-y-4 mt-6">
        <div className="flex justify-center w-full max-w-md">
          <div className="text-center mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
            Error loading your account information. Please try again later.
          </div>
        </div>
        {/* Fallback to payment option */}
        <div className="flex justify-center space-x-4 w-full max-w-md">
          <Button
            onClick={onRunWithPayment}
            disabled={isDisabled}
            className="relative z-10 px-8 py-3 rounded-full text-white transition-all duration-300 text-lg font-bold shadow-xl flex-1 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] hover:shadow-lg"
          >
            Pay $5 and Search
          </Button>
        </div>
      </div>
    );
  }

  // Debug rendering state
  console.log("AuthenticatedFlow: Rendering with tokens =", tokens);

  return (
    <div className="flex flex-col items-center space-y-4 mt-6">
      {/* Display tokens information if available */}
      {tokens !== null && (
        <div className="text-center bg-blue-50 dark:bg-blue-900/20 p-3 rounded-xl border border-blue-100 dark:border-blue-800/40 text-blue-700 dark:text-blue-300 mb-4">
          <span className="font-bold">
            {tokens > 0 
              ? `You have ${tokens} free ${tokens === 1 ? 'search' : 'searches'} remaining!` 
              : 'You have no free searches left'}
          </span>
        </div>
      )}

      <div className="flex justify-center space-x-4 w-full max-w-md">
        {tokens && tokens > 0 ? (
          // User has tokens - show the "Use Credit" button
          <Button
            onClick={onRunWithCredit}
            disabled={isDisabled}
            className="relative z-10 px-8 py-3 rounded-full text-white transition-all duration-300 text-lg font-bold shadow-xl flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:shadow-lg"
          >
            Use 1 Free Search
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {tokens}
            </div>
          </Button>
        ) : (
          // User has no tokens - show payment button
          <Button
            onClick={onRunWithPayment}
            disabled={isDisabled}
            className="relative z-10 px-8 py-3 rounded-full text-white transition-all duration-300 text-lg font-bold shadow-xl flex-1 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] hover:shadow-lg"
          >
            Pay $5 and Search
          </Button>
        )}
      </div>
    </div>
  );
}
