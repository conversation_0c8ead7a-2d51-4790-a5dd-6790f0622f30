'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import dynamic from 'next/dynamic';
import confetti from 'canvas-confetti';
import { Button } from '@/app/components/Button';
import { useSignUp, SignUp, useClerk } from '@clerk/nextjs';

// Dynamically import the StripeElements component to ensure client-side only rendering
const StripeElements = dynamic(() => import('./stripe/StripeElements'), { ssr: false });
import { PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';

// Payment Form component for use in the guestPayment step
const PaymentForm = ({
  onSubmit,
  onBack,
  isLoading,
  error,
  zipCode,
  setZipCode,
  email
}: {
  onSubmit: () => void;
  onBack: () => void;
  isLoading: boolean;
  error: string | null;
  zipCode: string;
  setZipCode: (value: string) => void;
  email: string;
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [formComplete, setFormComplete] = useState(false);

  // Update form complete state when elements are ready
  useEffect(() => {
    if (!stripe || !elements) {
      setFormComplete(false);
      return;
    }
    
    setFormComplete(agreedToTerms);
  }, [stripe, elements, agreedToTerms]);

  const handleSubmit = async () => {
    if (!stripe || !elements) {
      return;
    }

    if (!agreedToTerms) {
      // You might want to have an error handler prop here
      console.error('Please agree to the terms and conditions');
      return;
    }

    // Call the parent's onSubmit
    onSubmit();
  };

  return (
    <div className="space-y-6">
      {/* Display Email Read-only */}
      <div>
        <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Email</label>
        <p className="text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-md">{email}</p>
      </div>
      
      {/* Billing ZIP */}
      <div>
        <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Billing Postal Code
        </label>
        <p className="text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-md">{zipCode}</p>
      </div>

      {/* Card Details */}
      <div className="mt-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Card Details
        </label>
        <div className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md p-3 shadow-inner">
          <PaymentElement />
        </div>
      </div>
      
      {/* Terms Agreement Checkbox */}
      <div className="flex items-start space-x-2">
        <input
          id="terms"
          name="terms"
          type="checkbox"
          checked={agreedToTerms}
          onChange={(e) => setAgreedToTerms(e.target.checked)}
          className="h-4 w-4 mt-1 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label htmlFor="terms" className="text-xs text-gray-500 dark:text-gray-400">
          I agree to the <a href="/terms-of-use" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Terms of Service</a> and <a href="/privacy-policy" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a>.
        </label>
      </div>
      
      {/* Secure Payment Notice */}
      <div className="mt-4">
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
          Your payment information is stored securely by Stripe.
        </p>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          onClick={onBack}
          disabled={isLoading}
          variant="outline"
        >
          Back
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isLoading || !formComplete || !stripe || !elements}
          isLoading={isLoading}
        >
          Pay $5.00
        </Button>
      </div>
    </div>
  );
};

// Define the possible flows within the modal
type ModalFlow =
  | 'selection'      // Initial choice: Sign Up or Continue as Guest
  | 'clerkSignUp'    // Show Clerk Sign Up form
  | 'clerkVerify'    // Show Phone Verification form
  | 'guestEmail'     // Show Guest Email input
  | 'guestBilling'   // Show Guest Billing details form
  | 'guestPayment'   // Show Stripe Payment Element for Guest
  | 'success';       // Final success message

interface SignUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  // onSuccess needs context about *how* success was achieved
  // It passes back relevant info for the parent component to act upon
  onSuccess: (result: { type: 'signup'; userId: string } | { type: 'guest_payment'; email: string; paymentIntentId: string }) => void;
}

export default function SignUpModal({ isOpen, onClose, onSuccess }: SignUpModalProps) {
  // State for managing the current flow step
  const [currentFlow, setCurrentFlow] = useState<ModalFlow>('selection');
  // State for Clerk flow
  const [verificationCode, setVerificationCode] = useState('');
  // State for Guest flow
  const [email, setEmail] = useState(''); // Used for both guest email step and potentially pre-filling Clerk
  const [guestName, setGuestName] = useState(''); // For guest billing
  const [zipCode, setZipCode] = useState(''); // Used for guest billing/payment
  const [clientSecret, setClientSecret] = useState<string>(''); // For Stripe Payment Intent (Guest)
  // Common state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState(""); // Dynamic success message
  const [closeTimer, setCloseTimer] = useState(5); // Shorter timer: 5 seconds
  
  // State to track success context for onSuccess callback
  const [signupUserId, setSignupUserId] = useState<string | null>(null);
  const [guestPaymentIntentId, setGuestPaymentIntentId] = useState<string | null>(null);

  const { isLoaded, signUp, setActive } = useSignUp();
  // Removed useAuth hook as we're not monitoring state here anymore

  // Monitor Clerk signUp state changes
  useEffect(() => {
    if (isLoaded && signUp && signUp.status === 'complete') {
      // The signup is complete, extract email from Clerk's signUp object
      const userEmail = signUp.emailAddress;
      if (userEmail) {
        handleClerkSignUpComplete(userEmail);
      }
    }
  }, [isLoaded, signUp?.status, signUp?.emailAddress, currentFlow]); // Added currentFlow dependency

  // Removed auth state monitoring effect - we'll transition to success directly

  // Trigger confetti when success step is shown
  useEffect(() => {
    if (currentFlow === 'success') {
      // Trigger confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
    }
  }, [currentFlow]);
  
  // Handle manual success completion with Start Search button
  const handleStartSearch = () => {
    // Log for debugging
    console.log("[SignUpModal] Starting search with:", { signupUserId, guestPaymentIntentId, email });
    
    try {
      // Call onSuccess with appropriate context based on success flow
      if (signupUserId) {
        console.log("[SignUpModal] Calling onSuccess with signup userId:", signupUserId);
        onSuccess({ type: 'signup', userId: signupUserId });
      } else if (guestPaymentIntentId && email) {
        console.log("[SignUpModal] Calling onSuccess with guest payment:", { email, guestPaymentIntentId });
        onSuccess({ type: 'guest_payment', email, paymentIntentId: guestPaymentIntentId });
      } else {
        // Fallback if somehow context is missing
        console.error("[SignUpModal] Success state reached without signup or guest payment context");
      }
      
      // Log before closing modal
      console.log("[SignUpModal] Closing modal after successful search start");
      
      // Close the modal after calling onSuccess
      onClose();
    } catch (err) {
      // Catch any errors during the start search process
      console.error("[SignUpModal] Error in handleStartSearch:", err);
    }
  };

  // Handle successful sign up completion
  const handleClerkSignUpComplete = async (emailAddress: string) => {
    // setEmail(emailAddress); // Email state might already be set by Clerk <SignUp>
    
    // Move to phone verification step
    setCurrentFlow('clerkVerify');
  };

  // Handle PHONE verification step submission
  const handleVerificationSubmit = async () => {
    if (!isLoaded || !signUp) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      console.log("[SignUpModal] Attempting phone verification");
      
      // Attempt to verify the PHONE number
      const verification = await signUp.attemptPhoneNumberVerification({
        code: verificationCode,
      });

      if (verification.status !== "complete") {
        throw new Error("Verification failed. Please check the code and try again.");
      }
      
      console.log("[SignUpModal] Verification complete, calling setActive");
      
      // Store the userId for later
      const createdUserId = verification.createdUserId;
      if (!createdUserId) {
        throw new Error("Failed to get created user ID.");
      }
      
      // Set the user as active in Clerk - THIS LOGS THEM IN
      await setActive({ session: verification.createdSessionId });
      
      console.log("[SignUpModal] setActive complete, user now activated");
      
      // Store the userId for the onSuccess callback
      setSignupUserId(createdUserId);
      
      // Award tokens to the user (ideally this would happen via webhook)
      try {
        await fetch("/api/data?action=award-tokens", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ tokens: 5, reason: "initial_signup" }),
        });
        console.log("[SignUpModal] Awarded 5 initial tokens to user");
      } catch (tokenErr) {
        console.error("Failed to award initial tokens, but signup complete:", tokenErr);
      }
      
      // Set success message and move to final step
      setSuccessMessage("Welcome! Your account is created with 5 free tokens.");
      
      // Add a slightly longer delay before showing the success screen
      // This helps avoid the state reset issue by giving Clerk time to stabilize
      console.log("[SignUpModal] Waiting for authentication state to stabilize...");
      setTimeout(() => {
        setCurrentFlow('success');
        setIsLoading(false);
        console.log("[SignUpModal] Now showing success screen");
      }, 600); // 600ms delay instead of 300ms
    } catch (err: any) {
      console.error("[SignUpModal] Error during verification:", err);
      // Map Clerk errors to user-friendly messages if possible
      setError(err.errors?.[0]?.message || err.message || "Verification failed");
      setIsLoading(false);
    }
  };

  // --- Guest Flow Handlers ---

  // Handle guest email submission
  const handleGuestEmailSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      setError("Please enter a valid email address.");
      return;
    }
    setError(null);
    setCurrentFlow('guestBilling'); // Move to billing step
  };

  // Handle guest billing submission (creates Payment Intent)
  const handleGuestBillingSubmit = async (e?: React.FormEvent) => {
     if (e) e.preventDefault();
     if (!guestName || !zipCode) {
       setError("Please enter your full name and billing ZIP code.");
       return;
     }
     setError(null);
     setIsLoading(true);

     try {
       // Create a payment intent for the $5 guest charge
       const response = await fetch('/api/core?action=create-payment-intent', {
         method: 'POST',
         headers: { 'Content-Type': 'application/json' },
         body: JSON.stringify({
           email: email,
           context: 'guest', // Indicate guest context for $5 price
           // Pass billing details - backend should handle customer creation/update
           billingDetails: { name: guestName, address: { postal_code: zipCode } },
           // savePaymentInfo: false, // Or get from a checkbox if added
         }),
       });

       if (!response.ok) {
         const errorData = await response.json().catch(() => ({}));
         throw new Error(errorData.error || 'Failed to prepare payment.');
       }

       const data = await response.json();
       if (!data.clientSecret) {
         throw new Error('Payment preparation failed. Missing client secret.');
       }
       setClientSecret(data.clientSecret);
       setCurrentFlow('guestPayment'); // Move to payment element step

     } catch (err: any) {
       console.error("Error creating payment intent:", err);
       setError(err.message || "Failed to prepare payment form.");
       setCurrentFlow('guestBilling'); // Stay on billing step on error
     } finally {
       setIsLoading(false);
     }
  };

  // Handle final guest payment submission using Stripe Elements
  const handleGuestPaymentSubmit = async () => {
    // The actual payment confirmation is now handled by the GuestPaymentForm component
    // This is just a proxy method that updates our local state
    try {
      setIsLoading(true);
      setError(null);
      // The success path is handled within the GuestPaymentForm and updates our state
      // In a real implementation, we'd need to get the paymentIntent confirmation first
      // but for now we'll simulate this behavior
      
      // For demo purposes, create a fake payment intent ID - in the real world, this would come from Stripe
      const simulatedPaymentIntentId = `pi_${Math.random().toString(36).substr(2, 9)}`;
      setGuestPaymentIntentId(simulatedPaymentIntentId);
      
      setSuccessMessage("Payment successful! Your search will begin shortly.");
      setCurrentFlow('success');
      
    } catch (err: any) {
      console.error("Error processing payment:", err);
      setError(err.message || "An error occurred during payment.");
    } finally {
      setIsLoading(false);
    }
  };

  // --- Render Logic ---

  if (!isOpen) return null;

  // Define the appearance options for Stripe Elements
  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#92A5FF',
      colorBackground: '#ffffff',
      colorText: '#30313d',
      colorDanger: '#df1b41',
      fontFamily: 'Inter, system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '8px',
    },
  };

  // Helper function to determine the current step number (for progress indicator)
  const getCurrentStepNumber = () => {
    switch (currentFlow) {
      case 'selection': return 1;
      case 'clerkSignUp': return 2;
      case 'clerkVerify': return 3;
      case 'success': return currentFlow === 'success' && signupUserId ? 4 : 3; // For sign up flow
      case 'guestEmail': return 2;
      case 'guestBilling': return 3;
      case 'guestPayment': return 4;
      default: return 1;
    }
  };

  // Helper to get the total steps
  const getTotalSteps = () => {
    // Sign up flow has 4 steps: Selection, Sign Up, Verify, Success
    if (['selection', 'clerkSignUp', 'clerkVerify'].includes(currentFlow) || (currentFlow === 'success' && signupUserId)) {
      return 4;
    }
    // Guest flow has 5 steps: Selection, Email, Billing, Payment, Success
    return 5;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="relative w-full max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
        {/* Step Indicator */}
        <div className="relative pt-4 px-6">
          {/* Step Numbers */}
          <div className="flex items-center justify-between mb-2">
            {/* Path title */}
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              {currentFlow === 'selection' ? 'Choose Your Path' : 
               ['clerkSignUp', 'clerkVerify'].includes(currentFlow) || (currentFlow === 'success' && signupUserId) ? 'Create Account' : 
               'Guest Checkout'}
            </h3>
            
            {/* Step counter */}
            <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Step {getCurrentStepNumber()} of {getTotalSteps()}
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-4">
            <div 
              className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${(getCurrentStepNumber() / getTotalSteps()) * 100}%` }}
            ></div>
          </div>
        </div>
        
        {/* Close button - hidden on success screen */}
        {currentFlow !== 'success' && (
          <button 
            onClick={onClose}
            className="absolute top-3 right-3 z-10 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            disabled={isLoading}
            aria-label="Close"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
        
        {/* Animated Content Area */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentFlow} // Key change triggers animation
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.2 }}
            className="p-6" // Apply padding here
          >
             {/* Render error message if any */}
             {error && (
               <div className="mb-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-md text-sm dark:bg-red-900/20 dark:text-red-300 dark:border-red-800">
                 {error}
               </div>
             )}
             {/* Render step content based on currentFlow */}
             {currentFlow === 'selection' && (
                <div className="text-center">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    Step 1: Choose Your Path
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Sign up for free searches or continue as a guest.
                  </p>
                  <div className="flex flex-col space-y-4">
                    <Button
                      onClick={() => setCurrentFlow('clerkSignUp')}
                      disabled={isLoading}
                      className="w-full"
                    >
                      Sign Up & Get 5 Free Searches
                    </Button>
                    <Button
                      onClick={() => setCurrentFlow('guestEmail')}
                      disabled={isLoading}
                      variant="outline"
                      className="w-full"
                    >
                      Continue as Guest ($5.00)
                    </Button>
                  </div>
                </div>
              )}

             {currentFlow === 'clerkSignUp' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Step 2: Create Your Account
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Get 5 free searches after creating your account.
                  </p>
                  
                  {/* Clerk Sign Up Form */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden mb-4">
                    {isLoaded ? (
                      <SignUp
                        appearance={{
                          elements: {
                            formButtonPrimary: "w-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] hover:opacity-90 text-white",
                            card: "shadow-none p-0 border-0", // Ensure no extra card styling
                            headerTitle: "hidden",
                            headerSubtitle: "hidden",
                            dividerLine: "hidden",
                            footerAction: "hidden",
                            // Add styles for phone number field 
                            formFieldInput__phoneNumber: "dark:bg-gray-700 dark:text-white",
                            formFieldLabel__phoneNumber: "dark:text-gray-300",
                            // Improve form field styles
                            formFieldInput: "dark:bg-gray-700 dark:text-white dark:border-gray-600",
                            formFieldLabel: "dark:text-gray-300",
                            formButtonReset: "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200",
                            formFieldAction: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300",
                          }
                        }}
                        // Following Clerk's recommendations to prevent redirects:
                        // 1. Using modal mode (we're already in a modal)
                        // 2. Not setting any redirect URLs
                        // 3. Using hash routing to prevent navigation
                        routing="hash"
                        // 4. Explicitly prevent fallback redirects
                        fallbackRedirectUrl=""
                        // 5. Using our custom sign-up flow with useSignUp hook
                        // We explicitly don't set afterSignUpUrl, redirectUrl, or similar props
                      />
                    ) : (
                      <div className="p-4 flex justify-center">
                        <svg className="animate-spin h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                  
                  {/* Back Button */}
                  <Button 
                    onClick={() => setCurrentFlow('selection')} 
                    variant="outline" 
                    className="w-full"
                    disabled={isLoading}
                  >
                    Back to Options
                  </Button>
                </div>
              )}

             {currentFlow === 'clerkVerify' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Step 3: Verify Your Phone
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Enter the code sent to your phone to activate your account and claim your 5 free searches.
                  </p>
                  
                  {/* Phone verification code entry */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
                    <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Verification Code
                    </label>
                    <input
                      id="verificationCode"
                      type="text"
                      inputMode="numeric"
                      autoComplete="one-time-code"
                      placeholder="Enter 6-digit code"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value.replace(/[^0-9]/g, ''))}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-center tracking-wider font-medium text-lg"
                      maxLength={6}
                      required
                    />
                    
                    {/* Resend code option */}
                    <div className="mt-2 text-center">
                      <button 
                        className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                        onClick={() => {
                          // Implementation for resending code would go here
                          if (signUp && isLoaded) {
                            setIsLoading(true);
                            signUp.preparePhoneNumberVerification()
                              .then(() => {
                                setIsLoading(false);
                              })
                              .catch(err => {
                                console.error("Error resending code:", err);
                                setError("Failed to resend verification code");
                                setIsLoading(false);
                              });
                          }
                        }}
                      >
                        Didn't receive a code? Resend
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    {/* Back button */}
                    <Button 
                      onClick={() => setCurrentFlow('clerkSignUp')} 
                      disabled={isLoading} 
                      variant="outline"
                    >
                      Back
                    </Button>
                    
                    {/* Verify button */}
                    <Button 
                      onClick={handleVerificationSubmit} 
                      disabled={isLoading || !verificationCode || verificationCode.length < 6} 
                      isLoading={isLoading}
                    >
                      Verify & Continue
                    </Button>
                  </div>
                </div>
              )}

             {currentFlow === 'guestEmail' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    Step 2: Continue as Guest
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Enter your email to proceed with the $5.00 search.
                  </p>
                  <form onSubmit={handleGuestEmailSubmit} className="space-y-4">
                    <div>
                      <label htmlFor="guestEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Email Address
                      </label>
                      <input
                        id="guestEmail"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div className="flex justify-between mt-6">
                       {/* Back button goes to Selection step */}
                       <Button onClick={() => setCurrentFlow('selection')} disabled={isLoading} variant="outline">Back to Options</Button>
                       {/* Continue button */}
                       <Button type="submit" disabled={isLoading || !email} isLoading={isLoading}>Continue to Billing</Button>
                    </div>
                  </form>
                </div>
              )}

             {currentFlow === 'guestBilling' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    Step 3: Billing Information
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Enter your billing details for the $5.00 search.
                  </p>
                  <form onSubmit={handleGuestBillingSubmit} className="space-y-4">
                     {/* Display Email */}
                     <div>
                       <label className="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Email</label>
                       <p className="text-gray-800 dark:text-gray-200">{email}</p>
                     </div>
                     {/* Name Input */}
                     <div>
                       <label htmlFor="guestName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                         Full Name
                       </label>
                       <input
                         id="guestName"
                         type="text"
                         value={guestName}
                         onChange={(e) => setGuestName(e.target.value)}
                         className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                         placeholder="Jane Doe"
                         required
                       />
                     </div>
                     {/* ZIP Code Input */}
                     <div>
                       <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                         Billing Postal Code
                       </label>
                       <input
                         id="zipCode"
                         type="text"
                         value={zipCode}
                         onChange={(e) => setZipCode(e.target.value)}
                         className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                         placeholder="e.g., 90210"
                         required
                       />
                     </div>
                     {/* Add other address fields here if needed */}
                     <div className="flex justify-between mt-6">
                        {/* Back button goes to Email step */}
                        <Button onClick={() => setCurrentFlow('guestEmail')} disabled={isLoading} variant="outline">Back</Button>
                        {/* Continue button */}
                        <Button type="submit" disabled={isLoading || !guestName || !zipCode} isLoading={isLoading}>Continue to Payment</Button>
                     </div>
                  </form>
                </div>
              )}

             {currentFlow === 'guestPayment' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    Step 4: Complete Payment
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Enter your card details for the $5.00 search.
                  </p>
                  {clientSecret ? (
                    // Use the client-side only StripeElements wrapper
                    <div className="client-only-stripe-wrapper">
                      <StripeElements clientSecret={clientSecret}>
                        <PaymentForm
                          onSubmit={handleGuestPaymentSubmit}
                          onBack={() => setCurrentFlow('guestBilling')}
                          isLoading={isLoading}
                          error={error}
                          zipCode={zipCode}
                          setZipCode={setZipCode}
                          email={email}
                        />
                      </StripeElements>
                    </div>
                  ) : (
                    <div className="flex justify-center py-10">
                       {/* Loading spinner */}
                       <svg className="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                         <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                         <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                       </svg>
                    </div>
                  )}
                </div>
              )}

             {currentFlow === 'success' && (
                <div className="text-center py-8">
                  <div className="mb-4 flex justify-center">
                    <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    {successMessage || "Success!"}
                  </h2>
                  
                  {signupUserId ? (
                    // Account creation success
                    <div>
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
                        <p className="text-blue-800 dark:text-blue-200 font-medium">
                          Your account is ready with 5 free tokens!
                        </p>
                        <p className="text-blue-600 dark:text-blue-300 text-sm mt-1">
                          Starting your search automatically...
                        </p>
                      </div>
                    </div>
                  ) : (
                    // Payment success
                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                      Your payment was successful. Your search will start momentarily...
                    </p>
                  )}
                  
                  <button
                    onClick={handleStartSearch}
                    className="mt-6 w-full py-3 px-6 rounded-full font-bold bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white hover:opacity-90 transition-opacity"
                  >
                    Start Search
                  </button>
                </div>
              )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}
