'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// Check if authentication is disabled
const isAuthDisabled = process.env.NEXT_PUBLIC_DISABLE_AUTH === 'true';

// Dynamically import SignUpModal only when auth is enabled
const SignUpModal = dynamic(() => import('./SignUpModal'), { 
  ssr: false,
  loading: () => null
});

interface ConditionalSignUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (result: { type: 'signup'; userId: string } | { type: 'guest_payment'; email: string; paymentIntentId: string }) => void;
}

export default function ConditionalSignUpModal({ isOpen, onClose, onSuccess }: ConditionalSignUpModalProps) {
  // If authentication is disabled, don't render the modal at all
  if (isAuthDisabled) {
    console.log('[ConditionalSignUpModal] Authentication disabled - not rendering SignUpModal');
    return null;
  }

  // If authentication is enabled, render the SignUpModal
  return (
    <SignUpModal
      isOpen={isOpen}
      onClose={onClose}
      onSuccess={onSuccess}
    />
  );
}
