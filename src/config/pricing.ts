// Centralized pricing configuration for FaceTrace
export const PRICING_CONFIG = {
  // Report unlock pricing
  REPORT_UNLOCK_PRICE: 5.00,
  REPORT_UNLOCK_PRICE_CENTS: 500, // For Stripe (in cents)
  
  // Currency
  CURRENCY: 'USD',
  CURRENCY_SYMBOL: '$',
  
  // Token system
  NEW_USER_FREE_TOKENS: 5,
  TOKEN_VALUE: 1, // Each token is worth 1 report unlock
  
  // Display helpers
  getFormattedPrice: () => `${PRICING_CONFIG.CURRENCY_SYMBOL}${PRICING_CONFIG.REPORT_UNLOCK_PRICE.toFixed(2)}`,
  getPriceInCents: () => PRICING_CONFIG.REPORT_UNLOCK_PRICE_CENTS,
  
  // Stripe product IDs (if needed)
  STRIPE_PRODUCT_IDS: {
    REPORT_UNLOCK: 'prod_report_unlock_5usd',
  }
} as const;

export default PRICING_CONFIG;
