import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    await import('../sentry.server.config');
  } else if (process.env.NEXT_RUNTIME === 'edge') {
    await import('../sentry.edge.config');
  } else {
    // Browser environment
    await import('../sentry.client.config');
  }
}

export const onRequestError = Sentry.captureRequestError;
