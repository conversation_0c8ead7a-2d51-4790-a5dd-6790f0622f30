'use client';

import { useEffect } from 'react';
import LogRocket from 'logrocket';
import { useUser } from '@clerk/nextjs';

// Check if authentication is disabled
const isAuthDisabled = process.env.NEXT_PUBLIC_DISABLE_AUTH === 'true';

// Define props interface
interface LogRocketProviderProps {
  children: React.ReactNode;
}

// Component that uses <PERSON> hooks (only when auth is enabled)
function LogRocketWithClerk({ children }: LogRocketProviderProps) {
  const { user, isLoaded } = useUser();

  useEffect(() => {
    // Only initialize LogRocket in production
    if (process.env.NODE_ENV === 'production') {
      LogRocket.init('revefv/facetracepro');
    }
  }, []);

  // Identify user after they log in
  useEffect(() => {
    if (isLoaded && user && process.env.NODE_ENV === 'production') {
      // Prepare user data with proper type handling
      const userData: Record<string, string | number | boolean> = {
        name: user.firstName && user.lastName
          ? `${user.firstName} ${user.lastName}`
          : user.firstName || user.username || 'Unknown User',
      };

      // Only add email if it exists
      if (user.primaryEmailAddress?.emailAddress) {
        userData.email = user.primaryEmailAddress.emailAddress;
      }

      LogRocket.identify(user.id, userData);
    }
  }, [isLoaded, user]);

  return <>{children}</>;
}

// Component that doesn't use Clerk hooks (when auth is disabled)
function LogRocketWithoutClerk({ children }: LogRocketProviderProps) {
  useEffect(() => {
    // Only initialize LogRocket in production
    if (process.env.NODE_ENV === 'production') {
      LogRocket.init('revefv/facetracepro');
    }
  }, []);

  return <>{children}</>;
}

// Main provider that conditionally renders based on auth status
export function LogRocketProvider({ children }: LogRocketProviderProps) {
  // TEMPORARY DISABLE: Use different components based on auth status
  if (isAuthDisabled) {
    return <LogRocketWithoutClerk>{children}</LogRocketWithoutClerk>;
  }

  return <LogRocketWithClerk>{children}</LogRocketWithClerk>;
}