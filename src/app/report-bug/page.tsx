'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { Bug, ArrowRight } from "lucide-react";
import { useState } from "react";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function ReportBugPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    accountType: "",
    bugType: "",
    severity: "",
    description: "",
    stepsToReproduce: "",
    expectedBehavior: "",
    actualBehavior: "",
    deviceType: "",
    operatingSystem: "",
    browser: "",
    screenResolution: "",
    dateOccurred: "",
    timeOccurred: "",
    additionalInfo: ""
  });
  const [files, setFiles] = useState<FileList | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(e.target.files);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Show success message
      alert("Bug report submitted successfully! We'll get back to you within 24 business hours.");
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        accountType: "",
        bugType: "",
        severity: "",
        description: "",
        stepsToReproduce: "",
        expectedBehavior: "",
        actualBehavior: "",
        deviceType: "",
        operatingSystem: "",
        browser: "",
        screenResolution: "",
        dateOccurred: "",
        timeOccurred: "",
        additionalInfo: ""
      });
      setFiles(null);
      
      // Reset file input
      const fileInput = document.getElementById('screenshot-upload') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      
    } catch (error) {
      alert("Error sending message. Please try again or email us <NAME_EMAIL>");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="max-w-3xl mx-auto"
          >
            <motion.div 
              variants={itemVariants}
              className="flex items-center justify-center mb-8"
            >
              <Bug className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
              <h1 className="text-3xl font-bold text-[#1d3b6c] dark:text-white">Report a Bug</h1>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
            >
              <div className="p-6 bg-blue-100/50 dark:bg-blue-900/30 border-b border-blue-200 dark:border-blue-700">
                <p className="text-blue-800 dark:text-blue-300">
                  We strive to provide a seamless and error-free experience. Your feedback about any issues you encounter is invaluable in helping us improve our service. Please use this form to report bugs or technical problems.
                </p>
              </div>
              
              <div className="p-6">
                <form className="space-y-6" onSubmit={handleSubmit}>
                  <div className="bg-blue-50/30 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800">
                    <h2 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Your Information</h2>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Name</label>
                        <input 
                          type="text" 
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                          required
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Email Address</label>
                        <input 
                          type="email" 
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                          required
                        />
                      </div>
                      
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Account Type (if applicable)</label>
                        <select 
                          name="accountType"
                          value={formData.accountType}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                        >
                          <option value="">Select account type</option>
                          <option value="free">Free User</option>
                          <option value="openplus">Open Plus Subscriber</option>
                          <option value="protect">PROtect Subscriber</option>
                          <option value="advanced">Advanced Subscriber</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50/30 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800">
                    <h2 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Bug Details</h2>
                    
                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Bug Type</label>
                        <select 
                          name="bugType"
                          value={formData.bugType}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                          required
                        >
                          <option value="">Select bug type</option>
                          <option value="search">Search functionality issue</option>
                          <option value="account">Account/login problem</option>
                          <option value="payment">Payment processing error</option>
                          <option value="results">Results display issue</option>
                          <option value="performance">Website performance problem</option>
                          <option value="mobile">Mobile responsiveness issue</option>
                          <option value="privacy">Privacy/security concern</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Bug Severity</label>
                        <select 
                          name="severity"
                          value={formData.severity}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                          required
                        >
                          <option value="">Select severity</option>
                          <option value="critical">Critical (service completely unusable)</option>
                          <option value="major">Major (significant feature broken)</option>
                          <option value="moderate">Moderate (feature partially working)</option>
                          <option value="minor">Minor (inconvenient but workable)</option>
                          <option value="cosmetic">Cosmetic (visual issue only)</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Description of the Issue</label>
                        <textarea 
                          name="description"
                          value={formData.description}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                          rows={4}
                          placeholder="Please provide a detailed description of what happened..."
                          required
                        ></textarea>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Steps to Reproduce</label>
                        <textarea 
                          name="stepsToReproduce"
                          value={formData.stepsToReproduce}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                          rows={4}
                          placeholder="Please list the exact steps someone would take to encounter this bug... 
1. 
2. 
3."
                          required
                        ></textarea>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Expected Behavior</label>
                          <textarea 
                            name="expectedBehavior"
                            value={formData.expectedBehavior}
                            onChange={handleChange}
                            className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                            rows={3}
                            placeholder="What did you expect to happen?"
                            required
                          ></textarea>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Actual Behavior</label>
                          <textarea 
                            name="actualBehavior"
                            value={formData.actualBehavior}
                            onChange={handleChange}
                            className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                            rows={3}
                            placeholder="What actually happened?"
                            required
                          ></textarea>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Screenshots/Videos</label>
                        <div className="border-2 border-dashed border-blue-300 dark:border-blue-800 rounded-md p-4 text-center">
                          <input 
                            type="file" 
                            className="hidden" 
                            id="screenshot-upload" 
                            name="files"
                            accept="image/*, video/*" 
                            multiple 
                            onChange={handleFileChange}
                          />
                          <label 
                            htmlFor="screenshot-upload"
                            className="cursor-pointer flex flex-col items-center justify-center"
                          >
                            <svg 
                              className="h-10 w-10 text-blue-500 dark:text-blue-400 mb-2" 
                              fill="none" 
                              stroke="currentColor" 
                              viewBox="0 0 24 24"
                            >
                              <path 
                                strokeLinecap="round" 
                                strokeLinejoin="round" 
                                strokeWidth={2} 
                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" 
                              />
                            </svg>
                            <span className="text-sm text-blue-700 dark:text-blue-400">Drag and drop files here, or click to select files</span>
                            <span className="text-xs text-blue-500 dark:text-blue-500 mt-1">Max file size: 10MB. Accepted formats: JPG, PNG, GIF, MP4</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50/30 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800">
                    <h2 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Device Information</h2>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Device Type</label>
                        <input 
                          type="text" 
                          name="deviceType"
                          value={formData.deviceType}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                          placeholder="e.g., iPhone 13, MacBook Pro, Windows PC" 
                          required
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Operating System</label>
                        <input 
                          type="text" 
                          name="operatingSystem"
                          value={formData.operatingSystem}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                          placeholder="e.g., iOS 15, macOS Monterey, Windows 11" 
                          required
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Browser Name and Version</label>
                        <input 
                          type="text" 
                          name="browser"
                          value={formData.browser}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                          placeholder="e.g., Chrome 98, Safari 15, Firefox 97" 
                          required
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Screen Resolution (if known)</label>
                        <input 
                          type="text" 
                          name="screenResolution"
                          value={formData.screenResolution}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                          placeholder="e.g., 1920x1080, 2560x1440" 
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50/30 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800">
                    <h2 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Additional Information</h2>
                    
                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">When Did This Occur?</label>
                        <input 
                          type="date" 
                          name="dateOccurred"
                          value={formData.dateOccurred}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                          required
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Time (approx.)</label>
                        <input 
                          type="time" 
                          name="timeOccurred"
                          value={formData.timeOccurred}
                          onChange={handleChange}
                          className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Anything else that might help us understand or fix this issue?</label>
                      <textarea 
                        name="additionalInfo"
                        value={formData.additionalInfo}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                        rows={3}
                        placeholder="Any additional context or information that might be helpful..."
                      ></textarea>
                    </div>
                  </div>
                  
                  <button 
                    type="submit" 
                    className="w-full flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Submitting Report...
                      </>
                    ) : (
                      <>
                        Submit Bug Report
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </button>
                </form>
                
                <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 p-4">
                  <h3 className="text-md font-medium text-blue-800 dark:text-blue-300 mb-2">Our Commitment</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    We take all bug reports seriously and prioritize them based on severity and impact. After submitting your report:
                  </p>
                  <ol className="list-decimal pl-6 mt-2 text-sm text-blue-700 dark:text-blue-400">
                    <li>You will receive an acknowledgment within 24 hours.</li>
                    <li>Our technical team will investigate the issue.</li>
                    <li>You may be contacted for additional information if needed.</li>
                    <li>You will be notified when the bug is resolved.</li>
                  </ol>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 