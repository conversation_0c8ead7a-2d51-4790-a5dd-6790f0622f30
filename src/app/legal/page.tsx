'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { AlertTriangle, Shield, Ban, FileText, Check, AlertCircle } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function LegalPage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="prose prose-blue dark:prose-invert max-w-4xl mx-auto"
          >
            <motion.h1 
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-8"
            >
              Legal Disclaimer
            </motion.h1>
            
            {/* Intro warning */}
            <motion.div 
              variants={itemVariants}
              className="bg-blue-50 dark:bg-blue-900/30 p-6 rounded-lg border border-blue-200 dark:border-blue-800 mb-8"
            >
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-6 w-6 text-blue-600 dark:text-blue-400 mt-1 flex-shrink-0" />
                <p className="text-blue-800 dark:text-blue-300 m-0">
                  <strong>Please read carefully:</strong> By using FaceTrace.Pro, you agree to this Legal Disclaimer and our Terms of Service. 
                  If you do not agree, please do not use the service.
                </p>
              </div>
            </motion.div>
            
            {/* No Biometric Data Processing */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-blue-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <Shield className="h-7 w-7 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-[#1d3b6c] dark:text-white mt-0 mb-2">1. No Biometric Data Processing</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-0">
                    FaceTrace.Pro does not collect, analyze, store, or process personal identifiable information. Our advanced reverse image search technology only indexes publicly available images for the purpose of visual similarity matching.
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* No Identity Verification */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-blue-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <Ban className="h-7 w-7 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-[#1d3b6c] dark:text-white mt-0 mb-2">2. No Identity Verification or Background Checks</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-0">
                    FaceTrace.Pro is not an identity verification service and should not be used as a substitute for background checks, law enforcement investigations, or personal due diligence. The platform does not confirm a person's identity, criminal history, or legal status.
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* Limited to Publicly Available Content */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-blue-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <FileText className="h-7 w-7 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-[#1d3b6c] dark:text-white mt-0 mb-2">3. Limited to Publicly Available Content</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-0">
                    The results provided by FaceTrace.Pro are sourced from publicly accessible websites and indexed images. We do not index private social media accounts, restricted content, or encrypted databases. If an image appears in search results, it is because it was already published on a public website.
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* No Guarantee of Accuracy */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-yellow-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <AlertCircle className="h-7 w-7 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-yellow-900 dark:text-yellow-300 mt-0 mb-2">4. No Guarantee of Accuracy</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-0">
                    While FaceTrace.Pro strives for accuracy, search results are not guaranteed to be precise or complete. The AI algorithm relies on publicly available data, which may change over time or contain inaccuracies. Users should independently verify any information found through this service.
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* Responsible & Ethical Use */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-red-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <Ban className="h-7 w-7 text-red-600 dark:text-red-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-red-900 dark:text-red-300 mt-0 mb-2">5. Responsible & Ethical Use</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-3">
                    By using FaceTrace.Pro, users agree not to misuse the platform for any of the following:
                  </p>
                  <ul className="space-y-2 mb-0 pl-0 list-none">
                    <li className="flex items-start">
                      <span className="text-red-500 font-bold mr-2">❌</span>
                      <span className="text-gray-700 dark:text-gray-300">Harassment, stalking, or unauthorized tracking of individuals.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-red-500 font-bold mr-2">❌</span>
                      <span className="text-gray-700 dark:text-gray-300">Surveillance, law enforcement, or corporate investigations.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-red-500 font-bold mr-2">❌</span>
                      <span className="text-gray-700 dark:text-gray-300">Violation of privacy laws, GDPR, CCPA, or any applicable regulations.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-red-500 font-bold mr-2">❌</span>
                      <span className="text-gray-700 dark:text-gray-300">Discrimination, defamation, or any unlawful activity.</span>
                    </li>
                  </ul>
                  <p className="text-gray-700 dark:text-gray-300 mt-3 mb-0">
                    Violating these terms may result in the suspension of access, reporting to relevant authorities, and legal consequences.
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* Image Removal Requests */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-indigo-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <FileText className="h-7 w-7 text-indigo-600 dark:text-indigo-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-indigo-900 dark:text-indigo-300 mt-0 mb-2">6. Image Removal Requests</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-0">
                    FaceTrace.Pro respects privacy rights. If you believe an image indexed by our service should be removed, you may submit a request via our Image Removal Page. However, FaceTrace.Pro cannot remove images from third-party websites—users must contact the hosting website directly.
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* Limitation of Liability */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-blue-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <Shield className="h-7 w-7 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-[#1d3b6c] dark:text-white mt-0 mb-2">7. Limitation of Liability</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-3">
                    FaceTrace.Pro and its operators are not responsible for how users interpret, apply, or act upon search results. By using the platform, you acknowledge that:
                  </p>
                  <ul className="space-y-2 mb-0 pl-0 list-none">
                    <li className="flex items-start">
                      <span className="text-blue-500 font-bold mr-2">✓</span>
                      <span className="text-gray-700 dark:text-gray-300">FaceTrace.Pro does not provide legal, personal, or investigative advice.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 font-bold mr-2">✓</span>
                      <span className="text-gray-700 dark:text-gray-300">FaceTrace.Pro is not responsible for third-party content or external website availability.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 font-bold mr-2">✓</span>
                      <span className="text-gray-700 dark:text-gray-300">Users assume full responsibility for how they use the platform and its results.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>
            
            {/* Jurisdiction & Compliance */}
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-teal-500 mb-6"
            >
              <div className="flex items-start gap-4">
                <Check className="h-7 w-7 text-teal-600 dark:text-teal-400 flex-shrink-0" />
                <div>
                  <h2 className="text-xl font-bold text-teal-900 dark:text-teal-300 mt-0 mb-2">8. Jurisdiction & Compliance</h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-0">
                    FaceTrace.Pro operates in accordance with applicable laws and regulations, including GDPR, CCPA, and fair use copyright principles. Any legal disputes related to the use of this platform will be subject to the jurisdiction of European Union.
                  </p>
                </div>
              </div>
            </motion.div>
            
            {/* Contact */}
            <motion.div 
              variants={itemVariants}
              className="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-lg border border-gray-200 dark:border-gray-700 mt-10 text-center"
            >
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Questions? Contact Us</h3>
              <p className="text-gray-700 dark:text-gray-300 mb-2">If you have any questions about this legal disclaimer, please contact us:</p>
              <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium">
                <EMAIL>
              </a>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 