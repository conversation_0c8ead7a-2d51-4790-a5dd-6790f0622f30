'use client';

import { motion } from 'framer-motion';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Button } from '../components/Button';
import Link from 'next/link';

export default function AboutPage() {
  // Animation variants
  const pageVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, transition: { duration: 0.2 } }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1, 
      transition: { 
        staggerChildren: 0.1,
        delayChildren: 0.2
      } 
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
  };

  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <motion.div
          initial="initial"
          animate="animate"
          variants={pageVariants}
          className="container max-w-[90rem] mx-auto px-4"
        >
          {/* Hero Section */}
          <section className="mb-16">
            <div className="max-w-7xl mx-auto">
              <div className="text-center max-w-4xl mx-auto">
                <h1 
                  className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-4"
                >
                  How FaceTrace Works
                </h1>
                
                <p 
                  className="text-base md:text-xl text-gray-600 dark:text-gray-300 mb-6 max-w-3xl mx-auto"
                >
                  Our innovative system helps you monitor your online presence by finding images of yourself 
                  across the web. Here's a detailed look at how our platform works.
                </p>
                
                <div className="w-24 h-1 bg-blue-400 mx-auto rounded"></div>
              </div>
            </div>
          </section>
          
          {/* Security Section */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700">
                <div className="p-8 md:p-10">
                  <div className="flex items-center mb-6">
                    <svg className="w-10 h-10 text-[#2964DD]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="#F0F9FF"/>
                    </svg>
                    <h2 className="text-2xl md:text-3xl font-bold text-[#1d3b6c] dark:text-white ml-4">Your Security Is Our Priority</h2>
                  </div>
                  
                  <div className="grid md:grid-cols-3 gap-4 md:gap-6">
                    <div className="bg-blue-50 dark:bg-gray-700 p-5 rounded-lg">
                      <h3 className="font-semibold text-[#1d3b6c] dark:text-white mb-2 text-lg">Secure Processing</h3>
                      <p className="text-gray-600 dark:text-gray-300">All photos are processed securely using encrypted connections and automatically deleted after 48 hours.</p>
                    </div>
                    <div className="bg-blue-50 dark:bg-gray-700 p-5 rounded-lg">
                      <h3 className="font-semibold text-[#1d3b6c] dark:text-white mb-2 text-lg">Data Protection</h3>
                      <p className="text-gray-600 dark:text-gray-300">We never store your actual photos in our search database, only anonymous mathematical representations.</p>
                    </div>
                    <div className="bg-blue-50 dark:bg-gray-700 p-5 rounded-lg">
                      <h3 className="font-semibold text-[#1d3b6c] dark:text-white mb-2 text-lg">Privacy Controls</h3>
                      <p className="text-gray-600 dark:text-gray-300">You can delete your search history and results at any time, and request removal of your data from our systems.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Step 1 - Upload */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700">
                <div className="md:flex">
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center mb-3">
                      <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#2964DD] mr-3 font-medium">
                        1
                      </div>
                      <h2 className="text-2xl md:text-3xl font-bold text-[#1d3b6c] dark:text-white">Upload Your Photo</h2>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-6 text-lg">
                      Start by uploading a clear photo of your face. For best results:
                    </p>
                    
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Choose a well-lit, front-facing photo</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Make sure your face is clearly visible</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Avoid sunglasses, masks, or heavy filters</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Upload a recent photo for more accurate results</span>
                      </li>
                    </ul>
                    
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      Your photo is processed securely and is used only for the search operation. We automatically delete it after 48 hours.
                    </p>
                  </div>
                  
                  <div className="md:w-1/2 p-8 bg-blue-50 dark:bg-gray-700 flex items-center justify-center">
                    <div className="max-w-md w-full">
                      <div className="rounded-lg border border-blue-200 dark:border-blue-800 bg-white dark:bg-gray-800 p-6 shadow-sm">
                        <div className="flex justify-center mb-4">
                          <div className="p-4 rounded-full bg-blue-50 dark:bg-blue-900/20">
                            <svg className="w-10 h-10 text-[#2964DD]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M7 16.2V14.2H17V16.2H7Z" fill="currentColor"/>
                              <path d="M12 4L17.5 9.5H14V16H10V9.5H6.5L12 4Z" fill="currentColor"/>
                              <path d="M19 20H5C3.89543 20 3 19.1046 3 18V6C3 4.89543 3.89543 4 5 4H9L11 6H19C20.1046 6 21 6.89543 21 8V18C21 19.1046 20.1046 20 19 20Z" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                            </svg>
                          </div>
                        </div>
                        
                        <div className="text-center">
                          <h3 className="text-lg font-medium text-[#1d3b6c] dark:text-white mb-1">Upload your photo here</h3>
                          <p className="text-gray-500 dark:text-gray-400 mb-4">Drag & drop or click to browse</p>
                          
                          <div className="border-2 border-dashed border-blue-200 dark:border-blue-800 rounded-lg p-8 mb-4 bg-blue-50/50 dark:bg-gray-700/50">
                            <svg className="w-12 h-12 mx-auto text-[#2964DD]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                            </svg>
                          </div>
                          
                          <button className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#2964DD] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Select File
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Step 2 - Photo Analysis */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700">
                <div className="md:flex flex-row-reverse">
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center mb-3">
                      <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#2964DD] mr-3 font-medium">
                        2
                      </div>
                      <h2 className="text-2xl md:text-3xl font-bold text-[#1d3b6c] dark:text-white">Photo Analysis</h2>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4 text-lg">
                      FaceTrace analyzes your photo to extract unique facial features. This creates a mathematical representation (or "facial vector") that can be used to find matches.
                    </p>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4">The process involves:</p>
                    
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Detecting and isolating the face in your photo</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Identifying key facial landmarks and features</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Converting these features into a unique digital signature</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Optimizing the signature for efficient searching</span>
                      </li>
                    </ul>
                    
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      We never store your actual photo in our search database, only the anonymous mathematical representation.
                    </p>
                  </div>
                  
                  <div className="md:w-1/2 p-8 bg-blue-50 dark:bg-gray-700 flex items-center justify-center">
                    <div className="w-full max-w-md p-6 bg-white dark:bg-gray-800 rounded-lg border border-blue-100 dark:border-gray-700 shadow-sm">
                      <div className="space-y-4">
                        <div className="flex items-center mb-2">
                          <svg className="w-5 h-5 text-[#2964DD] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                          </svg>
                          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Processing facial features...</h3>
                        </div>
                        
                        <div className="h-4 bg-blue-100 dark:bg-gray-700 rounded overflow-hidden">
                          <div className="h-4 bg-gradient-to-r from-blue-400 to-blue-500 rounded w-3/4"></div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-full"></div>
                          <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-5/6"></div>
                          <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-4/6"></div>
                        </div>
                        
                        <div className="flex space-x-1">
                          <div className="h-2 bg-blue-400 rounded w-1/6"></div>
                          <div className="h-2 bg-blue-500 rounded w-1/6"></div>
                          <div className="h-2 bg-blue-400 rounded w-1/6"></div>
                          <div className="h-2 bg-blue-500 rounded w-1/6"></div>
                          <div className="h-2 bg-blue-400 rounded w-1/6"></div>
                          <div className="h-2 bg-blue-500 rounded w-1/6"></div>
                        </div>
                        
                        <div className="flex justify-end">
                          <div className="h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                            <svg className="w-4 h-4 text-[#2964DD]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"/>
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Step 3 - Searching the Web */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700">
                <div className="md:flex">
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center mb-3">
                      <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#2964DD] mr-3 font-medium">
                        3
                      </div>
                      <h2 className="text-2xl md:text-3xl font-bold text-[#1d3b6c] dark:text-white">Searching the Web</h2>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4 text-lg">
                      We compare your facial vector against our extensive index of publicly available images from across the internet.
                    </p>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4">Our search process:</p>
                    
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Scans billions of images from publicly accessible websites</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Uses advanced algorithms to identify potential matches</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Ranks results by similarity score and relevance</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Excludes images from private social media accounts</span>
                      </li>
                    </ul>
                    
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      Depending on your subscription level, searches can take from a few minutes to several hours to complete.
                    </p>
                  </div>
                  
                  <div className="md:w-1/2 p-8 bg-blue-50 dark:bg-gray-700 flex items-center justify-center">
                    <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 overflow-hidden">
                      <div className="p-6">
                        <div className="flex justify-center mb-6">
                          <div className="flex items-center space-x-3">
                            <svg className="w-8 h-8 text-[#2964DD]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            <svg className="w-8 h-8 text-[#2964DD]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                        </div>
                        
                        <div className="space-y-4">
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 mr-4"></div>
                            <div className="flex-1">
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-full mb-2"></div>
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-5/6"></div>
                            </div>
                            <div className="h-5 w-14 bg-blue-400 rounded-full"></div>
                          </div>
                          
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 mr-4"></div>
                            <div className="flex-1">
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-full mb-2"></div>
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-4/6"></div>
                            </div>
                            <div className="h-5 w-14 bg-blue-400 rounded-full opacity-75"></div>
                          </div>
                          
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 mr-4"></div>
                            <div className="flex-1">
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-full mb-2"></div>
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-3/6"></div>
                            </div>
                            <div className="h-5 w-14 bg-blue-400 rounded-full opacity-50"></div>
                          </div>
                          
                          <div className="flex items-center">
                            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 mr-4"></div>
                            <div className="flex-1">
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-full mb-2"></div>
                              <div className="h-2 bg-blue-100 dark:bg-gray-700 rounded w-2/6"></div>
                            </div>
                            <div className="h-5 w-14 bg-blue-400 rounded-full opacity-25"></div>
                          </div>
                        </div>
                        
                        <div className="text-center mt-6 text-blue-500 dark:text-blue-400 text-sm font-medium">
                          Scanning web for matches...
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Step 4 - Review Your Results */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700">
                <div className="md:flex flex-row-reverse">
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center mb-3">
                      <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#2964DD] mr-3 font-medium">
                        4
                      </div>
                      <h2 className="text-2xl md:text-3xl font-bold text-[#1d3b6c] dark:text-white">Review Your Results</h2>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4 text-lg">
                      Once the search is complete, you'll receive a comprehensive report showing potential matches of your face across the web.
                    </p>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4">For each match, you'll see:</p>
                    
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">The image containing a potential match of your face</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">The website where the image appears</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">A confidence score indicating match accuracy</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#2964DD] mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"/>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.172 13.828a4 4 0 015.656 0l4 4a4 4 0 01-5.656 5.656l-1.102-1.101"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Direct links to the source websites</span>
                      </li>
                    </ul>
                    
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      Premium subscriptions provide additional details and monitoring capabilities to help you better manage your online presence.
                    </p>
                  </div>
                  
                  <div className="md:w-1/2 p-8 bg-blue-50 dark:bg-gray-700 flex items-center justify-center">
                    <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 overflow-hidden">
                      <div className="p-4">
                        <div className="bg-blue-50 dark:bg-gray-700 rounded-lg p-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="aspect-video rounded-md bg-white dark:bg-gray-600 overflow-hidden">
                              <div className="w-full h-full bg-blue-100 dark:bg-blue-900/30"></div>
                            </div>
                            <div className="aspect-video rounded-md bg-white dark:bg-gray-600 overflow-hidden">
                              <div className="w-full h-full bg-blue-100 dark:bg-blue-900/30"></div>
                            </div>
                            <div className="aspect-video rounded-md bg-white dark:bg-gray-600 overflow-hidden">
                              <div className="w-full h-full bg-blue-100 dark:bg-blue-900/30"></div>
                            </div>
                            <div className="aspect-video rounded-md bg-white dark:bg-gray-600 overflow-hidden">
                              <div className="w-full h-full bg-blue-100 dark:bg-blue-900/30"></div>
                            </div>
                          </div>
                          
                          <div className="mt-4 flex justify-center">
                            <button className="px-4 py-2 bg-white dark:bg-gray-600 rounded-md text-[#2964DD] dark:text-blue-400 text-sm font-medium border border-blue-100 dark:border-gray-700">
                              View more results
                            </button>
                          </div>
                        </div>
                        
                        <div className="mt-4 space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="h-3 w-8 bg-blue-400 rounded-full"></div>
                              <div className="h-2 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                            </div>
                            <div className="h-2 w-6 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="h-3 w-6 bg-blue-400 rounded-full opacity-75"></div>
                              <div className="h-2 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                            </div>
                            <div className="h-2 w-6 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="h-3 w-4 bg-blue-400 rounded-full opacity-50"></div>
                              <div className="h-2 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                            </div>
                            <div className="h-2 w-6 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          
          {/* Important Information Section */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl shadow-sm overflow-hidden border border-blue-100 dark:border-blue-800">
                <div className="p-8 md:p-10">
                  <div className="flex items-center mb-6">
                    <svg className="w-6 h-6 text-blue-500 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <h2 className="text-2xl font-bold text-[#1d3b6c] dark:text-white">Important Information</h2>
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-10">
                    <div>
                      <h3 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Privacy & Ethical Use</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">FaceTrace is designed for personal use to monitor your own online presence</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">Only upload photos of yourself or those you have explicit permission to search</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">We do not search private social media accounts or restricted areas of the web</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">Your searches and results are kept private and not shared with third parties</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Limitations</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">Results may not include every instance of your face on the internet</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">Some matches may be false positives, especially with low confidence scores</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">Our index is extensive but doesn't cover the entire internet</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2 text-xl leading-tight">•</span>
                          <span className="text-gray-600 dark:text-gray-300">Search accuracy depends on the quality of the uploaded photo</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Final CTA */}
          <section className="mb-20">
            <div className="max-w-4xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700 text-center p-8 md:p-10">
                <h2 className="text-3xl font-bold text-[#1d3b6c] dark:text-white mb-6">Ready to Discover Your Online Presence?</h2>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                  FaceTrace offers powerful tools to help you monitor where your face appears across the web, giving you greater control over your online identity.
                </p>
                <Link href="/search">
                  <Button 
                    size="lg" 
                    className="bg-[#2964DD] hover:bg-blue-700 text-white px-10 py-3 text-lg font-medium rounded-full shadow-sm hover:shadow"
                  >
                    Try FaceTrace Now
                  </Button>
                </Link>
              </div>
            </div>
          </section>
        </motion.div>
      </main>
      
      <Footer />
    </div>
  );
} 
