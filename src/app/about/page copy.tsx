'use client';

import { motion } from 'framer-motion';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Button } from '../components/Button';
import Link from 'next/link';

export default function AboutPage() {
  // Animation variants
  const pageVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, transition: { duration: 0.2 } }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1, 
      transition: { 
        staggerChildren: 0.1,
        delayChildren: 0.2
      } 
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
  };

  // Define CSS for background pattern and gradient
  const styles = {
    bgGridPattern: {
      backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      backgroundSize: '30px 30px',
      backgroundColor: '#eef5ff'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgGridPattern}>
      {/* AI Face Background */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-20" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 30%, rgba(146, 165, 255, 0.15) 0%, rgba(146, 165, 255, 0) 70%), 
            radial-gradient(circle at 70% 70%, rgba(255, 161, 191, 0.15) 0%, rgba(255, 161, 191, 0) 70%)`,
          backgroundSize: '150% 150%',
          backgroundPosition: 'center'
        }}
      />
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16">
        <motion.div
          initial="initial"
          animate="animate"
          variants={pageVariants}
          className="container max-w-7xl mx-auto px-4"
        >
          {/* Hero Section */}
          <motion.div 
            className="max-w-4xl mx-auto text-center mb-16"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            <motion.h1 
              variants={itemVariants}
              className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#313130] dark:text-white mb-4 font-[RF_Dewi] tracking-tight"
              style={{ fontFamily: "'RF Dewi', sans-serif" }}
            >
              How FaceTrace Works
            </motion.h1>
            
            <motion.p 
              variants={itemVariants}
              className="text-base md:text-lg text-gray-600 dark:text-gray-300 mb-4 max-w-3xl mx-auto"
            >
              Our innovative system helps you monitor your online presence by finding images of yourself 
              across the web. Here's a detailed look at how our platform works.
            </motion.p>
          </motion.div>
          
          {/* Security Section */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
              <div className="md:flex">
                <div className="md:shrink-0 flex items-center justify-center p-8 md:p-10 md:w-1/4">
                  <svg className="w-40 h-40 text-[#92A5FF]" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
                    {/* Shield Base */}
                    <path d="M120 20L40 60V120C40 160 75 197 120 210C165 197 200 160 200 120V60L120 20Z" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="4" strokeLinejoin="round"/>
                    
                    {/* Lock Icon */}
                    <rect x="90" y="100" width="60" height="50" rx="8" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="3"/>
                    <rect x="100" y="80" width="40" height="30" rx="15" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="3"/>
                    <circle cx="120" cy="125" r="10" fill="#92A5FF"/>
                    <rect x="118" y="125" width="4" height="15" rx="2" fill="#F0F6FF"/>
                    
                    {/* Shield Decoration */}
                    <path d="M120 30L50 65V120C50 155 80 187 120 200" stroke="#92A5FF" strokeWidth="2" strokeDasharray="4 4"/>
                    <path d="M120 30L190 65V120C190 155 160 187 120 200" stroke="#92A5FF" strokeWidth="2" strokeDasharray="4 4"/>
                    
                    {/* Data Protection Elements */}
                    <circle cx="80" cy="70" r="12" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="2"/>
                    <path d="M75 70L80 75L85 65" stroke="#92A5FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    
                    <circle cx="160" cy="70" r="12" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="2"/>
                    <path d="M155 70L160 75L165 65" stroke="#92A5FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    
                    <circle cx="120" cy="170" r="12" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="2"/>
                    <path d="M115 170L120 175L125 165" stroke="#92A5FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="p-8 md:p-10 md:w-3/4">
                  <div className="uppercase tracking-wide text-sm text-[#92A5FF] font-semibold">Our Commitment</div>
                  <h2 className="text-2xl md:text-3xl font-bold text-[#313130] dark:text-white mt-1 mb-4">Your Security Is Our Priority</h2>
                  <div className="grid md:grid-cols-3 gap-6 mt-8">
                    <div className="bg-blue-50 dark:bg-gray-700 p-5 rounded-lg">
                      <h3 className="font-semibold text-[#313130] dark:text-white mb-2">Secure Processing</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">All photos are processed securely using encrypted connections and automatically deleted after 48 hours.</p>
                    </div>
                    <div className="bg-blue-50 dark:bg-gray-700 p-5 rounded-lg">
                      <h3 className="font-semibold text-[#313130] dark:text-white mb-2">Data Protection</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">We never store your actual photos in our search database, only anonymous mathematical representations.</p>
                    </div>
                    <div className="bg-blue-50 dark:bg-gray-700 p-5 rounded-lg">
                      <h3 className="font-semibold text-[#313130] dark:text-white mb-2">Privacy Controls</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">You can delete your search history and results at any time, and request removal of your data from our systems.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Process Steps in Rows */}
          {/* Step 1 - Upload */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div className="md:flex">
                  <div className="md:shrink-0 flex items-center justify-center p-8 bg-blue-50 dark:bg-gray-700 md:w-2/5">
                    <div className="relative">
                      <div className="absolute -top-12 -left-12 w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#92A5FF] font-bold text-2xl">
                        1
                      </div>
                      <svg className="w-64 h-64" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
                        {/* Upload Interface */}
                        <rect className="rounded-lg" x="40" y="30" width="160" height="180" rx="10" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="2.5"/>
                        
                        {/* Browser Header */}
                        <rect x="40" y="30" width="160" height="25" rx="10" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="2.5"/>
                        <circle cx="55" cy="42.5" r="5" fill="#FF8A8A"/>
                        <circle cx="75" cy="42.5" r="5" fill="#FFDD8A"/>
                        <circle cx="95" cy="42.5" r="5" fill="#8AFF8A"/>
                        
                        {/* Upload Area */}
                        <rect x="60" y="75" width="120" height="90" rx="8" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="2" strokeDasharray="6 3"/>
                        
                        {/* Upload Icon */}
                        <circle cx="120" cy="105" r="15" fill="#92A5FF" fillOpacity="0.3" stroke="#92A5FF" strokeWidth="2"/>
                        <path d="M120 95V115M110 105H130" stroke="#92A5FF" strokeWidth="3" strokeLinecap="round"/>
                        
                        {/* Face Placeholder */}
                        <circle cx="120" cy="140" r="20" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1.5"/>
                        <path d="M110 135C110 135 113 132 120 132C127 132 130 135 130 135" stroke="#92A5FF" strokeWidth="1.5"/>
                        <path d="M110 145C110 145 113 150 120 150C127 150 130 145 130 145" stroke="#92A5FF" strokeWidth="1.5"/>
                        <circle cx="112" cy="138" r="2" fill="#92A5FF"/>
                        <circle cx="128" cy="138" r="2" fill="#92A5FF"/>
                        
                        {/* Button */}
                        <rect x="85" y="180" width="70" height="20" rx="10" fill="#92A5FF"/>
                        <text x="95" y="194" fill="white" fontFamily="Arial" fontSize="12" fontWeight="bold">UPLOAD</text>
                      </svg>
                    </div>
                  </div>
                  <div className="p-8 md:w-3/5">
                    <div className="uppercase tracking-wide text-sm text-[#92A5FF] font-semibold">Step 1</div>
                    <h2 className="text-2xl md:text-3xl font-bold text-[#313130] dark:text-white mt-1 mb-4">Upload Your Photo</h2>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      Start by uploading a clear photo of your face. For best results:
                    </p>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Choose a well-lit, front-facing photo</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Make sure your face is clearly visible</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Avoid sunglasses, masks, or heavy filters</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Upload a recent photo for more accurate results</span>
                      </li>
                    </ul>
                    <p className="mt-4 text-gray-600 dark:text-gray-300 text-sm">
                      Your photo is processed securely and is used only for the search operation. We automatically delete it after 48 hours.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Step 2 - Photo Analysis */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div className="md:flex flex-row-reverse">
                  <div className="md:shrink-0 flex items-center justify-center p-8 bg-blue-50 dark:bg-gray-700 md:w-2/5">
                    <div className="relative">
                      <div className="absolute -top-12 -right-12 w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#92A5FF] font-bold text-2xl">
                        2
                      </div>
                      <svg className="w-64 h-64" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
                        {/* Analysis Interface */}
                        <rect x="40" y="30" width="160" height="180" rx="10" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="2.5"/>
                        
                        {/* Face Detection */}
                        <circle cx="120" cy="90" r="35" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="2"/>
                        <path d="M105 85C105 85 108 82 120 82C132 82 135 85 135 85" stroke="#92A5FF" strokeWidth="1.5"/>
                        <path d="M110 100C110 100 115 105 120 105C125 105 130 100 130 100" stroke="#92A5FF" strokeWidth="1.5"/>
                        <circle cx="110" cy="90" r="3" fill="#92A5FF"/>
                        <circle cx="130" cy="90" r="3" fill="#92A5FF"/>
                        
                        {/* Facial Recognition Points */}
                        <circle cx="110" cy="90" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        <circle cx="130" cy="90" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        <circle cx="120" cy="82" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        <circle cx="105" cy="85" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        <circle cx="135" cy="85" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        <circle cx="120" cy="105" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        <circle cx="110" cy="100" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        <circle cx="130" cy="100" r="1" fill="#FF8A8A" stroke="#FF8A8A" strokeWidth="0.5"/>
                        
                        {/* Facial Mesh Grid */}
                        <path d="M85 90H155" stroke="#92A5FF" strokeWidth="0.5" strokeDasharray="2 2"/>
                        <path d="M120 55V125" stroke="#92A5FF" strokeWidth="0.5" strokeDasharray="2 2"/>
                        <path d="M95 65L145 115" stroke="#92A5FF" strokeWidth="0.5" strokeDasharray="2 2"/>
                        <path d="M145 65L95 115" stroke="#92A5FF" strokeWidth="0.5" strokeDasharray="2 2"/>
                        
                        {/* Digital Signature */}
                        <rect x="60" y="140" width="120" height="10" rx="2" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        <rect x="60" y="155" width="120" height="10" rx="2" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        <rect x="60" y="170" width="120" height="10" rx="2" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        
                        {/* Binary Data Visualization */}
                        <text x="65" y="148" fill="#92A5FF" fontFamily="monospace" fontSize="8">10110101 01101001 10010110</text>
                        <text x="65" y="163" fill="#92A5FF" fontFamily="monospace" fontSize="8">01011010 10101010 01010101</text>
                        <text x="65" y="178" fill="#92A5FF" fontFamily="monospace" fontSize="8">11001010 01010011 10101100</text>
                        
                        {/* Processing Animation */}
                        <circle cx="50" cy="190" r="3" fill="#92A5FF"/>
                        <circle cx="60" cy="190" r="3" fill="#92A5FF" fillOpacity="0.8"/>
                        <circle cx="70" cy="190" r="3" fill="#92A5FF" fillOpacity="0.6"/>
                        <circle cx="80" cy="190" r="3" fill="#92A5FF" fillOpacity="0.4"/>
                        <circle cx="90" cy="190" r="3" fill="#92A5FF" fillOpacity="0.2"/>
                      </svg>
                    </div>
                  </div>
                  <div className="p-8 md:w-3/5">
                    <div className="uppercase tracking-wide text-sm text-[#92A5FF] font-semibold">Step 2</div>
                    <h2 className="text-2xl md:text-3xl font-bold text-[#313130] dark:text-white mt-1 mb-4">Photo Analysis</h2>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      FaceTrace analyzes your photo to extract unique facial features. This creates a mathematical representation (or "facial vector") that can be used to find matches.
                    </p>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">The process involves:</p>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Detecting and isolating the face in your photo</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Identifying key facial landmarks and features</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Converting these features into a unique digital signature</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Optimizing the signature for efficient searching</span>
                      </li>
                    </ul>
                    <p className="mt-4 text-gray-600 dark:text-gray-300 text-sm">
                      We never store your actual photo in our search database, only the anonymous mathematical representation.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Step 3 - Searching the Web */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div className="md:flex">
                  <div className="md:shrink-0 flex items-center justify-center p-8 bg-blue-50 dark:bg-gray-700 md:w-2/5">
                    <div className="relative">
                      <div className="absolute -top-12 -left-12 w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#92A5FF] font-bold text-2xl">
                        3
                      </div>
                      <svg className="w-64 h-64" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
                        {/* Web Search Interface */}
                        <rect x="30" y="40" width="180" height="160" rx="10" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="2.5"/>
                        
                        {/* Browser Header */}
                        <rect x="30" y="40" width="180" height="25" rx="5" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1.5"/>
                        <circle cx="45" cy="52.5" r="5" fill="#FF8A8A"/>
                        <circle cx="60" cy="52.5" r="5" fill="#FFDD8A"/>
                        <circle cx="75" cy="52.5" r="5" fill="#8AFF8A"/>
                        
                        {/* URL Bar */}
                        <rect x="90" y="45" width="110" height="15" rx="3" fill="white" stroke="#92A5FF" strokeWidth="1"/>
                        <text x="95" y="56" fill="#92A5FF" fontFamily="Arial" fontSize="8">https://facetrace.app/search</text>
                        
                        {/* Search Engine Interface */}
                        <rect x="45" y="75" width="150" height="30" rx="15" fill="white" stroke="#92A5FF" strokeWidth="1.5"/>
                        <circle cx="185" cy="90" r="10" fill="#92A5FF" fillOpacity="0.2"/>
                        <path d="M182 90L188 96" stroke="#92A5FF" strokeWidth="2" strokeLinecap="round"/>
                        <text x="55" y="93" fill="#92A5FF" fontFamily="Arial" fontSize="10">Search by face...</text>
                        
                        {/* Web Content Visualization */}
                        <rect x="45" y="115" width="45" height="35" rx="3" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        <rect x="97.5" y="115" width="45" height="35" rx="3" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        <rect x="150" y="115" width="45" height="35" rx="3" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        
                        <rect x="45" y="160" width="45" height="35" rx="3" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        <rect x="97.5" y="160" width="45" height="35" rx="3" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        <rect x="150" y="160" width="45" height="35" rx="3" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1"/>
                        
                        {/* Face Icons in Results */}
                        <circle cx="67.5" cy="132.5" r="10" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M63 130C63 130 65 128 67.5 128C70 128 72 130 72 130" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M63 135C63 135 65 138 67.5 138C70 138 72 135 72 135" stroke="#92A5FF" strokeWidth="1"/>
                        <circle cx="64" cy="131" r="1.5" fill="#92A5FF"/>
                        <circle cx="71" cy="131" r="1.5" fill="#92A5FF"/>
                        
                        <circle cx="120" cy="132.5" r="10" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M115.5 130C115.5 130 117.5 128 120 128C122.5 128 124.5 130 124.5 130" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M115.5 135C115.5 135 117.5 138 120 138C122.5 138 124.5 135 124.5 135" stroke="#92A5FF" strokeWidth="1"/>
                        <circle cx="116.5" cy="131" r="1.5" fill="#92A5FF"/>
                        <circle cx="123.5" cy="131" r="1.5" fill="#92A5FF"/>
                        
                        <circle cx="172.5" cy="132.5" r="10" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M168 130C168 130 170 128 172.5 128C175 128 177 130 177 130" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M168 135C168 135 170 138 172.5 138C175 138 177 135 177 135" stroke="#92A5FF" strokeWidth="1"/>
                        <circle cx="169" cy="131" r="1.5" fill="#92A5FF"/>
                        <circle cx="176" cy="131" r="1.5" fill="#92A5FF"/>
                        
                        {/* Match Indicators */}
                        <rect x="45" y="152" width="45" height="5" rx="2.5" fill="#92A5FF"/>
                        <text x="60" y="156" fill="white" fontFamily="Arial" fontSize="4" textAnchor="middle">87% MATCH</text>
                        
                        <rect x="97.5" y="152" width="45" height="5" rx="2.5" fill="#92A5FF"/>
                        <text x="120" y="156" fill="white" fontFamily="Arial" fontSize="4" textAnchor="middle">92% MATCH</text>
                        
                        <rect x="150" y="152" width="45" height="5" rx="2.5" fill="#92A5FF"/>
                        <text x="172.5" y="156" fill="white" fontFamily="Arial" fontSize="4" textAnchor="middle">78% MATCH</text>
                      </svg>
                    </div>
                  </div>
                  <div className="p-8 md:w-3/5">
                    <div className="uppercase tracking-wide text-sm text-[#92A5FF] font-semibold">Step 3</div>
                    <h2 className="text-2xl md:text-3xl font-bold text-[#313130] dark:text-white mt-1 mb-4">Searching the Web</h2>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      We compare your facial vector against our extensive index of publicly available images from across the internet.
                    </p>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">Our search process:</p>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Scans billions of images from publicly accessible websites</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Uses advanced algorithms to identify potential matches</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Ranks results by similarity score and relevance</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Excludes images from private social media accounts</span>
                      </li>
                    </ul>
                    <p className="mt-4 text-gray-600 dark:text-gray-300 text-sm">
                      Depending on your subscription level, searches can take from a few minutes to several hours to complete.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Step 4 - Review Your Results */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div className="md:flex flex-row-reverse">
                  <div className="md:shrink-0 flex items-center justify-center p-8 bg-blue-50 dark:bg-gray-700 md:w-2/5">
                    <div className="relative">
                      <div className="absolute -top-12 -right-12 w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-[#92A5FF] font-bold text-2xl">
                        4
                      </div>
                      <svg className="w-64 h-64" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
                        {/* Results Dashboard */}
                        <rect x="20" y="30" width="200" height="180" rx="10" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="2.5"/>
                        
                        {/* Dashboard Header */}
                        <rect x="20" y="30" width="200" height="30" rx="10" fill="#E6EDFF" stroke="#92A5FF" strokeWidth="1.5"/>
                        <text x="85" y="50" fill="#92A5FF" fontFamily="Arial" fontSize="12" fontWeight="bold">SEARCH RESULTS</text>
                        
                        {/* Results Grid */}
                        <rect x="35" y="70" width="75" height="60" rx="5" fill="white" stroke="#92A5FF" strokeWidth="1.5"/>
                        <rect x="130" y="70" width="75" height="60" rx="5" fill="white" stroke="#92A5FF" strokeWidth="1.5"/>
                        <rect x="35" y="140" width="75" height="60" rx="5" fill="white" stroke="#92A5FF" strokeWidth="1.5"/>
                        <rect x="130" y="140" width="75" height="60" rx="5" fill="white" stroke="#92A5FF" strokeWidth="1.5"/>
                        
                        {/* Result Content - Images with Faces */}
                        <rect x="40" y="75" width="65" height="40" rx="3" fill="#E6EDFF"/>
                        <circle cx="72.5" cy="95" r="15" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M67 92C67 92 70 90 72.5 90C75 90 78 92 78 92" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M67 98C67 98 70 102 72.5 102C75 102 78 98 78 98" stroke="#92A5FF" strokeWidth="1"/>
                        <circle cx="68.5" cy="93.5" r="1.5" fill="#92A5FF"/>
                        <circle cx="76.5" cy="93.5" r="1.5" fill="#92A5FF"/>
                        
                        <rect x="135" y="75" width="65" height="40" rx="3" fill="#E6EDFF"/>
                        <circle cx="167.5" cy="95" r="15" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M162 92C162 92 165 90 167.5 90C170 90 173 92 173 92" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M162 98C162 98 165 102 167.5 102C170 102 173 98 173 98" stroke="#92A5FF" strokeWidth="1"/>
                        <circle cx="163.5" cy="93.5" r="1.5" fill="#92A5FF"/>
                        <circle cx="171.5" cy="93.5" r="1.5" fill="#92A5FF"/>
                        
                        <rect x="40" y="145" width="65" height="40" rx="3" fill="#E6EDFF"/>
                        <circle cx="72.5" cy="165" r="15" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M67 162C67 162 70 160 72.5 160C75 160 78 162 78 162" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M67 168C67 168 70 172 72.5 172C75 172 78 168 78 168" stroke="#92A5FF" strokeWidth="1"/>
                        <circle cx="68.5" cy="163.5" r="1.5" fill="#92A5FF"/>
                        <circle cx="76.5" cy="163.5" r="1.5" fill="#92A5FF"/>
                        
                        <rect x="135" y="145" width="65" height="40" rx="3" fill="#E6EDFF"/>
                        <circle cx="167.5" cy="165" r="15" fill="#F0F6FF" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M162 162C162 162 165 160 167.5 160C170 160 173 162 173 162" stroke="#92A5FF" strokeWidth="1"/>
                        <path d="M162 168C162 168 165 172 167.5 172C170 172 173 168 173 168" stroke="#92A5FF" strokeWidth="1"/>
                        <circle cx="163.5" cy="163.5" r="1.5" fill="#92A5FF"/>
                        <circle cx="171.5" cy="163.5" r="1.5" fill="#92A5FF"/>
                        
                        {/* Result Metadata */}
                        <rect x="40" y="118" width="65" height="7" rx="3.5" fill="#92A5FF"/>
                        <text x="72.5" y="124" fill="white" fontFamily="Arial" fontSize="5" textAnchor="middle">95% MATCH</text>
                        
                        <rect x="135" y="118" width="65" height="7" rx="3.5" fill="#92A5FF"/>
                        <text x="167.5" y="124" fill="white" fontFamily="Arial" fontSize="5" textAnchor="middle">89% MATCH</text>
                        
                        <rect x="40" y="188" width="65" height="7" rx="3.5" fill="#92A5FF"/>
                        <text x="72.5" y="194" fill="white" fontFamily="Arial" fontSize="5" textAnchor="middle">82% MATCH</text>
                        
                        <rect x="135" y="188" width="65" height="7" rx="3.5" fill="#92A5FF"/>
                        <text x="167.5" y="194" fill="white" fontFamily="Arial" fontSize="5" textAnchor="middle">78% MATCH</text>
                        
                        {/* Source Icons */}
                        <circle cx="45" cy="130" r="3" fill="#92A5FF"/>
                      </svg>
                    </div>
                  </div>
                  <div className="p-8 md:w-3/5">
                    <div className="uppercase tracking-wide text-sm text-[#92A5FF] font-semibold">Step 4</div>
                    <h2 className="text-2xl md:text-3xl font-bold text-[#313130] dark:text-white mt-1 mb-4">Review Your Results</h2>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      Once the search is complete, you'll receive a comprehensive report showing potential matches of your face across the web.
                    </p>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">For each match, you'll see:</p>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">The image containing a potential match of your face</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">The website where the image appears</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">A confidence score indicating match accuracy</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#92A5FF] mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"/>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.172 13.828a4 4 0 015.656 0l4 4a4 4 0 01-5.656 5.656l-1.102-1.101"/>
                        </svg>
                        <span className="text-gray-600 dark:text-gray-300">Direct links to the source websites</span>
                      </li>
                    </ul>
                    <p className="mt-4 text-gray-600 dark:text-gray-300 text-sm">
                      Premium subscriptions provide additional details and monitoring capabilities to help you better manage your online presence.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          
          {/* Important Information Section */}
          <section className="mb-20">
            <div className="max-w-7xl mx-auto bg-blue-50 dark:bg-blue-900/20 rounded-xl shadow-md overflow-hidden">
              <div className="p-8">
                <div className="flex items-center mb-6">
                  <svg className="w-8 h-8 text-blue-500 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <h2 className="text-2xl font-bold text-[#313130] dark:text-white">Important Information</h2>
                </div>
                
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-[#313130] dark:text-white mb-4">Privacy & Ethical Use</h3>
                    <ul className="space-y-3">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">FaceTrace is designed for personal use to monitor your own online presence</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">Only upload photos of yourself or those you have explicit permission to search</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">We do not search private social media accounts or restricted areas of the web</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">Your searches and results are kept private and not shared with third parties</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-semibold text-[#313130] dark:text-white mb-4">Limitations</h3>
                    <ul className="space-y-3">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">Results may not include every instance of your face on the internet</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">Some matches may be false positives, especially with low confidence scores</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">Our index is extensive but doesn't cover the entire internet</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span className="text-gray-600 dark:text-gray-300">Search accuracy depends on the quality of the uploaded photo</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          {/* Final CTA */}
          <section className="mb-10">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold text-[#313130] dark:text-white mb-6">Ready to Discover Your Online Presence?</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                FaceTrace offers powerful tools to help you monitor where your face appears across the web, giving you greater control over your online identity.
              </p>
              <Link href="/search">
                <Button 
                  size="lg" 
                  className="bg-[#2964DD] hover:bg-blue-700 text-white px-8 py-3 text-lg font-medium rounded-full"
                >
                  Try FaceTrace Now
                </Button>
              </Link>
            </div>
          </section>
        </motion.div>
      </main>
      
      <Footer />
    </div>
  );
}
