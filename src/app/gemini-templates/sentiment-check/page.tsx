"use client";

import { useState, FormEvent } from "react";
import { Button } from "@/app/components/Button";
import { GEMINI_MODELS, DEFAULT_GENERATION_CONFIG } from "@/app/api/gemini/route";

type SentimentResult = {
  text: string;
  sentiment: "positive" | "negative" | "neutral" | "mixed";
  score: number;
  analysis: string;
  keywords: string[];
};

export default function SentimentCheckPage() {
  const [textInput, setTextInput] = useState("");
  const [batchMode, setBatchMode] = useState(false);
  const [results, setResults] = useState<SentimentResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!textInput.trim()) return;

    try {
      setIsLoading(true);
      setError(null);

      const texts = batchMode ? parseMultipleTexts(textInput) : [textInput];
      const newResults: SentimentResult[] = [];

      for (const text of texts) {
        const prompt = createSentimentPrompt(text);
        
        const response = await fetch("/api/gemini", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ 
            prompt,
            model: GEMINI_MODELS.GEMINI_2_5_PRO_PREVIEW,
            temperature: 0.1, // Lower temperature for more consistent results
          }),
        });

        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || "Failed to analyze sentiment");
        }
        
        try {
          // Parse the structured response
          const parsedResult = parseSentimentResponse(data.response, text);
          newResults.push(parsedResult);
        } catch (parseError) {
          console.error("Failed to parse result:", parseError);
          newResults.push({
            text,
            sentiment: "neutral",
            score: 0,
            analysis: data.response,
            keywords: [],
          });
        }
      }
      
      setResults(newResults);
    } catch (err: any) {
      setError(err.message || "An error occurred");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const createSentimentPrompt = (text: string) => {
    return `Analyze the sentiment of the following text and respond in a structured JSON format.
    
Text to analyze: "${text}"

Provide your response in the following JSON format:
{
  "sentiment": "positive|negative|neutral|mixed",
  "score": [number between -1 and 1, where -1 is very negative, 0 is neutral, and 1 is very positive],
  "analysis": [brief 1-2 sentence analysis explaining the sentiment],
  "keywords": [array of emotional or tone keywords present in the text]
}

Only respond with the JSON object, no additional text.`;
  };

  const parseMultipleTexts = (input: string): string[] => {
    // Split by line breaks and filter out empty lines
    return input
      .split(/\n/)
      .map(line => line.trim())
      .filter(line => line.length > 0);
  };

  const parseSentimentResponse = (response: string, originalText: string): SentimentResult => {
    try {
      // Extract JSON from the response (in case there's surrounding text)
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error("No JSON found in response");
      
      const parsedResponse = JSON.parse(jsonMatch[0]);
      
      return {
        text: originalText,
        sentiment: parsedResponse.sentiment || "neutral",
        score: parsedResponse.score || 0,
        analysis: parsedResponse.analysis || "No analysis provided",
        keywords: parsedResponse.keywords || [],
      };
    } catch (e) {
      console.error("Failed to parse JSON from response:", e);
      return {
        text: originalText,
        sentiment: "neutral",
        score: 0,
        analysis: response,
        keywords: [],
      };
    }
  };

  const getSentimentColor = (sentiment: string): string => {
    switch (sentiment) {
      case "positive":
        return "text-green-600 dark:text-green-400";
      case "negative":
        return "text-red-600 dark:text-red-400";
      case "mixed":
        return "text-purple-600 dark:text-purple-400";
      case "neutral":
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  const getSentimentBgColor = (sentiment: string): string => {
    switch (sentiment) {
      case "positive":
        return "bg-green-50 dark:bg-green-900/20";
      case "negative":
        return "bg-red-50 dark:bg-red-900/20";
      case "mixed":
        return "bg-purple-50 dark:bg-purple-900/20";
      case "neutral":
      default:
        return "bg-gray-50 dark:bg-gray-800";
    }
  };

  const clearAll = () => {
    setTextInput("");
    setResults([]);
    setError(null);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Sentiment Check</h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Analyze the sentiment of text to understand emotional tone
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Input Text</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex items-center mb-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox h-4 w-4 text-blue-600"
                  checked={batchMode}
                  onChange={(e) => setBatchMode(e.target.checked)}
                />
                <span className="ml-2">Batch Mode (one text per line)</span>
              </label>
            </div>

            <div>
              <label htmlFor="textInput" className="block mb-1">
                Text to Analyze:
              </label>
              <textarea
                id="textInput"
                className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
                rows={8}
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder={batchMode ? "Enter multiple texts, one per line..." : "Enter text to analyze..."}
                required
              />
            </div>

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={isLoading || !textInput.trim()}
                data-loading={isLoading}
              >
                {isLoading ? "Analyzing..." : "Analyze Sentiment"}
              </Button>
              <Button type="button" onClick={clearAll} variant="secondary">
                Clear All
              </Button>
            </div>

            {error && (
              <div className="p-3 bg-red-100 text-red-700 rounded-md mt-4">
                {error}
              </div>
            )}
          </form>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Results</h2>
          
          {results.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center py-8 text-gray-500 dark:text-gray-400">
              <p>Sentiment analysis results will appear here</p>
            </div>
          ) : (
            <div className="space-y-4">
              {results.map((result, index) => (
                <div key={index} className={`p-6 rounded-lg shadow-md ${getSentimentBgColor(result.sentiment)}`}>
                  <div className="text-gray-700 dark:text-gray-300 mb-2 text-sm line-clamp-2">
                    "{result.text}"
                  </div>
                  
                  <div className="flex justify-between items-center mb-3">
                    <div className={`font-bold text-lg capitalize ${getSentimentColor(result.sentiment)}`}>
                      {result.sentiment}
                    </div>
                    <div className="text-sm">
                      Score: <span className="font-mono">{result.score.toFixed(2)}</span>
                    </div>
                  </div>
                  
                  <div className="mb-3 text-gray-700 dark:text-gray-300">
                    {result.analysis}
                  </div>
                  
                  {result.keywords.length > 0 && (
                    <div>
                      <p className="text-xs text-gray-500 mb-1">Key emotions/tones:</p>
                      <div className="flex flex-wrap gap-1">
                        {result.keywords.map((keyword, kidx) => (
                          <span 
                            key={kidx} 
                            className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded-full"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="mt-10 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">About Sentiment Analysis</h2>
        <div className="space-y-4">
          <p>
            Sentiment analysis is the process of determining the emotional tone behind text. It helps identify whether the 
            expressed opinion is positive, negative, neutral, or mixed.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h3 className="font-bold text-green-600 dark:text-green-400">Positive</h3>
              <p className="text-sm">Expresses approval, optimism, or happiness</p>
            </div>
            
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
              <h3 className="font-bold text-red-600 dark:text-red-400">Negative</h3>
              <p className="text-sm">Expresses criticism, pessimism, anger, or sadness</p>
            </div>
            
            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-bold text-gray-600 dark:text-gray-400">Neutral</h3>
              <p className="text-sm">States facts without emotional tone</p>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h3 className="font-bold text-purple-600 dark:text-purple-400">Mixed</h3>
              <p className="text-sm">Contains both positive and negative elements</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
