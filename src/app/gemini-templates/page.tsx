import Link from "next/link";

const templates = [
  {
    name: "Santa's Mailbox",
    description: "Write a letter to <PERSON> and get a personalized response",
    path: "/gemini-templates/santas-mailbox",
    icon: "🎅",
    color: "bg-red-100 dark:bg-red-900/20",
    accentColor: "text-red-600 dark:text-red-400",
  },
  {
    name: "Video Q&A",
    description: "Ask questions about key details in videos",
    path: "/gemini-templates/video-qa",
    icon: "🎬",
    color: "bg-blue-100 dark:bg-blue-900/20",
    accentColor: "text-blue-600 dark:text-blue-400",
  },
  {
    name: "Sentiment Check",
    description: "Analyze the sentiment and emotional tone of text",
    path: "/gemini-templates/sentiment-check",
    icon: "😊",
    color: "bg-green-100 dark:bg-green-900/20",
    accentColor: "text-green-600 dark:text-green-400",
  },
  {
    name: "Code Assistant",
    description: "Debug, optimize, and improve your code",
    path: "/debug",
    icon: "👨‍💻",
    color: "bg-purple-100 dark:bg-purple-900/20",
    accentColor: "text-purple-600 dark:text-purple-400",
  },
  {
    name: "General Assistant",
    description: "General purpose AI assistant with text and image capabilities",
    path: "/gemini",
    icon: "🤖",
    color: "bg-yellow-100 dark:bg-yellow-900/20", 
    accentColor: "text-yellow-600 dark:text-yellow-400",
  },
];

export default function GeminiTemplatesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-3">Gemini AI Templates</h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Specialized applications powered by Google Gemini 2.5 Pro Preview
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template, index) => (
          <Link
            key={index}
            href={template.path}
            className={`${template.color} p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 flex flex-col`}
          >
            <div className="flex items-center mb-4">
              <div className="text-4xl mr-4">{template.icon}</div>
              <h2 className={`text-xl font-semibold ${template.accentColor}`}>
                {template.name}
              </h2>
            </div>
            <p className="text-gray-700 dark:text-gray-300">
              {template.description}
            </p>
            <div className={`mt-auto pt-4 ${template.accentColor} font-medium text-sm text-right`}>
              Explore →
            </div>
          </Link>
        ))}
      </div>

      <div className="mt-16 bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">About Google Gemini 2.5 Pro</h2>
        <p className="mb-4">
          These templates showcase the capabilities of Google's Gemini 2.5 Pro Preview model, which offers:
        </p>
        <ul className="list-disc pl-5 space-y-2">
          <li>Advanced text understanding and generation</li>
          <li>Image analysis and multimodal reasoning</li>
          <li>Long context comprehension with up to 1M tokens</li>
          <li>Improved reasoning and problem-solving capabilities</li>
          <li>The ability to follow complex, multi-step instructions</li>
        </ul>

        <h3 className="text-xl font-semibold mt-8 mb-2">Configuration Options</h3>
        <p className="mb-4">
          These templates use the following configuration for optimal performance:
        </p>
        <div className="bg-gray-100 dark:bg-gray-700 rounded-md p-4 font-mono text-sm">
          <div>temperature: <span className="text-green-600 dark:text-green-400">1.0</span></div>
          <div>topP: <span className="text-green-600 dark:text-green-400">0.95</span></div>
          <div>topK: <span className="text-green-600 dark:text-green-400">64</span></div>
          <div>maxOutputTokens: <span className="text-green-600 dark:text-green-400">65536</span></div>
        </div>

        <div className="mt-8 text-gray-600 dark:text-gray-400 text-sm">
          <p>
            Note: To use these templates, you'll need to set up your
            <span className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded font-mono text-xs mx-1">GOOGLE_GEMINI_API_KEY</span>
            in your .env.local file.
          </p>
        </div>
      </div>
    </div>
  );
}
