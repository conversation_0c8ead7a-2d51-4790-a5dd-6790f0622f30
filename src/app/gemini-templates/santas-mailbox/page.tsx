"use client";

import { useState, FormEvent } from "react";
import { Button } from "@/app/components/Button";
import { GEMINI_MODELS } from "@/app/api/gemini/route";

export default function SantasMailboxPage() {
  const [childName, setChildName] = useState("");
  const [childAge, setChildAge] = useState("");
  const [letterContent, setLetterContent] = useState("");
  const [responseType, setResponseType] = useState("encouraging");
  const [response, setResponse] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const responseTypes = {
    encouraging: "Encouraging and supportive",
    educational: "Educational and informative",
    funny: "Funny and playful",
    magical: "Magical and whimsical",
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!letterContent.trim()) return;

    try {
      setIsLoading(true);
      setError(null);

      const prompt = createPrompt();
      
      const response = await fetch("/api/gemini", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          prompt,
          model: GEMINI_MODELS.GEMINI_2_5_PRO_PREVIEW
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to get Santa's response");
      }
      
      setResponse(data.response);
    } catch (err: any) {
      setError(err.message || "An error occurred");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const createPrompt = () => {
    let prompt = "You are Santa Claus responding to a letter from a child. ";
    
    prompt += `The child's name is ${childName || "unknown"}. `;
    
    if (childAge) {
      prompt += `The child is ${childAge} years old. `;
    }
    
    switch (responseType) {
      case "encouraging":
        prompt += "Be encouraging, warm, and supportive in your response. ";
        break;
      case "educational":
        prompt += "Include some educational facts about the North Pole, reindeer, or toy-making in your response. ";
        break;
      case "funny":
        prompt += "Be funny and playful in your response, include some jokes. ";
        break;
      case "magical":
        prompt += "Focus on the magic of Christmas and create a sense of wonder in your response. ";
        break;
    }
    
    prompt += "Sign the letter as 'Santa Claus' at the end. ";
    prompt += "Here is the child's letter:\n\n";
    prompt += letterContent;
    
    return prompt;
  };

  const clearAll = () => {
    setChildName("");
    setChildAge("");
    setLetterContent("");
    setResponse(null);
    setError(null);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-red-600 mb-2">Santa's Mailbox</h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Write a letter to Santa and get a personalized response!
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Write Your Letter</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="childName" className="block mb-1">
                Child's Name:
              </label>
              <input
                id="childName"
                className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
                value={childName}
                onChange={(e) => setChildName(e.target.value)}
                placeholder="e.g., Emma"
              />
            </div>

            <div>
              <label htmlFor="childAge" className="block mb-1">
                Child's Age:
              </label>
              <input
                id="childAge"
                type="number"
                min="1"
                max="12"
                className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
                value={childAge}
                onChange={(e) => setChildAge(e.target.value)}
                placeholder="e.g., 7"
              />
            </div>

            <div>
              <label htmlFor="responseType" className="block mb-1">
                Type of Response:
              </label>
              <select
                id="responseType"
                className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
                value={responseType}
                onChange={(e) => setResponseType(e.target.value)}
              >
                {Object.entries(responseTypes).map(([value, label]) => (
                  <option key={value} value={value}>
                    {label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="letterContent" className="block mb-1">
                Letter to Santa:
              </label>
              <textarea
                id="letterContent"
                className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
                rows={8}
                value={letterContent}
                onChange={(e) => setLetterContent(e.target.value)}
                placeholder="Dear Santa, I've been very good this year..."
                required
              />
            </div>

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={isLoading || !letterContent.trim()}
                data-loading={isLoading}
              >
                {isLoading ? "Sending to North Pole..." : "Get Santa's Response"}
              </Button>
              <Button type="button" onClick={clearAll} variant="secondary">
                Clear All
              </Button>
            </div>
          </form>
        </div>

        <div className="relative">
          {response ? (
            <div className="bg-[url('/paper-texture.jpg')] bg-cover p-8 rounded-lg shadow-md min-h-[400px] relative">
              <div className="absolute top-0 right-0 w-24 h-24">
                <div className="absolute w-24 h-24 bg-red-600 rounded-bl-full"></div>
                <div className="absolute w-6 h-6 bg-green-600 rounded-full top-4 right-4"></div>
              </div>
              
              <div className="text-green-800 font-serif whitespace-pre-wrap">
                {response}
              </div>
              
              <div className="mt-8 text-right italic text-red-700 font-bold">
                {/* Santa's signature will be in the response */}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg h-full min-h-[400px] p-6 text-center">
              {error ? (
                <div className="text-red-500">{error}</div>
              ) : (
                <div className="text-gray-500 dark:text-gray-400">
                  <p className="mb-2">🎅 Ho Ho Ho! 🎅</p>
                  <p>Santa's response will appear here</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="mt-10 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Tips for Writing to Santa</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>Introduce yourself and your age</li>
          <li>Share some of the good things you've done this year</li>
          <li>Mention a few gifts you'd like to receive</li>
          <li>Ask Santa questions if you're curious about the North Pole</li>
          <li>Don't forget to thank Santa for reading your letter!</li>
        </ul>
      </div>
    </div>
  );
}
