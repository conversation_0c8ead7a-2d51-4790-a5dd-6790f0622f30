"use client";

import { useState, FormEvent, useRef } from "react";
import { Button } from "@/app/components/Button";
import { GEMINI_MODELS } from "@/app/api/gemini/route";

export default function VideoQAPage() {
  const [videoUrl, setVideoUrl] = useState("");
  const [question, setQuestion] = useState("");
  const [answers, setAnswers] = useState<{question: string; answer: string}[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [videoId, setVideoId] = useState<string | null>(null);
  
  const answersEndRef = useRef<HTMLDivElement>(null);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!videoUrl.trim() || !question.trim()) return;

    try {
      setIsLoading(true);
      setError(null);

      // Extract the YouTube video ID if it's a YouTube URL
      const extractedVideoId = extractYouTubeVideoId(videoUrl);
      if (extractedVideoId) {
        setVideoId(extractedVideoId);
      }

      const prompt = createPrompt();
      
      const response = await fetch("/api/gemini", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          prompt,
          model: GEMINI_MODELS.GEMINI_2_5_PRO_PREVIEW
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to get an answer");
      }
      
      const newAnswers = [...answers, { question, answer: data.response }];
      setAnswers(newAnswers);
      setQuestion("");
      
      // Scroll to the latest answer
      setTimeout(() => {
        answersEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    } catch (err: any) {
      setError(err.message || "An error occurred");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const createPrompt = () => {
    let prompt = "You are a helpful assistant that answers questions about video content. ";
    
    prompt += `The user is watching a video at this URL: ${videoUrl}. `;
    
    if (answers.length > 0) {
      prompt += "\n\nPrevious questions and answers about this video:\n";
      answers.forEach((qa, index) => {
        prompt += `\nQ${index + 1}: ${qa.question}\nA${index + 1}: ${qa.answer}\n`;
      });
    }
    
    prompt += `\nBased on what you can infer about this video, please answer the following question:\n\n${question}`;
    
    prompt += "\n\nIf you cannot answer the question based on the video URL and previous context, explain that you may need more information about the video content to provide an accurate answer.";
    
    return prompt;
  };

  const extractYouTubeVideoId = (url: string): string | null => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };

  const clearAll = () => {
    setVideoUrl("");
    setQuestion("");
    setAnswers([]);
    setVideoId(null);
    setError(null);
  };

  const isYouTubeUrl = videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Video Q&A</h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Ask questions about video content and get detailed answers
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Video Details</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="videoUrl" className="block mb-1">
                Video URL:
              </label>
              <input
                id="videoUrl"
                type="url"
                className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                placeholder="https://www.youtube.com/watch?v=..."
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Works best with YouTube videos
              </p>
            </div>

            {isYouTubeUrl && videoId && (
              <div className="aspect-video w-full bg-black rounded-md overflow-hidden">
                <iframe
                  width="100%"
                  height="100%"
                  src={`https://www.youtube.com/embed/${videoId}`}
                  title="YouTube video player"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
            )}

            <div>
              <label htmlFor="question" className="block mb-1">
                Your Question:
              </label>
              <textarea
                id="question"
                className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
                rows={4}
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                placeholder="What's the main topic of this video?"
                required
              />
            </div>

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={isLoading || !videoUrl.trim() || !question.trim()}
                data-loading={isLoading}
              >
                {isLoading ? "Processing..." : "Ask Question"}
              </Button>
              <Button type="button" onClick={clearAll} variant="secondary">
                Clear All
              </Button>
            </div>

            {error && (
              <div className="p-3 bg-red-100 text-red-700 rounded-md mt-4">
                {error}
              </div>
            )}
          </form>
        </div>

        <div className="lg:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md max-h-[800px] overflow-y-auto">
          <h2 className="text-xl font-semibold mb-4">Q&A History</h2>
          
          {answers.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>Your questions and answers will appear here</p>
            </div>
          ) : (
            <div className="space-y-6">
              {answers.map((qa, index) => (
                <div key={index} className="border-b pb-4 dark:border-gray-700">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md mb-2">
                    <p className="font-medium">Q: {qa.question}</p>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                    <p>A: {qa.answer}</p>
                  </div>
                </div>
              ))}
              <div ref={answersEndRef} />
            </div>
          )}
        </div>
      </div>

      <div className="mt-10 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Tips for Effective Video Q&A</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>Use YouTube URLs for the best experience</li>
          <li>Start with general questions about the video topic or content</li>
          <li>Be specific in your questions for more accurate answers</li>
          <li>Follow up with related questions to explore the topic in depth</li>
          <li>Remember that the AI has limited understanding of the actual video content and responds based on inferring what might be in the video based on its URL and your previous questions</li>
        </ul>
      </div>
    </div>
  );
}
