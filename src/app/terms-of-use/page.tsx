'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { Gavel, CheckCircle, XCircle, ShieldCheck, Users, FileText, Key, RefreshCw, DollarSign, Trash2, AlertTriangle, Scale, Globe, Mail } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function TermsOfUsePage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="max-w-4xl mx-auto"
          >
            <motion.div
              variants={itemVariants}
              className="text-center mb-12"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/50 mb-4">
                <Gavel className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-4">
                Terms of Use
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Effective Date: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
              </p>
            </motion.div>

            {/* Section 1: Acceptance of Terms */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="flex items-start gap-4">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                  <CheckCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-3">
                    1. Acceptance of Terms
                  </h2>
                  <p className="text-gray-700 dark:text-gray-300">
                    By accessing or using our website and services, you agree to be bound by these Terms of Use. If you do not agree to all the terms and conditions, you may not access or use our services.
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Section 2: Description of Services */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="flex items-start gap-4">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                  <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-3">
                    2. Description of Services
                  </h2>
                  <p className="text-gray-700 dark:text-gray-300">
                    We provide an online face search engine that allows users to find instances of their own face across the internet. Our service searches publicly available websites to locate images that may contain the user's face, then provides links to those websites.
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Section 3 & 4: Account & Permitted Use */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="grid md:grid-cols-2 gap-8">
                {/* Account Registration */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <Key className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        3. Account Registration
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300 mb-3">
                      To access certain features of our services, you may need to create an account. You agree to:
                    </p>
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                      <li>Provide accurate and complete information</li>
                      <li>Maintain the security of your account credentials</li>
                      <li>Accept responsibility for all activities that occur under your account</li>
                      <li>Notify us immediately of any unauthorized use of your account</li>
                    </ul>
                  </div>
                </div>

                {/* Permitted Use */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <CheckCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        4. Permitted Use
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300 mb-3">
                      You may use our services solely for:
                    </p>
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                      <li>Finding images of yourself on the internet</li>
                      <li>Monitoring your online presence</li>
                      <li>Protecting your privacy and image rights</li>
                      <li>Identifying potential unauthorized use of your likeness</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Section 5: Prohibited Activities */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="flex items-start gap-4">
                <div className="bg-red-100 dark:bg-red-900/50 p-2 rounded-lg mt-1">
                  <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-3">
                    5. Prohibited Activities
                  </h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    You agree not to use our services to:
                  </p>
                  <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800">
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-2">
                      <li>Search for images of other individuals without appropriate authorization</li>
                      <li>Harass, stalk, or conduct surveillance on others</li>
                      <li>Violate any applicable laws or regulations</li>
                      <li>Infringe on the rights of others</li>
                      <li>Attempt to reverse-engineer our technology</li>
                      <li>Use automated methods to access our services</li>
                      <li>Interfere with the proper functioning of our platform</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Sections 6-7: Intellectual Property & User Content */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="grid md:grid-cols-2 gap-8">
                {/* Intellectual Property */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <ShieldCheck className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        6. Intellectual Property
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300">
                      All content, features, and functionality of our services, including but not limited to text, graphics, logos, icons, images, audio clips, digital downloads, and software, are owned by us or our licensors and are protected by copyright, trademark, and other intellectual property laws.
                    </p>
                  </div>
                </div>

                {/* User Content */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        7. User Content
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300 mb-3">
                      By uploading images to our service, you:
                    </p>
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                      <li>Represent that you have the right to use these images</li>
                      <li>Grant us a license to process these images for the purpose of providing our services</li>
                      <li>Understand that we do not claim ownership of your content</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Sections 8-9: Subscription and Termination */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="grid md:grid-cols-2 gap-8">
                {/* Subscription and Fees */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <DollarSign className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        8. Subscription and Fees
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300 mb-3">
                      Some features of our services require paid subscription. By subscribing, you agree:
                    </p>
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                      <li>To pay all applicable fees as they become due</li>
                      <li>That subscriptions automatically renew unless canceled</li>
                      <li>To our refund policy as detailed in this document</li>
                      <li>That we may change our fees upon notice</li>
                    </ul>
                  </div>
                </div>

                {/* Termination */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <Trash2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        9. Termination
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300 mb-3">
                      We reserve the right to suspend or terminate your access to our services if:
                    </p>
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                      <li>You violate these Terms of Use</li>
                      <li>You use our services in a manner that could cause harm</li>
                      <li>We are unable to verify your identity or information</li>
                      <li>Required by law or court order</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Sections 10-12: Legal Provisions */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="grid md:grid-cols-3 gap-6">
                {/* Disclaimer of Warranties */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-3 mb-3">
                    <div className="bg-yellow-100 dark:bg-yellow-900/50 p-2 rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <h2 className="text-lg font-semibold text-[#1d3b6c] dark:text-white">
                      10. Disclaimer of Warranties
                    </h2>
                  </div>
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-3 border border-yellow-200 dark:border-yellow-800 flex-grow text-sm">
                    <p className="text-gray-700 dark:text-gray-300">
                      Our services are provided "as is" and "as available" without warranties of any kind, either express or implied. We do not guarantee that our services will be uninterrupted, error-free, or completely secure.
                    </p>
                  </div>
                </div>

                {/* Limitation of Liability */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-3 mb-3">
                    <div className="bg-yellow-100 dark:bg-yellow-900/50 p-2 rounded-lg">
                      <ShieldCheck className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <h2 className="text-lg font-semibold text-[#1d3b6c] dark:text-white">
                      11. Limitation of Liability
                    </h2>
                  </div>
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-3 border border-yellow-200 dark:border-yellow-800 flex-grow text-sm">
                    <p className="text-gray-700 dark:text-gray-300">
                      To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use or inability to use our services.
                    </p>
                  </div>
                </div>

                {/* Indemnification */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-3 mb-3">
                    <div className="bg-yellow-100 dark:bg-yellow-900/50 p-2 rounded-lg">
                      <Scale className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <h2 className="text-lg font-semibold text-[#1d3b6c] dark:text-white">
                      12. Indemnification
                    </h2>
                  </div>
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-3 border border-yellow-200 dark:border-yellow-800 flex-grow text-sm">
                    <p className="text-gray-700 dark:text-gray-300">
                      You agree to indemnify and hold us harmless from any claims, losses, liabilities, damages, expenses, or costs arising from your use of our services or violation of these Terms.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Sections 13-14: Governance & Changes */}
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="grid md:grid-cols-2 gap-8">
                {/* Governing Law */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        13. Governing Law
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300">
                      These Terms shall be governed by and construed in accordance with the laws of [Jurisdiction], without regard to its conflict of law provisions.
                    </p>
                  </div>
                </div>

                {/* Changes to Terms */}
                <div className="flex flex-col">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg mt-1">
                      <RefreshCw className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                        14. Changes to Terms
                      </h2>
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800 flex-grow">
                    <p className="text-gray-700 dark:text-gray-300">
                      We may modify these Terms at any time. We will notify users of material changes by posting the new Terms on our website. Your continued use of our services after such modifications constitutes acceptance of the updated Terms.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Section 15: Contact Information */}
            <motion.div 
              variants={itemVariants}
              className="bg-blue-50/80 dark:bg-blue-900/20 backdrop-blur-sm rounded-xl border border-blue-200 dark:border-blue-800 shadow-lg p-8 text-center"
            >
              <div className="flex flex-col items-center">
                <div className="bg-white dark:bg-gray-800 p-3 rounded-full shadow-md mb-4">
                  <Mail className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">
                  15. Contact Information
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  If you have any questions about these Terms, please contact us at:
                </p>
                <div className="inline-block bg-white dark:bg-gray-800 px-6 py-3 rounded-lg shadow-sm">
                  <p className="text-blue-600 dark:text-blue-400 font-medium"><EMAIL></p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 