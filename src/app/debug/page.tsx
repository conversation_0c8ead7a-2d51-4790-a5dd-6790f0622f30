"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/app/components/Button";
import { geminiCodeHelper, CodeLanguage, TestingFramework } from "@/app/utils/geminiCodeHelper";

// Programming language options
const LANGUAGES: CodeLanguage[] = [
  "typescript",
  "javascript",
  "python",
  "java",
  "c#",
  "c++",
  "go",
  "rust",
  "php",
  "ruby",
  "html",
  "css",
  "sql",
];

// Testing framework options
const TESTING_FRAMEWORKS: TestingFramework[] = [
  "jest",
  "mocha",
  "vitest",
  "pytest",
  "junit",
  "xunit",
];

// Operations that can be performed on code
type Operation = 
  | "debug" 
  | "optimize" 
  | "explain" 
  | "suggest" 
  | "test" 
  | "complete" 
  | "refactor"
  | "convert"
  | "error"
  | "custom";

// Mapping of operation to its display name
const OPERATIONS: Record<Operation, string> = {
  debug: "Debug Code",
  optimize: "Optimize Code",
  explain: "Explain Code",
  suggest: "Suggest Improvements",
  test: "Generate Tests",
  complete: "Complete Code",
  refactor: "Refactor Code",
  convert: "Convert Code",
  error: "Explain Error",
  custom: "Custom Task",
};

export default function DebugPage() {
  // Code input
  const [code, setCode] = useState("");
  const [operation, setOperation] = useState<Operation>("debug");
  const [language, setLanguage] = useState<CodeLanguage>("typescript");
  const [targetLanguage, setTargetLanguage] = useState<CodeLanguage>("javascript");
  const [testingFramework, setTestingFramework] = useState<TestingFramework>("jest");
  const [customInstruction, setCustomInstruction] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  // Response state
  const [response, setResponse] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Refs
  const responseRef = useRef<HTMLDivElement>(null);

  // Scroll to response when it changes
  useEffect(() => {
    if (response && responseRef.current) {
      responseRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [response]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!code.trim() && operation !== "error") return;
    if (operation === "error" && !errorMessage.trim()) return;
    if (operation === "custom" && !customInstruction.trim()) return;

    try {
      setIsLoading(true);
      setError(null);
      
      let result: string;
      
      switch (operation) {
        case "debug":
          result = await geminiCodeHelper.debugCode(code, language);
          break;
        case "optimize":
          result = await geminiCodeHelper.optimizeCode(code, language);
          break;
        case "explain":
          result = await geminiCodeHelper.explainCode(code, language);
          break;
        case "suggest":
          result = await geminiCodeHelper.suggestImprovements(code, language);
          break;
        case "test":
          result = await geminiCodeHelper.generateTests(code, language, testingFramework);
          break;
        case "complete":
          result = await geminiCodeHelper.completeCode(code, language);
          break;
        case "refactor":
          result = await geminiCodeHelper.refactorCode(code, language);
          break;
        case "convert":
          result = await geminiCodeHelper.convertCode(code, language, targetLanguage);
          break;
        case "error":
          result = await geminiCodeHelper.explainError(errorMessage, code || undefined, language);
          break;
        case "custom":
          result = await geminiCodeHelper.customCodeTask(code, customInstruction, language);
          break;
        default:
          throw new Error("Unknown operation");
      }
      
      setResponse(result);
    } catch (err: any) {
      setError(err.message || "An error occurred");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all inputs and results
  const clearAll = () => {
    setCode("");
    setErrorMessage("");
    setCustomInstruction("");
    setResponse(null);
    setError(null);
  };

  // Copy response to clipboard
  const copyToClipboard = async () => {
    if (response) {
      try {
        await navigator.clipboard.writeText(response);
        alert("Copied to clipboard!");
      } catch (err) {
        console.error("Failed to copy: ", err);
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Gemini Code Assistant</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Input</h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="operation" className="block mb-2 font-medium">
                Operation:
              </label>
              <select
                id="operation"
                className="w-full p-2 border rounded-md bg-white dark:bg-gray-800 dark:border-gray-700"
                value={operation}
                onChange={(e) => setOperation(e.target.value as Operation)}
              >
                {Object.entries(OPERATIONS).map(([value, label]) => (
                  <option key={value} value={value}>
                    {label}
                  </option>
                ))}
              </select>
            </div>

            {operation === "error" ? null : (
              <div>
                <label htmlFor="code" className="block mb-2 font-medium">
                  Code:
                </label>
                <textarea
                  id="code"
                  className="w-full p-2 border rounded-md font-mono text-sm dark:bg-gray-800 dark:border-gray-700"
                  rows={10}
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="Paste your code here..."
                  required={true}
                />
              </div>
            )}

            {operation === "error" && (
              <div>
                <label htmlFor="errorMessage" className="block mb-2 font-medium">
                  Error Message:
                </label>
                <textarea
                  id="errorMessage"
                  className="w-full p-2 border rounded-md font-mono text-sm dark:bg-gray-800 dark:border-gray-700"
                  rows={5}
                  value={errorMessage}
                  onChange={(e) => setErrorMessage(e.target.value)}
                  placeholder="Paste your error message here..."
                  required
                />
              </div>
            )}

            {operation === "custom" && (
              <div>
                <label htmlFor="customInstruction" className="block mb-2 font-medium">
                  Custom Instructions:
                </label>
                <textarea
                  id="customInstruction"
                  className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                  rows={3}
                  value={customInstruction}
                  onChange={(e) => setCustomInstruction(e.target.value)}
                  placeholder="Enter custom instructions for Gemini..."
                  required
                />
              </div>
            )}

            <div>
              <label htmlFor="language" className="block mb-2 font-medium">
                Language:
              </label>
              <select
                id="language"
                className="w-full p-2 border rounded-md bg-white dark:bg-gray-800 dark:border-gray-700"
                value={language}
                onChange={(e) => setLanguage(e.target.value as CodeLanguage)}
              >
                {LANGUAGES.map((lang) => (
                  <option key={lang} value={lang}>
                    {lang.charAt(0).toUpperCase() + lang.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            {operation === "convert" && (
              <div>
                <label htmlFor="targetLanguage" className="block mb-2 font-medium">
                  Target Language:
                </label>
                <select
                  id="targetLanguage"
                  className="w-full p-2 border rounded-md bg-white dark:bg-gray-800 dark:border-gray-700"
                  value={targetLanguage}
                  onChange={(e) => setTargetLanguage(e.target.value as CodeLanguage)}
                >
                  {LANGUAGES.map((lang) => (
                    <option key={lang} value={lang}>
                      {lang.charAt(0).toUpperCase() + lang.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {operation === "test" && (
              <div>
                <label htmlFor="testingFramework" className="block mb-2 font-medium">
                  Testing Framework:
                </label>
                <select
                  id="testingFramework"
                  className="w-full p-2 border rounded-md bg-white dark:bg-gray-800 dark:border-gray-700"
                  value={testingFramework}
                  onChange={(e) => setTestingFramework(e.target.value as TestingFramework)}
                >
                  {TESTING_FRAMEWORKS.map((framework) => (
                    <option key={framework} value={framework}>
                      {framework.charAt(0).toUpperCase() + framework.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={isLoading}
                data-loading={isLoading}
              >
                {isLoading ? "Processing..." : "Get Response"}
              </Button>
              <Button type="button" onClick={clearAll} variant="secondary">
                Clear All
              </Button>
            </div>
          </form>
        </div>

        <div ref={responseRef}>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Gemini Response</h2>
            {response && (
              <button
                onClick={copyToClipboard}
                className="text-sm text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Copy to Clipboard
              </button>
            )}
          </div>
          
          {error && (
            <div className="p-4 mb-4 bg-red-100 text-red-800 rounded-md dark:bg-red-900 dark:text-red-200">
              {error}
            </div>
          )}
          
          {response ? (
            <div className="p-4 border rounded-md whitespace-pre-wrap font-mono text-sm overflow-auto dark:bg-gray-800 dark:border-gray-700">
              {response}
            </div>
          ) : (
            <div className="p-4 border rounded-md text-gray-500 dark:bg-gray-800 dark:border-gray-700">
              Response will appear here
            </div>
          )}
        </div>
      </div>

      <div className="mt-10">
        <h2 className="text-xl font-semibold mb-4">How to Use</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Select an operation from the dropdown (Debug, Optimize, Explain, etc.)</li>
          <li>Paste your code in the text area</li>
          <li>Select the programming language</li>
          <li>If applicable, select additional options like target language or testing framework</li>
          <li>Click "Get Response" to process your request</li>
          <li>View Gemini's response and copy it to your clipboard if needed</li>
        </ol>
      </div>

      <div className="mt-8 p-4 border rounded-md bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
        <h3 className="text-lg font-medium mb-2">Tips</h3>
        <ul className="list-disc pl-5 space-y-1">
          <li>Be specific in your custom instructions to get better results</li>
          <li>Provide complete code snippets with necessary context for more accurate assistance</li>
          <li>For error explanations, include both the error message and the relevant code</li>
          <li>Use "Explain Code" to understand complex pieces of code or unfamiliar patterns</li>
          <li>When debugging, include any error messages or unexpected behaviors you're experiencing</li>
        </ul>
      </div>
    </div>
  );
}
