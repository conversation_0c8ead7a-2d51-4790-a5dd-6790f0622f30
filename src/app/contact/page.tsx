'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { Mail, MessageSquare, Map } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { useState } from "react";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function ContactPage() {
  const [formState, setFormState] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormState({
      ...formState,
      [e.target.name]: e.target.value
    });
  };

  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    // Basic validation
    if (!formState.name.trim() || !formState.email.trim() || !formState.message.trim()) {
      setErrorMessage('Please fill in all required fields.');
      setIsSubmitting(false);
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formState.email)) {
      setErrorMessage('Please enter a valid email address.');
      setIsSubmitting(false);
      return;
    }

    try {
      // Use internal API endpoint instead of Formspree
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(formState)
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus('success');

        // Reset form
        setFormState({
          name: "",
          email: "",
          subject: "",
          message: ""
        });
      } else {
        throw new Error(data.error || "Failed to submit form");
      }
    } catch (error) {
      console.error('Contact form error:', error);
      setSubmitStatus('error');
      setErrorMessage(error instanceof Error ? error.message : "Error sending message. Please try again or email us <NAME_EMAIL>");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="max-w-5xl mx-auto"
          >
            <motion.h1 
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-8 text-center"
            >
              Contact Us
            </motion.h1>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden mb-12"
            >
              <div className="grid md:grid-cols-2">
                <div className="p-8 bg-blue-600 text-white">
                  <h2 className="text-2xl font-semibold mb-6">Get in Touch</h2>
                  <p className="mb-8">We value your feedback and are committed to providing exceptional service. Please don't hesitate to reach out with any questions, concerns, or suggestions you may have about our services.</p>
                  
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <Mail className="h-5 w-5 mr-3 mt-0.5" />
                      <div>
                        <h3 className="font-medium">Email Us</h3>
                        <p className="text-blue-100"><EMAIL></p>
                        <p className="text-sm text-blue-200">Response time: 24 business hours</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <MessageSquare className="h-5 w-5 mr-3 mt-0.5" />
                      <div>
                        <h3 className="font-medium">Live Chat</h3>
                        <p className="text-blue-100">Available Monday-Friday</p>
                        <p className="text-sm text-blue-200">9:00 AM - 6:00 PM (UTC)</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <Map className="h-5 w-5 mr-3 mt-0.5" />
                      <div>
                        <h3 className="font-medium">Mailing Address</h3>
                        <p className="text-blue-100">FaceTrace</p>
                        <p className="text-sm text-blue-200">Miami Beach, Florida<br />United States</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-8 dark:bg-gray-800">
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-6">Send a Message</h2>

                  {/* Success Message */}
                  {submitStatus === 'success' && (
                    <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <div>
                          <h3 className="text-green-800 dark:text-green-200 font-medium">Message sent successfully!</h3>
                          <p className="text-green-700 dark:text-green-300 text-sm">We'll get back to you within 24 business hours.</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Error Message */}
                  {submitStatus === 'error' && errorMessage && (
                    <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <div>
                          <h3 className="text-red-800 dark:text-red-200 font-medium">Error sending message</h3>
                          <p className="text-red-700 dark:text-red-300 text-sm">{errorMessage}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <form className="space-y-4" onSubmit={handleSubmit}>
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Your Name</label>
                      <input 
                        id="name"
                        name="name"
                        type="text" 
                        value={formState.name}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                        placeholder="Full name" 
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Email Address</label>
                      <input 
                        id="email"
                        name="email"
                        type="email" 
                        value={formState.email}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                        placeholder="<EMAIL>" 
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Subject</label>
                      <select 
                        id="subject"
                        name="subject"
                        value={formState.subject}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                        required
                      >
                        <option value="">Select a topic</option>
                        <option value="general">General Inquiry</option>
                        <option value="support">Technical Support</option>
                        <option value="billing">Billing Question</option>
                        <option value="feedback">Product Feedback</option>
                        <option value="privacy">Privacy Concern</option>
                        <option value="partnership">Business Partnership</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Message</label>
                      <textarea 
                        id="message"
                        name="message"
                        value={formState.message}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                        rows={5} 
                        placeholder="How can we help you?"
                        required
                      ></textarea>
                    </div>
                    
                    <div className="pt-2">
                      <button 
                        type="submit" 
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors flex justify-center items-center"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Sending...
                          </>
                        ) : "Send Message"}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              variants={containerVariants}
              className="grid md:grid-cols-3 gap-6 mb-12"
            >
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-[#1d3b6c] dark:text-white mb-3">Technical Support</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">Having issues with our service? Our technical team is ready to help.</p>
                <p className="text-blue-600 dark:text-blue-400"><EMAIL></p>
              </motion.div>
              
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-[#1d3b6c] dark:text-white mb-3">Privacy Concerns</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">For matters related to your personal data or privacy inquiries.</p>
                <p className="text-blue-600 dark:text-blue-400"><EMAIL></p>
              </motion.div>
              
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-[#1d3b6c] dark:text-white mb-3">Business Inquiries</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">For partnerships, press, or business development opportunities.</p>
                <p className="text-blue-600 dark:text-blue-400"><EMAIL></p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 