'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { SearchResult } from '@/app/services/apiService';
import { ResultCard } from '@/components/ResultCard';
import { ResultCardSkeleton } from '@/components/ResultCardSkeleton';
import UnlockModal from '@/components/UnlockModal';
import { useAuth } from '@clerk/nextjs';

// Define the structure of the API response 
interface ReportResponse {
  id: number;
  status: 'processing' | 'completed' | 'failed';
  progress?: number;
  results?: any[]; 
  facecheckIdSearch?: string;
  error?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  expiresAt?: string | Date;
  isPrivate?: boolean;
  locked?: boolean;
  domainStats?: Array<{domain: string, count: number}>;
  resultCount?: number;
}

// Helper function to format expiration time
function formatExpirationTime(expiresAt: Date | string): string {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  
  const timeDiff = expiryDate.getTime() - now.getTime();
  
  if (timeDiff <= 0) {
    return "Expired";
  }
  
  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  
  let result = "";
  if (days > 0) {
    result += `${days}d `;
  }
  if (hours > 0 || days > 0) {
    result += `${hours}h `;
  }
  result += `${minutes}m`;
  
  return result;
}

// Get expiration status color
function getExpirationColor(expiresAt: Date | string): string {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  
  const hoursDiff = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  if (hoursDiff <= 6) {
    return "text-red-500";
  } else if (hoursDiff <= 24) {
    return "text-amber-500";
  } else {
    return "text-green-500";
  }
}

export default function ReportPage() {
  const params = useParams();
  const router = useRouter();
  const { isLoaded: isAuthLoaded, userId } = useAuth();
  const reportIdentifier = params?.id as string;
  
  // State variables
  const [reportData, setReportData] = useState<ReportResponse | null>(null);
  const [isPrivate, setIsPrivate] = useState<boolean>(false);
  const [isUnlockModalOpen, setIsUnlockModalOpen] = useState<boolean>(false);
  const [domainStats, setDomainStats] = useState<Array<{domain: string, count: number}>>([]);
  const [resultCount, setResultCount] = useState<number>(0);
  const [status, setStatus] = useState<'loading' | 'expired' | 'error' | 'completed' | 'processing'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [expiresAt, setExpiresAt] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  // Update the timer every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    
    return () => clearInterval(timer);
  }, []);

  // Handle unlocking the report
  const handleUnlockReport = () => {
    if (!reportData) {
      console.error('[Report Page] Cannot unlock report: reportData is undefined');
      return;
    }
    
    setIsUnlockModalOpen(true);
  };

  // Handle exporting report as PDF
  const handleExportPDF = () => {
    // PDF Export functionality (to be implemented)
    alert('PDF export functionality coming soon!');
  };

  // Handle removal request
  const handleRemovalRequest = () => {
    // Removal request functionality (to be implemented)
    alert('Removal request feature coming soon!');
  };

  // Handle deleting report
  const handleDeleteReport = () => {
    if (confirm("Are you sure you want to delete this report? This action cannot be undone.")) {
      // Delete report functionality (to be implemented)
      // For now, just navigate back to search page
      router.push('/search');
    }
  };

  // Handle when unlock modal is closed
  const handleUnlockModalClose = (success: boolean) => {
    setIsUnlockModalOpen(false);
    
    if (success) {
      router.refresh();
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  };

  // Fetch report data on component mount
  useEffect(() => {
    async function fetchReportData() {
      if (!reportIdentifier) return;
      
      try {
        const response = await fetch(`/api/reports/${reportIdentifier}/results`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch report: ${response.status}`);
        }
        
        const data: ReportResponse = await response.json();
        setReportData(data);

        // Check if the report is locked/private
        const reportIsPrivate = data.isPrivate || data.locked || false;
        setIsPrivate(reportIsPrivate);
        
        // For locked reports, store domain stats and result count
        if (reportIsPrivate) {
          if (data.domainStats && Array.isArray(data.domainStats)) {
            setDomainStats(data.domainStats);
          }
          
          if (data.resultCount !== undefined) {
            setResultCount(data.resultCount);
          } else if (data.results && Array.isArray(data.results)) {
            setResultCount(data.results.length);
          }
        }
        
        // Save expiration date
        if (data.expiresAt) {
          setExpiresAt(data.expiresAt.toString());
          
          const expiryDate = new Date(data.expiresAt);
          const now = new Date();
          
          if (now > expiryDate) {
            setStatus('expired');
            return;
          }
        }
        
        // Set progress and status from report data
        setProgress(data.progress || 0);
        setStatus(data.status === 'processing' ? 'processing' : 'completed');
        
        // Process and use facecheckIdSearch data if available
        if (data.facecheckIdSearch) {
          try {
            const storedResults = JSON.parse(data.facecheckIdSearch);
            if (Array.isArray(storedResults) && storedResults.length > 0) {
              const processedResults = storedResults.map((item: any) => ({
                id: item.id?.toString() || '',
                confidence: item.confidence || 0,
                sourceUrl: item.sourceUrl || item.url || '',
                thumbnail: item.thumbnail || '',
                title: item.title || 'Unknown',
                description: item.description || '',
                sourceType: item.sourceType || 'other'
              }));
              
              setResults(processedResults);
              setStatus('completed');
            } else if (data.status === 'processing') {
              setStatus('processing');
            } else {
              setStatus('completed');
            }
          } catch (error) {
            console.error('Error parsing report results:', error);
            setStatus('error');
            setErrorMessage('Failed to parse report data');
          }
        } else if (data.results && Array.isArray(data.results) && data.results.length > 0) {
          const processedResults = data.results.map((item: any) => ({
            id: item.id?.toString() || '',
            confidence: item.confidence || 0,
            sourceUrl: item.sourceUrl || item.url || '',
            thumbnail: item.thumbnail || '',
            title: item.title || 'Unknown',
            description: item.description || '',
            sourceType: item.sourceType || 'other'
          }));
          
          setResults(processedResults);
          setStatus('completed');
        } else if (data.status === 'failed') {
          setStatus('error');
          setErrorMessage(data.error || 'Report processing failed');
        } else if (data.status === 'processing') {
          setStatus('processing');
        } else {
          setStatus('completed');
        }
      } catch (error) {
        console.error('Error fetching report:', error);
        setStatus('error');
        setErrorMessage(error instanceof Error ? error.message : 'An unknown error occurred');
      }
    }
    
    fetchReportData();
  }, [reportIdentifier]);

  // Categorize results by confidence
  const categorizedResults = {
    all: results,
    certain: results.filter(r => r.confidence >= 90),
    confident: results.filter(r => r.confidence >= 83 && r.confidence < 90),
    uncertain: results.filter(r => r.confidence >= 70 && r.confidence < 83),
    weak: results.filter(r => r.confidence >= 50 && r.confidence < 70)
  };

  // Get filtered results based on selected category
  const getFilteredResults = () => {
    return categorizedResults[selectedCategory as keyof typeof categorizedResults] || results;
  };
  
  // Animation variants for framer-motion
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Unlock Modal */}
      {reportData && (
        <UnlockModal 
          isOpen={isUnlockModalOpen} 
          onClose={handleUnlockModalClose} 
          reportId={reportData.id}
          reportIdentifier={reportIdentifier}
        />
      )}
      
      <main className="container mx-auto px-4 py-8">
        {/* Header Section - Always visible */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Face Analysis Report</h1>
            <p className="text-sm text-gray-500">ID: {reportIdentifier}</p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Link
              href="/search"
              className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              New Search
            </Link>
            
            {status === 'completed' && results && results.length > 0 && (
              <>
                <button
                  className="px-3 py-2 bg-gray-200 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-300 transition flex items-center"
                  onClick={handleExportPDF}
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  Export PDF
                </button>
                
                <button
                  className="px-3 py-2 bg-gray-200 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-300 transition flex items-center"
                  onClick={handleRemovalRequest}
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Removal Request
                </button>
                
                <button
                  className="px-3 py-2 bg-gray-200 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-300 transition flex items-center"
                  onClick={handleDeleteReport}
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Delete Report
                </button>
              </>
            )}
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Main Content Area - Changes based on status */}
          <motion.div
            key={status}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={fadeIn}
            transition={{ duration: 0.3 }}
          >
            {/* Loading State */}
            {status === 'loading' && (
              <div className="space-y-6">
                {/* Skeleton Summary Card */}
                <div className="bg-white rounded-xl shadow-md p-6 mb-6 animate-pulse">
                  <div className="flex flex-col md:flex-row justify-between">
                    <div className="space-y-4">
                      <div className="h-6 bg-gray-200 rounded w-40"></div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                          <div className="h-6 bg-gray-200 rounded w-12"></div>
                        </div>
                        <div>
                          <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                          <div className="h-6 bg-gray-200 rounded w-12"></div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 md:mt-0 border rounded-lg p-4 bg-gray-50 animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-24"></div>
                    </div>
                  </div>
                </div>
                
                {/* Skeleton Results Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <ResultCardSkeleton key={i} />
                  ))}
                </div>
              </div>
            )}
            
            {/* Processing State */}
            {status === 'processing' && (
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="flex flex-col items-center justify-center">
                  <div className="w-16 h-16 mb-4">
                    <svg className="animate-spin w-full h-full text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Processing Your Search</h3>
                  <p className="text-gray-600 text-center mb-4">
                    Please wait while we search... This may take a minute or two.
                  </p>
                  
                  {/* Progress Bar */}
                  <div className="w-full max-w-md bg-gray-200 rounded-full h-2 mb-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${progress}%` }}></div>
                  </div>
                  <p className="text-sm text-gray-500 mb-4">{progress}% Complete</p>
                  
                  <button 
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
                  >
                    Refresh Status
                  </button>
                </div>
              </div>
            )}
            
            {/* Expired State */}
            {status === 'expired' && (
              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <svg className="w-16 h-16 text-amber-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Report Expired</h3>
                <p className="text-gray-600 max-w-md mx-auto mb-6">
                  For privacy and security reasons, reports expire after 24 hours. Please conduct a new search if you need this information.
                </p>
                <Link 
                  href="/search"
                  className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition inline-block"
                >
                  New Search
                </Link>
              </div>
            )}
            
            {/* Error State */}
            {status === 'error' && (
              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Something Went Wrong</h3>
                <p className="text-gray-600 max-w-md mx-auto mb-4">
                  {errorMessage || 'An unexpected error occurred while loading your report.'}
                </p>
                <div className="flex justify-center gap-4">
                  <button 
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium rounded-lg transition"
                  >
                    Refresh
                  </button>
                  <Link 
                    href="/search"
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition inline-block"
                  >
                    New Search
                  </Link>
                </div>
              </div>
            )}
            
            {/* Completed State with Results */}
            {status === 'completed' && results && results.length > 0 && (
              <div className="space-y-6">
                {/* Report Information Card */}
                <div className="bg-white rounded-xl shadow-md p-6 mb-6">
                  <div className="flex flex-col md:flex-row justify-between">
                    <div>
                      <h2 className="text-xl font-bold text-gray-900 mb-2">Report Summary</h2>
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <span className="text-sm text-gray-500">Total Matches:</span>
                          <p className="text-lg font-semibold">{results.length}</p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Strongest Match:</span>
                          <p className="text-lg font-semibold">{Math.max(...results.map(r => r.confidence)).toFixed(1)}%</p>
                        </div>
                      </div>
                      
                      {/* Domain Stats Preview (only for locked reports) */}
                      {isPrivate && domainStats && domainStats.length > 0 && (
                        <div className="mt-4">
                          <span className="text-sm text-gray-500">Top Sources:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {domainStats.map((stat, index) => (
                              <div key={index} className="bg-gray-100 px-2 py-1 rounded text-xs text-gray-700">
                                {stat.domain} ({stat.count})
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {/* Expiration Timer */}
                    {expiresAt && (
                      <div className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center mb-2">
                          <svg className={`w-5 h-5 ${getExpirationColor(expiresAt)} mr-2`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <span className="text-sm font-medium text-gray-700">Report Expires In:</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className={`text-lg font-bold ${getExpirationColor(expiresAt)}`}>
                            {formatExpirationTime(expiresAt)}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Filter Tabs */}
                  <div className="mt-6">
                    <div className="border-b border-gray-200">
                      <nav className="flex space-x-8 overflow-x-auto">
                        <button
                          onClick={() => setSelectedCategory('all')}
                          className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                            selectedCategory === 'all'
                              ? 'border-blue-500 text-blue-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          All Results ({results.length})
                        </button>
                        <button
                          onClick={() => setSelectedCategory('certain')}
                          className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                            selectedCategory === 'certain'
                              ? 'border-green-500 text-green-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          Certain (90%+) ({categorizedResults.certain.length})
                        </button>
                        <button
                          onClick={() => setSelectedCategory('confident')}
                          className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                            selectedCategory === 'confident'
                              ? 'border-green-500 text-green-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          Confident (83-89%) ({categorizedResults.confident.length})
                        </button>
                        <button
                          onClick={() => setSelectedCategory('uncertain')}
                          className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                            selectedCategory === 'uncertain'
                              ? 'border-yellow-500 text-yellow-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          Uncertain (70-82%) ({categorizedResults.uncertain.length})
                        </button>
                        <button
                          onClick={() => setSelectedCategory('weak')}
                          className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                            selectedCategory === 'weak'
                              ? 'border-red-500 text-red-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          Weak (50-69%) ({categorizedResults.weak.length})
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
                
                {/* Locked Report Banner */}
                <AnimatePresence>
                  {isPrivate && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="bg-amber-50 border border-amber-200 rounded-xl p-4 mb-6"
                    >
                      <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                        <div className="flex items-center">
                          <svg className="w-6 h-6 text-amber-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>
                          <div>
                            <h3 className="font-bold text-amber-800">Locked Report</h3>
                            <p className="text-amber-700 text-sm">URLs and source details are hidden in preview mode</p>
                          </div>
                        </div>
                        
                        <button 
                          onClick={handleUnlockReport}
                          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold py-2 px-5 rounded-lg shadow hover:shadow-lg transition"
                        >
                          Unlock Full Results ($5)
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
                
                {/* Results Grid */}
                <AnimatePresence>
                  <motion.div
                    key={`results-${selectedCategory}-${isPrivate}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                  >
                    {getFilteredResults().map(result => (
                      <ResultCard 
                        key={result.id}
                        result={result}
                        isPrivate={isPrivate}
                        onUnlockRequest={handleUnlockReport}
                      />
                    ))}
                  </motion.div>
                </AnimatePresence>
                
                {/* No Results for Selected Category */}
                <AnimatePresence>
                  {getFilteredResults().length === 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="bg-white rounded-xl shadow p-8 text-center"
                    >
                      <p className="text-gray-600">No results found in the selected category.</p>
                      <button
                        onClick={() => setSelectedCategory('all')}
                        className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
                      >
                        View All Results
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
            
            {/* Completed State with No Results */}
            {status === 'completed' && (!results || results.length === 0) && (
              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 10l4 4m0-4l-4 4"></path>
                </svg>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Matches Found</h3>
                <p className="text-gray-600 max-w-md mx-auto mb-6">
                  We couldn't find any matches for this image in our database. This could mean this face hasn't appeared online, or the matches are below our confidence threshold.
                </p>
                <Link 
                  href="/search"
                  className="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition inline-block"
                >
                  Try Another Search
                </Link>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </main>
    </div>
  );
} 