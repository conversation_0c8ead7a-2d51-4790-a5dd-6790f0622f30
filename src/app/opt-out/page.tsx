// src/app/opt-out/page.tsx
/* eslint-disable */
'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { useState, useRef } from "react";
import { Shield, Upload, FileText, Check, ChevronRight, ChevronLeft, Info } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";
import Image from "next/image";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

// Step types
type Step = "info" | "personal" | "photos" | "confirm";

export default function OptOutPage() {
  // Form state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [facePhotoFile, setFacePhotoFile] = useState<File | null>(null);
  const [idPhotoFile, setIdPhotoFile] = useState<File | null>(null);
  const [facePhotoPreview, setFacePhotoPreview] = useState<string | null>(null);
  const [idPhotoPreview, setIdPhotoPreview] = useState<string | null>(null);
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [checkbox1, setCheckbox1] = useState(false);
  const [checkbox2, setCheckbox2] = useState(false);
  const [checkbox3, setCheckbox3] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  
  // Multi-step form state
  const [currentStep, setCurrentStep] = useState<Step>("info");
  const steps: Step[] = ["info", "personal", "photos", "confirm"];
  
  const formRef = useRef<HTMLFormElement>(null);
  
  // Check if form is valid
  React.useEffect(() => {
    const valid = 
      fullName.trim() !== "" && 
      email.trim() !== "" && 
      facePhotoFile !== null && 
      idPhotoFile !== null && 
      checkbox1 && 
      checkbox2 && 
      checkbox3;
    
    setIsFormValid(valid);
  }, [fullName, email, facePhotoFile, idPhotoFile, checkbox1, checkbox2, checkbox3]);
  
  // Handle face photo upload
  const handleFacePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFacePhotoFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setFacePhotoPreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle ID photo upload
  const handleIdPhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setIdPhotoFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setIdPhotoPreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  const [submitError, setSubmitError] = useState<string>('');

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitError('');

    if (!isFormValid) {
      setSubmitError("Please complete all required fields and upload both photos");
      return;
    }

    // Additional validation
    if (!fullName.trim() || fullName.trim().length < 2) {
      setSubmitError("Please enter a valid full name (at least 2 characters)");
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setSubmitError("Please enter a valid email address");
      return;
    }

    if (!facePhotoFile || !idPhotoFile) {
      setSubmitError("Please upload both a face photo and ID photo");
      return;
    }

    // Check file sizes (max 10MB each)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (facePhotoFile.size > maxSize || idPhotoFile.size > maxSize) {
      setSubmitError("Each photo must be smaller than 10MB");
      return;
    }

    // Check file types
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(facePhotoFile.type) || !allowedTypes.includes(idPhotoFile.type)) {
      setSubmitError("Please upload only JPEG, PNG, or WebP images");
      return;
    }

    setIsSubmitting(true);

    try {
      // Create form data to submit
      const formData = new FormData();
      formData.append('fullName', fullName.trim());
      formData.append('email', email.trim());
      formData.append('facePhotoFile', facePhotoFile);
      formData.append('idPhotoFile', idPhotoFile);

      // Submit to the API endpoint
      const response = await fetch('/api/data?action=opt-out', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit opt-out request');
      }

      setIsSuccess(true);

      // Reset form
      if (formRef.current) {
        formRef.current.reset();
      }
      setFacePhotoFile(null);
      setIdPhotoFile(null);
      setFacePhotoPreview(null);
      setIdPhotoPreview(null);
      setFullName('');
      setEmail('');
      setCheckbox1(false);
      setCheckbox2(false);
      setCheckbox3(false);
      setCurrentStep('info');

    } catch (error: unknown) {
      const err = error as Error;
      console.error("Error submitting opt-out request:", err);
      setSubmitError(err.message || 'Failed to submit opt-out request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Check if step is complete
  const isStepComplete = (step: Step): boolean => {
    switch (step) {
      case "info":
        return true; // Info step is always complete
      case "personal":
        return fullName.trim() !== "" && email.trim() !== "";
      case "photos":
        return facePhotoFile !== null && idPhotoFile !== null;
      case "confirm":
        return checkbox1 && checkbox2 && checkbox3;
      default:
        return false;
    }
  };
  
  // Get next step
  const getNextStep = (): Step | null => {
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      return steps[currentIndex + 1];
    }
    return null;
  };
  
  // Get previous step
  const getPreviousStep = (): Step | null => {
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      return steps[currentIndex - 1];
    }
    return null;
  };
  
  // Handle next step
  const handleNextStep = () => {
    const nextStep = getNextStep();
    if (nextStep && isStepComplete(currentStep)) {
      setCurrentStep(nextStep);
    }
  };
  
  // Handle previous step
  const handlePreviousStep = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      setCurrentStep(previousStep);
    }
  };

  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="max-w-7xl mx-auto"
          >
            <motion.div 
              variants={itemVariants}
              className="flex items-center justify-center mb-6"
            >
              <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
              <h1 className="text-3xl font-bold text-[#1d3b6c] dark:text-white">Opt-Out Request</h1>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8"
            >
              {/* Progress Steps */}
              <div className="flex border-b border-gray-200 dark:border-gray-700">
                {steps.map((step, index) => (
                  <button
                    key={step}
                    className={`
                      flex-1 py-3 px-2 text-sm font-medium transition-colors
                      ${currentStep === step 
                        ? "bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-b-2 border-blue-500" 
                        : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"}
                      ${isStepComplete(step) && currentStep !== step ? "text-blue-600 dark:text-blue-400" : ""}
                    `}
                    onClick={() => isStepComplete(step) ? setCurrentStep(step) : null}
                    disabled={!isStepComplete(step)}
                  >
                    <span className="hidden md:inline">{index + 1}. </span>
                    {step === "info" && "Information"}
                    {step === "personal" && "Personal Details"}
                    {step === "photos" && "Upload Photos"}
                    {step === "confirm" && "Confirmation"}
                  </button>
                ))}
              </div>
              
              <div className="p-6">
                {isSuccess ? (
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 text-center"
                  >
                    <div className="flex justify-center mb-4">
                      <div className="bg-green-100 dark:bg-green-800 rounded-full p-3">
                        <Check className="h-12 w-12 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                    <h2 className="text-xl font-semibold text-green-800 dark:text-green-300 mb-2">Opt-Out Request Submitted</h2>
                    <p className="text-green-700 dark:text-green-400 mb-4">
                      Thank you for your request. We've received your opt-out submission and will process it within 7 business days.
                    </p>
                    <p className="text-green-700 dark:text-green-400">
                      You will receive a confirmation email when your request has been processed.
                    </p>
                  </motion.div>
                ) : (
                  <form onSubmit={handleSubmit} ref={formRef}>
                    {/* Error Message */}
                    {submitError && (
                      <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div className="flex items-center">
                          <svg className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                          </svg>
                          <div>
                            <h3 className="text-red-800 dark:text-red-200 font-medium">Error</h3>
                            <p className="text-red-700 dark:text-red-300 text-sm">{submitError}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Step 1: Information */}
                    {currentStep === "info" && (
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 text-blue-700 dark:text-blue-400 mb-2">
                          <Info className="h-5 w-5" />
                          <h2 className="text-lg font-semibold">Important Information</h2>
                        </div>
                        
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          We respect your privacy rights and provide this opt-out mechanism to exclude your face from our search results. 
                          Complete this form to request removal of your facial data from our search index.
                        </p>
                        
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg p-4">
                          <ul className="list-disc pl-5 space-y-1 text-sm text-blue-700 dark:text-blue-400">
                            <li>This process will remove your facial data from our search index</li>
                            <li>This does not remove original images from their source websites</li>
                            <li>Verification is required to prevent unauthorized opt-out requests</li>
                            <li>Processing may take up to 7 business days to complete</li>
                          </ul>
                        </div>
                        
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg p-4">
                          <h3 className="text-sm font-semibold text-blue-700 dark:text-blue-400 mb-2 flex items-center">
                            <Check className="h-4 w-4 mr-1.5 text-blue-600 dark:text-blue-400" />
                            What Happens Next?
                          </h3>
                          <ol className="list-decimal pl-5 space-y-1 text-sm text-blue-700 dark:text-blue-400">
                            <li>We'll review your request within 3 business days</li>
                            <li>You'll receive a confirmation email when processing begins</li>
                            <li>Your facial data will be removed from our search index</li>
                            <li>You'll receive a final notification when the process is complete</li>
                          </ol>
                        </div>
                      </div>
                    )}
                    
                    {/* Step 2: Personal Details */}
                    {currentStep === "personal" && (
                      <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-blue-700 dark:text-blue-400 mb-2">Your Information</h2>
                        
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Full Name</label>
                            <input 
                              type="text" 
                              name="fullName"
                              className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                              required 
                              value={fullName}
                              onChange={(e) => setFullName(e.target.value)}
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Email Address</label>
                            <input 
                              type="email" 
                              name="email"
                              className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                              required 
                              value={email}
                              onChange={(e) => setEmail(e.target.value)}
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Reason for Opt-Out (Optional)</label>
                          <select 
                            name="reason"
                            className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md"
                          >
                            <option value="">Select a reason (optional)</option>
                            <option value="privacy">Privacy concerns</option>
                            <option value="security">Security concerns</option>
                            <option value="harassment">Harassment prevention</option>
                            <option value="professional">Professional reasons</option>
                            <option value="other">Other</option>
                          </select>
                        </div>
                      </div>
                    )}
                    
                    {/* Step 3: Photo Uploads */}
                    {currentStep === "photos" && (
                      <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-blue-700 dark:text-blue-400 mb-2">Upload Photos</h2>
                        
                        <div className="grid md:grid-cols-2 gap-4">
                          {/* Face Photo Upload */}
                          <div>
                            <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Your Photo</label>
                            <div className="border-2 border-dashed border-blue-300 dark:border-blue-800 rounded-lg p-4 text-center relative">
                              <input 
                                type="file" 
                                className="hidden" 
                                id="face-photo-upload" 
                                accept=".jpg,.jpeg,.png,.heic,.pdf,.webp"
                                onChange={handleFacePhotoChange}
                              />
                              <label 
                                htmlFor="face-photo-upload"
                                className="cursor-pointer flex flex-col items-center justify-center"
                              >
                                {facePhotoPreview ? (
                                  <>
                                    <div className="relative h-24 w-24 mx-auto mb-2">
                                      <Image 
                                        src={facePhotoPreview} 
                                        alt="Face preview" 
                                        fill
                                        className="rounded-md object-cover"
                                      />
                                    </div>
                                    <p className="text-blue-700 dark:text-blue-400 text-xs truncate max-w-full">{facePhotoFile?.name}</p>
                                  </>
                                ) : (
                                  <>
                                    <Upload className="h-8 w-8 text-blue-400 dark:text-blue-500 mb-2" />
                                    <p className="text-blue-700 dark:text-blue-400 text-xs">Upload your photo</p>
                                  </>
                                )}
                              </label>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Accepted formats: JPG, PNG, HEIC, JPEG, PDF, WEBP (Max 50MB)</p>
                          </div>
                          
                          {/* ID Photo Upload */}
                          <div>
                            <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">ID Verification</label>
                            <div className="border-2 border-dashed border-blue-300 dark:border-blue-800 rounded-lg p-4 text-center relative">
                              <input 
                                type="file" 
                                className="hidden" 
                                id="id-upload" 
                                accept=".jpg,.jpeg,.png,.heic,.pdf,.webp"
                                onChange={handleIdPhotoChange}
                              />
                              <label 
                                htmlFor="id-upload"
                                className="cursor-pointer flex flex-col items-center justify-center"
                              >
                                {idPhotoPreview ? (
                                  <>
                                    <div className="relative h-24 w-24 mx-auto mb-2">
                                      <Image 
                                        src={idPhotoPreview} 
                                        alt="ID preview" 
                                        fill
                                        className="rounded-md object-cover"
                                      />
                                    </div>
                                    <p className="text-blue-700 dark:text-blue-400 text-xs truncate max-w-full">{idPhotoFile?.name}</p>
                                  </>
                                ) : (
                                  <>
                                    <FileText className="h-8 w-8 text-blue-400 dark:text-blue-500 mb-2" />
                                    <p className="text-blue-700 dark:text-blue-400 text-xs">Upload redacted ID</p>
                                  </>
                                )}
                              </label>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Redact all personal info except name and photo</p>
                          </div>
                        </div>
                        
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg p-3 text-xs text-blue-700 dark:text-blue-400">
                          <p className="font-medium mb-1">Important:</p>
                          <ul className="list-disc pl-5 space-y-1">
                            <li>Choose a well-lit, front-facing photo</li>
                            <li>Redact (black out) all personal information on ID except your name and photo</li>
                            <li>Accepted formats: JPG, PNG, HEIC, JPEG, PDF, WEBP (Max 50MB)</li>
                          </ul>
                        </div>
                      </div>
                    )}
                    
                    {/* Step 4: Confirmation */}
                    {currentStep === "confirm" && (
                      <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-blue-700 dark:text-blue-400 mb-2">Confirmation</h2>
                        
                        <div className="space-y-3">
                          <label className="flex items-start">
                            <input 
                              type="checkbox" 
                              className="mt-1 h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800 rounded" 
                              required 
                              checked={checkbox1}
                              onChange={(e) => setCheckbox1(e.target.checked)}
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                              I confirm that I am the person in the uploaded photo and that I want to opt out of FaceTrace's 
                              facial recognition search results.
                            </span>
                          </label>
                          
                          <label className="flex items-start">
                            <input 
                              type="checkbox" 
                              className="mt-1 h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800 rounded" 
                              required 
                              checked={checkbox2}
                              onChange={(e) => setCheckbox2(e.target.checked)}
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                              I understand that this process removes my facial data from the FaceTrace search index, but does not 
                              remove original images from their source websites.
                            </span>
                          </label>
                          
                          <label className="flex items-start">
                            <input 
                              type="checkbox" 
                              className="mt-1 h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800 rounded" 
                              required 
                              checked={checkbox3}
                              onChange={(e) => setCheckbox3(e.target.checked)}
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                              I understand that FaceTrace will use the information I provide solely for the purpose of processing 
                              my opt-out request and will delete verification documents after processing.
                            </span>
                          </label>
                        </div>
                        
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg p-4">
                          <h3 className="text-sm font-semibold text-blue-700 dark:text-blue-400 mb-2">Review Your Information</h3>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="text-gray-500 dark:text-gray-400">Name:</div>
                            <div className="font-medium text-gray-700 dark:text-gray-300">{fullName}</div>
                            <div className="text-gray-500 dark:text-gray-400">Email:</div>
                            <div className="font-medium text-gray-700 dark:text-gray-300">{email}</div>
                            <div className="text-gray-500 dark:text-gray-400">Face Photo:</div>
                            <div className="font-medium text-gray-700 dark:text-gray-300">{facePhotoFile?.name || "None"}</div>
                            <div className="text-gray-500 dark:text-gray-400">ID Photo:</div>
                            <div className="font-medium text-gray-700 dark:text-gray-300">{idPhotoFile?.name || "None"}</div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Navigation Buttons */}
                    <div className="flex justify-between mt-6">
                      {getPreviousStep() ? (
                        <button
                          type="button"
                          onClick={handlePreviousStep}
                          className="flex items-center gap-1 px-4 py-2 border border-blue-300 dark:border-blue-800 rounded-md text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Back
                        </button>
                      ) : (
                        <div></div> // Empty div for spacing
                      )}
                      
                      {getNextStep() ? (
                        <button
                          type="button"
                          onClick={handleNextStep}
                          disabled={!isStepComplete(currentStep)}
                          className={`
                            flex items-center gap-1 px-4 py-2 rounded-md transition-colors
                            ${isStepComplete(currentStep) 
                              ? "bg-blue-600 hover:bg-blue-700 text-white" 
                              : "bg-blue-300 dark:bg-blue-800 text-white cursor-not-allowed"}
                          `}
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </button>
                      ) : (
                        <button
                          type="submit"
                          disabled={isSubmitting || !isFormValid}
                          className={`
                            px-4 py-2 rounded-md transition-colors
                            ${isFormValid && !isSubmitting
                              ? "bg-blue-600 hover:bg-blue-700 text-white" 
                              : "bg-blue-300 dark:bg-blue-800 text-white cursor-not-allowed"}
                          `}
                        >
                          {isSubmitting ? (
                            <>
                              <svg className="animate-spin inline-block h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Processing...
                            </>
                          ) : (
                            "Submit Request"
                          )}
                        </button>
                      )}
                    </div>
                  </form>
                )}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
