'use client';

import * as React from "react";
import { motion } from "framer-motion";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function DMCATakedownPage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="prose prose-blue dark:prose-invert max-w-4xl mx-auto"
          >
            <motion.h1 
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-8"
            >
              DMCA Takedown Request
            </motion.h1>
            
            <motion.p 
              variants={itemVariants}
              className="text-gray-600 dark:text-gray-300 mb-4"
            >
              Effective Date: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
            </motion.p>
            
            <motion.h2 
              variants={itemVariants}
              className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mt-6 mb-4"
            >
              DMCA Takedown Request Policy
            </motion.h2>
            
            <motion.div variants={containerVariants}>
              <motion.h3 
                variants={itemVariants}
                className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mt-4 mb-2"
              >
                Purpose
              </motion.h3>
              <motion.p 
                variants={itemVariants}
                className="text-gray-600 dark:text-gray-300"
              >
                This policy outlines the procedure for submitting Digital Millennium Copyright Act (DMCA) takedown requests related to content that allegedly infringes upon your copyrighted work.
              </motion.p>
              
              <motion.h3 
                variants={itemVariants}
                className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mt-4 mb-2"
              >
                What is the DMCA?
              </motion.h3>
              <motion.p 
                variants={itemVariants}
                className="text-gray-600 dark:text-gray-300"
              >
                The Digital Millennium Copyright Act (DMCA) provides a legal framework for copyright owners to request the removal of their copyrighted content from websites if they believe it has been used without authorization.
              </motion.p>
              
              <motion.h3 
                variants={itemVariants}
                className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mt-4 mb-2"
              >
                How to Submit a DMCA Takedown Request
              </motion.h3>
              <motion.p 
                variants={itemVariants}
                className="text-gray-600 dark:text-gray-300"
              >
                If you believe that content available through our service infringes on your copyright, please submit a notification containing the following information:
              </motion.p>
              <motion.ol 
                variants={containerVariants}
                className="list-decimal pl-6 mb-4 text-gray-600 dark:text-gray-300"
              >
                <motion.li variants={itemVariants}>Identification of the copyrighted work: A description of the copyrighted work that you claim has been infringed.</motion.li>
                <motion.li variants={itemVariants}>Identification of the material: The specific URL or other information sufficient to allow us to locate the material you claim is infringing.</motion.li>
                <motion.li variants={itemVariants}>Your contact information: Your address, telephone number, and email address.</motion.li>
                <motion.li variants={itemVariants}>Statement of good faith belief: A statement that you have a good faith belief that the use of the material in the manner complained of is not authorized by the copyright owner, its agent, or the law.</motion.li>
                <motion.li variants={itemVariants}>Statement of accuracy and authorization: A statement, under penalty of perjury, that the information in your notification is accurate and that you are the copyright owner or authorized to act on behalf of the copyright owner.</motion.li>
                <motion.li variants={itemVariants}>Your physical or electronic signature: A handwritten or digital signature of the copyright owner or a person authorized to act on their behalf.</motion.li>
              </motion.ol>
              
              <motion.h3 
                variants={itemVariants}
                className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mt-4 mb-2"
              >
                Submission Methods
              </motion.h3>
              <motion.p 
                variants={itemVariants}
                className="text-gray-600 dark:text-gray-300"
              >
                Submit your DMCA request through one of the following methods:
              </motion.p>
              <motion.ul 
                variants={containerVariants}
                className="list-disc pl-6 mb-4 text-gray-600 dark:text-gray-300"
              >
                <motion.li variants={itemVariants}>Email: <EMAIL></motion.li>
                <motion.li variants={itemVariants}>Online Form: Available on our website</motion.li>
                <motion.li variants={itemVariants}>Postal Mail: [Company Address]</motion.li>
              </motion.ul>
              
              <motion.h3 
                variants={itemVariants}
                className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mt-4 mb-2"
              >
                Our Response Process
              </motion.h3>
              <motion.p 
                variants={itemVariants}
                className="text-gray-600 dark:text-gray-300"
              >
                Upon receiving a valid DMCA takedown request, we will:
              </motion.p>
              <motion.ol 
                variants={containerVariants}
                className="list-decimal pl-6 mb-4 text-gray-600 dark:text-gray-300"
              >
                <motion.li variants={itemVariants}>Remove or disable access to the allegedly infringing material.</motion.li>
                <motion.li variants={itemVariants}>Make a reasonable effort to notify the affected user.</motion.li>
                <motion.li variants={itemVariants}>Provide the affected user with information about the takedown.</motion.li>
                <motion.li variants={itemVariants}>Forward a copy of the takedown notice to the affected user.</motion.li>
              </motion.ol>
              
              <motion.h3 
                variants={itemVariants}
                className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mt-4 mb-2"
              >
                Counter-Notification Procedure
              </motion.h3>
              <motion.p 
                variants={itemVariants}
                className="text-gray-600 dark:text-gray-300"
              >
                If you believe your content was wrongfully removed due to a DMCA takedown request, you may submit a counter-notification containing:
              </motion.p>
              <motion.ol 
                variants={containerVariants}
                className="list-decimal pl-6 mb-4 text-gray-600 dark:text-gray-300"
              >
                <motion.li variants={itemVariants}>Identification of the material that has been removed and where it appeared.</motion.li>
                <motion.li variants={itemVariants}>A statement under penalty of perjury that you have a good faith belief the material was removed as a result of mistake or misidentification.</motion.li>
                <motion.li variants={itemVariants}>Your name, address, phone number, and email address.</motion.li>
                <motion.li variants={itemVariants}>A statement that you consent to the jurisdiction of the federal district court for the judicial district in which your address is located.</motion.li>
                <motion.li variants={itemVariants}>A statement that you will accept service of process from the person who provided the DMCA notification.</motion.li>
                <motion.li variants={itemVariants}>Your physical or electronic signature.</motion.li>
              </motion.ol>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800 rounded-lg p-6 mt-8"
            >
              <h3 className="text-lg font-medium text-[#1d3b6c] dark:text-white mb-2">DMCA Takedown Request Form</h3>
              <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">Fill out this form to submit a DMCA takedown request.</p>
              
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Your Name</label>
                  <input type="text" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Your Email</label>
                  <input type="email" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Description of Copyrighted Work</label>
                  <textarea className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" rows={3}></textarea>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">URL or Description of Infringing Material</label>
                  <textarea className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" rows={3}></textarea>
                </div>
                
                <div>
                  <label className="flex items-center">
                    <input type="checkbox" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800 rounded" />
                    <span className="ml-2 text-sm text-blue-700 dark:text-blue-300">I have a good faith belief that the use of the material in the manner complained of is not authorized by the copyright owner, its agent, or the law.</span>
                  </label>
                </div>
                
                <div>
                  <label className="flex items-center">
                    <input type="checkbox" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800 rounded" />
                    <span className="ml-2 text-sm text-blue-700 dark:text-blue-300">Under penalty of perjury, I state that the information in this notification is accurate and that I am the copyright owner or authorized to act on behalf of the copyright owner.</span>
                  </label>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Electronic Signature (Type your full name)</label>
                  <input type="text" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                </div>
                
                <button 
                  type="submit" 
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  Submit Takedown Request
                </button>
              </form>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 