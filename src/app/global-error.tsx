"use client";

import * as Sentry from "@sentry/nextjs";
import { useEffect } from "react";

export default function GlobalError({ 
  error,
  reset,
}: { 
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to Sentry
    const eventId = Sentry.captureException(error);
    
    // Log to console in development
    console.error("Global error caught and reported to Sentry:", error);
    console.log("Sentry event ID:", eventId);
  }, [error]);

  return (
    <html lang="en">
      <body>
        <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 text-center">
            <div className="mb-6">
              <svg 
                className="w-16 h-16 text-red-500 mx-auto" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24" 
                xmlns="http://www.w3.org/2000/svg"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
                />
              </svg>
            </div>
            
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
              Oops, something went wrong
            </h1>
            
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              We've been automatically notified and are working to fix the issue.
            </p>
            
            {error.message && (
              <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded text-left">
                <p className="text-sm font-mono text-gray-800 dark:text-gray-200">
                  {error.message}
                </p>
                {error.digest && (
                  <p className="text-xs font-mono text-gray-500 dark:text-gray-400 mt-2">
                    ID: {error.digest}
                  </p>
                )}
              </div>
            )}
            
            <div className="flex justify-center">
              <button
                onClick={reset}
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg shadow-md transition duration-150 ease-in-out"
              >
                Try again
              </button>
            </div>
            
            <div className="mt-6">
              <a 
                href="/"
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                Return to home page
              </a>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
