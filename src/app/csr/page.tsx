'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { Shield<PERSON>heck, Users, Leaf, Globe, Heart, BookOpen } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function CSRPage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="prose prose-blue dark:prose-invert max-w-4xl mx-auto"
          >
            <motion.h1 
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-8"
            >
              Corporate Social Responsibility
            </motion.h1>
            
            <motion.div 
              variants={itemVariants}
              className="bg-blue-100/50 dark:bg-blue-900/30 p-6 rounded-lg border border-blue-200 dark:border-blue-800 mb-8"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mt-0 mb-4">Our Commitment</h2>
              <p className="text-gray-600 dark:text-gray-300">
                At FaceTrace, we believe that technology should be developed and deployed responsibly, with respect for individual 
                privacy, ethical considerations, and the broader societal impact. Our Corporate Social Responsibility (CSR) 
                policy outlines our commitment to operating as a socially conscious and ethically responsible organization.
              </p>
            </motion.div>
            
            <motion.div 
              variants={containerVariants}
              className="grid md:grid-cols-2 gap-8 mb-8"
            >
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-blue-100 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <ShieldCheck className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                  <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mt-0 mb-0">Ethics in Technology</h2>
                </div>
                <p className="mt-0 text-gray-600 dark:text-gray-300">
                  We recognize the powerful capabilities of facial recognition technology and are committed to developing and providing 
                  our services in a manner that:
                </p>
                <ul className="list-disc pl-6 mb-4 text-gray-600 dark:text-gray-300">
                  <li>Respects individual privacy rights</li>
                  <li>Prevents misuse for surveillance, stalking, or harassment</li>
                  <li>Empowers individuals to control their online presence</li>
                  <li>Reduces potential harms associated with technology</li>
                </ul>
              </motion.div>
              
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-blue-100 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <Users className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                  <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mt-0 mb-0">Privacy Advocacy</h2>
                </div>
                <p className="mt-0 text-gray-600 dark:text-gray-300">
                  We are advocates for privacy in the digital age:
                </p>
                <ul className="list-disc pl-6 mb-4 text-gray-600 dark:text-gray-300">
                  <li>We support balanced regulation of facial recognition technologies</li>
                  <li>We promote digital literacy and educate users about online privacy</li>
                  <li>We implement privacy-by-design principles in our development process</li>
                  <li>We believe individuals should have the right to find and manage their presence online</li>
                </ul>
              </motion.div>
            </motion.div>
            
            <motion.div variants={itemVariants}>
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mt-8 mb-4">Ethical Guidelines</h2>
              <p className="text-gray-600 dark:text-gray-300">Our operations are guided by the following ethical principles:</p>
              <ol className="list-decimal pl-6 mb-6 text-gray-600 dark:text-gray-300">
                <li><span className="font-medium">Transparency:</span> We provide clear information about how our technology works and how we use data.</li>
                <li><span className="font-medium">Consent:</span> We respect informed consent and individual autonomy over personal data.</li>
                <li><span className="font-medium">Harm Prevention:</span> We actively work to prevent potential misuse of our technology.</li>
                <li><span className="font-medium">Access Control:</span> We implement strict controls to ensure our technology is used for legitimate purposes.</li>
                <li><span className="font-medium">Accountability:</span> We take responsibility for the impact of our services.</li>
              </ol>
            </motion.div>
            
            <motion.div variants={itemVariants}>
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mt-8 mb-4">Combating Online Abuse</h2>
              <p className="text-gray-600 dark:text-gray-300">We are committed to helping combat various forms of online abuse:</p>
              <ul className="list-disc pl-6 mb-6 text-gray-600 dark:text-gray-300">
                <li>Identity theft protection</li>
                <li>Tools to identify unauthorized use of personal images</li>
                <li>Resources for victims of non-consensual intimate imagery</li>
                <li>Partnerships with organizations fighting online exploitation</li>
              </ul>
            </motion.div>
            
            <motion.div 
              variants={containerVariants}
              className="grid md:grid-cols-2 gap-8 mb-8"
            >
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-blue-100 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <Leaf className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                  <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mt-0 mb-0">Environmental Responsibility</h2>
                </div>
                <p className="mt-0 text-gray-600 dark:text-gray-300">
                  We strive to minimize our environmental impact through:
                </p>
                <ul className="list-disc pl-6 mb-4 text-gray-600 dark:text-gray-300">
                  <li>Energy-efficient data centers and server operations</li>
                  <li>Carbon offset programs for our digital footprint</li>
                  <li>Sustainable office practices and waste reduction</li>
                  <li>Remote work options to reduce commuting emissions</li>
                </ul>
              </motion.div>
              
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-blue-100 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <Globe className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                  <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mt-0 mb-0">Community Engagement</h2>
                </div>
                <p className="mt-0 text-gray-600 dark:text-gray-300">
                  We actively engage with and support:
                </p>
                <ul className="list-disc pl-6 mb-4 text-gray-600 dark:text-gray-300">
                  <li>Digital privacy advocacy groups</li>
                  <li>Organizations promoting responsible technology development</li>
                  <li>Educational initiatives on online safety and digital literacy</li>
                  <li>Research on the ethical implications of facial recognition technology</li>
                </ul>
              </motion.div>
            </motion.div>
            
            <motion.div variants={itemVariants}>
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mt-8 mb-4">Implementation and Governance</h2>
              <p className="text-gray-600 dark:text-gray-300">Our CSR initiatives are overseen by a dedicated committee that:</p>
              <ul className="list-disc pl-6 mb-6 text-gray-600 dark:text-gray-300">
                <li>Sets annual goals and objectives</li>
                <li>Monitors progress and impact</li>
                <li>Engages with stakeholders for feedback</li>
                <li>Reports regularly to company leadership</li>
              </ul>
            </motion.div>
            
            <motion.div 
              variants={containerVariants}
              className="grid md:grid-cols-2 gap-8 mb-8"
            >
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-blue-100 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <BookOpen className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                  <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mt-0 mb-0">Education and Awareness</h2>
                </div>
                <p className="mt-0 text-gray-600 dark:text-gray-300">
                  We are committed to promoting digital literacy through:
                </p>
                <ul className="list-disc pl-6 mb-4 text-gray-600 dark:text-gray-300">
                  <li>Educational resources on our website</li>
                  <li>Webinars and workshops on online privacy</li>
                  <li>Partnerships with educational institutions</li>
                  <li>Regular blog posts and guides on protecting digital identity</li>
                </ul>
              </motion.div>
              
              <motion.div variants={itemVariants} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-blue-100 dark:border-gray-700">
                <div className="flex items-center mb-4">
                  <Heart className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                  <h2 className="text-xl font-semibold text-[#1d3b6c] dark:text-white mt-0 mb-0">Ongoing Improvement</h2>
                </div>
                <p className="mt-0 text-gray-600 dark:text-gray-300">
                  We continuously evaluate and improve our CSR efforts by:
                </p>
                <ul className="list-disc pl-6 mb-4 text-gray-600 dark:text-gray-300">
                  <li>Soliciting feedback from users and stakeholders</li>
                  <li>Staying informed about emerging ethical considerations</li>
                  <li>Adapting our practices to address new challenges</li>
                  <li>Setting progressively more ambitious social responsibility goals</li>
                </ul>
              </motion.div>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800 rounded-lg p-6 mt-8"
            >
              <p className="italic text-blue-800 dark:text-blue-300 mb-0">
                We welcome feedback on our CSR initiatives and are committed to ongoing dialogue with our users, partners, and the broader 
                community about how we can best serve society while respecting individual rights and privacy.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 