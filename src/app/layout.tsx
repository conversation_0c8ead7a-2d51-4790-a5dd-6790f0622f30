import type { Metada<PERSON> } from "next";
import LayoutClient from './LayoutClient';

export const metadata: Metadata = {
  title: "FaceTrace.Pro - Find Where Faces Appear Across The Web",
  description: "Upload a photo and search across the web to see where this face appears. Identify scammers, locate missing persons, and more.",
  icons: {
    icon: [
      { url: '/favicon.png', type: 'image/png' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
    ],
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  openGraph: {
    title: 'FaceTrace.Pro - Find Where Faces Appear Across The Web',
    description: 'Upload a photo and search across the web to see where this face appears',
    url: 'https://facetrace.pro',
    siteName: 'FaceTrace.Pro',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'FaceTrace.Pro - Find Where Faces Appear Across The Web',
      }
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FaceTrace.Pro - Find Where Faces Appear Online',
    description: 'Upload a photo and search across the web to see where this face appears.',
    images: ['/og-image.png'],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <LayoutClient>{children}</LayoutClient>;
}
