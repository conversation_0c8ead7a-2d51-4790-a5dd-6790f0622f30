'use client';

import * as React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";
import { MessageSquare, X, Send, ChevronDown } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

// Support resources organized by categories with emojis
const supportResources = [
  {
    category: "Help & Information",
    color: "blue",
    items: [
      { emoji: "🔍", title: "How It Works", path: "/about" },
      { emoji: "💬", title: "Contact Us", path: "/contact" },
      { emoji: "🐞", title: "Report a Bug", path: "/report-bug" }
    ]
  },
  {
    category: "Account & Services",
    color: "emerald",
    items: [
      { emoji: "💰", title: "Refund Policy", path: "/refund-policy" },
      { emoji: "🔒", title: "Opt-Out from Search", path: "/opt-out" },
      { emoji: "📊", title: "Subject Access Request", path: "/sar-form" }
    ]
  },
  {
    category: "Legal & Privacy",
    color: "blue",
    items: [
      { emoji: "📜", title: "Terms of Use", path: "/terms-of-use" },
      { emoji: "🔏", title: "Privacy Policy", path: "/privacy-policy" },
      { emoji: "⚠️", title: "Legal Disclaimer", path: "/legal" },
      { emoji: "⚖️", title: "DMCA Takedown", path: "/dmca-takedown" },
      { emoji: "🌍", title: "Corporate Social Responsibility", path: "/csr" }
    ]
  }
];

// Chat support functionality
const ChatSupportBot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    { 
      sender: 'bot', 
      text: 'Hi there! I\'m the FaceTrace assistant. How can I help you today?',
      time: new Date() 
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Predefined responses for common questions
  const botResponses = {
    'search': 'To use our face search feature, upload a photo containing your face. Our AI will search for visually similar faces across public websites and provide you with the results. This feature is available in our basic and premium plans.',
    'account': 'You can manage your account settings by clicking on your profile icon in the top right corner and selecting "Account Settings". From there, you can update your profile information, change your password, or manage your subscription.',
    'subscription': 'We offer several subscription plans. The free tier allows basic searches with limited results. Our premium plans offer more comprehensive searches, alert features, and priority support. Visit our pricing page for more details.',
    'privacy': 'Privacy is our priority. We only search for images that are publicly available online. All uploaded images are processed securely and deleted within 48 hours. You can also opt-out at any time using our Opt-Out form.',
    'refund': 'We offer a 14-day money-back guarantee for all new subscriptions. If you\'re not satisfied, you can request a refund through our Refund Policy page or by contacting our support team.',
    'help': 'I can help with questions about our services, account management, privacy concerns, or technical issues. Please let me know what you need assistance with!',
    'default': 'Thank you for your message. If you need specific assistance, please email our support <NAME_EMAIL> or use our contact form for a more detailed response.',
  };

  // Handle submitting a message
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() === '') return;

    // Add user message
    const userMessage = {
      sender: 'user',
      text: inputValue.trim(),
      time: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate thinking time
    setTimeout(() => {
      const lowerCaseInput = inputValue.toLowerCase();
      let responseText = botResponses.default;

      // Check for keywords in the input
      if (lowerCaseInput.includes('search') || lowerCaseInput.includes('find') || lowerCaseInput.includes('look')) {
        responseText = botResponses.search;
      } else if (lowerCaseInput.includes('account') || lowerCaseInput.includes('profile') || lowerCaseInput.includes('settings')) {
        responseText = botResponses.account;
      } else if (lowerCaseInput.includes('subscription') || lowerCaseInput.includes('plan') || lowerCaseInput.includes('pricing') || lowerCaseInput.includes('cost')) {
        responseText = botResponses.subscription;
      } else if (lowerCaseInput.includes('privacy') || lowerCaseInput.includes('data') || lowerCaseInput.includes('security')) {
        responseText = botResponses.privacy;
      } else if (lowerCaseInput.includes('refund') || lowerCaseInput.includes('money back') || lowerCaseInput.includes('cancel')) {
        responseText = botResponses.refund;
      } else if (lowerCaseInput.includes('help') || lowerCaseInput.includes('support') || lowerCaseInput.includes('assist')) {
        responseText = botResponses.help;
      }

      const botMessage = {
        sender: 'bot',
        text: responseText,
        time: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000);
  };

  // Auto-scroll to the bottom when new messages arrive
  React.useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Format time
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Render
  return (
    <div className="fixed right-4 bottom-4 z-50">
      {/* Chat toggle button */}
      {!isOpen && (
        <motion.button
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 1 }}
          onClick={() => setIsOpen(true)}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-full shadow-lg"
        >
          <MessageSquare className="h-5 w-5" />
          <span>Chat Support</span>
        </motion.button>
      )}

      {/* Chat window */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden w-full sm:w-96 flex flex-col border border-blue-200 dark:border-blue-800"
        >
          {/* Chat header */}
          <div className="bg-blue-600 dark:bg-blue-700 text-white p-4 flex justify-between items-center">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              <h3 className="font-medium">FaceTrace Support</h3>
            </div>
            <button 
              onClick={() => setIsOpen(false)}
              className="text-white hover:bg-blue-700 dark:hover:bg-blue-800 rounded-full p-1"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Messages container */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 max-h-96">
            {messages.map((message, index) => (
              <div 
                key={index}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div 
                  className={`max-w-[80%] px-4 py-2 rounded-lg ${
                    message.sender === 'user' 
                      ? 'bg-blue-600 text-white rounded-br-none' 
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-bl-none'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  <p className={`text-xs mt-1 ${message.sender === 'user' ? 'text-blue-200' : 'text-gray-500 dark:text-gray-400'}`}>
                    {formatTime(message.time)}
                  </p>
                </div>
              </div>
            ))}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg rounded-bl-none">
                  <div className="flex space-x-1 items-center">
                    <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce delay-75"></div>
                    <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce delay-150"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Common questions */}
          <div className="bg-gray-50 dark:bg-gray-900 p-2 border-t border-gray-200 dark:border-gray-700">
            <details className="cursor-pointer">
              <summary className="flex items-center justify-between text-sm text-blue-600 dark:text-blue-400 font-medium p-1">
                <span>Common Questions</span>
                <ChevronDown className="h-4 w-4" />
              </summary>
              <div className="mt-2 space-y-1 px-1">
                {["How do I use face search?", "What subscription plans do you offer?", "How is my privacy protected?", "Can I get a refund?"].map((q, i) => (
                  <button
                    key={i}
                    onClick={() => {
                      setInputValue(q);
                    }}
                    className="w-full text-left text-xs p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded"
                  >
                    {q}
                  </button>
                ))}
              </div>
            </details>
          </div>

          {/* Input area */}
          <form className="p-3 border-t border-gray-200 dark:border-gray-700 flex gap-2" onSubmit={handleSubmit}>
            <input
              type="text"
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md text-sm"
              placeholder="Type your message..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-md"
              disabled={isTyping}
            >
              <Send className="h-5 w-5" />
            </button>
          </form>
        </motion.div>
      )}
    </div>
  );
};

export default function SupportPage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div
            className="text-center mb-12 md:mb-16"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            <motion.h1 
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-4"
              variants={itemVariants}
            >
              Support Center
            </motion.h1>
            <motion.p 
              className="text-gray-600 dark:text-gray-300 text-lg md:text-xl max-w-3xl mx-auto"
              variants={itemVariants}
            >
              Get help with FaceTrace services, accounts, and policies
            </motion.p>
          </motion.div>

          <motion.div 
            className="max-w-6xl mx-auto"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            <div className="grid gap-6 mb-10">
              {/* Support Resource Sections */}
              {supportResources.map((section, idx) => (
                <motion.div 
                  key={section.category}
                  variants={itemVariants}
                  className="p-6 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-gray-700 shadow-lg"
                >
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-6 pb-2 border-b border-blue-100 dark:border-gray-700">
                    {section.category}
                  </h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {section.items.map((item) => (
                      <Link
                        key={item.path}
                        href={item.path}
                        className="p-4 rounded-lg flex items-center gap-3 bg-blue-50/50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-200 border border-blue-100 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700 transition-all duration-200 shadow-sm hover:shadow"
                      >
                        <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                          section.color === "blue" 
                            ? "bg-blue-100 dark:bg-blue-900/40 text-blue-600 dark:text-blue-400" 
                            : section.color === "emerald" 
                              ? "bg-emerald-100 dark:bg-emerald-900/40 text-emerald-600 dark:text-emerald-400"
                              : "bg-indigo-100 dark:bg-indigo-900/40 text-indigo-600 dark:text-indigo-400"
                        }`}>
                          <span className="text-2xl">{item.emoji}</span>
                        </div>
                        <span className="font-medium">{item.title}</span>
                      </Link>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
            
            {/* Quick help section */}
            <motion.div
              variants={itemVariants}
              className="mt-10 p-6 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-gray-700 shadow-lg  mx-auto"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4 flex items-center">
                <span className="mr-3">⚡</span>
                Need Immediate Help?
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6 text-lg">
                24-Hour Support: 12 hours response time <br />
                <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a>
              </p>
              
              <div className="mt-6 grid sm:grid-cols-2 gap-4">
                <Link 
                  href="/contact"
                  className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                >
                  Contact Support
                </Link>
                <Link 
                  href="/faq"
                  className="inline-flex items-center justify-center px-6 py-3 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-blue-600 dark:text-blue-400 font-medium rounded-lg border border-blue-200 dark:border-gray-600 transition-colors"
                >
                  View FAQs
                </Link>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
      
      {/* Chat Support Bot */}
      <ChatSupportBot />
    </div>
  );
} 