// src/app/api/data/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { auth } from '@clerk/nextjs/server';


// Import database functions
import {
  createGuestTransaction, createSearchReport,
  updateSearchReport, getSearchReportById, getUploadsBySearchReportId,
  createUpload, getSearchReportByReportId, findUserByClerkId, 
  updateUser, addUserTokens, createUser
} from '@/lib/db';
import { db } from '@/lib/db';
import { sql, eq } from 'drizzle-orm';
import * as schema from '@/lib/db/schema';
// Removed unused sql import

/**
 * GET handler for data API
 */
export async function GET(request: NextRequest) {
  try {
    // Parse action from searchParams
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const id = searchParams.get('id');

    // Handle different GET actions
    switch (action) {
      case 'report':
        // Pass the request object to handlers
        return id ? handleReportById(id, request) : handleReportsList(request);
      case 'report-by-identifier':
        return handleReportByIdentifier(request);
      case 'user-data':
        return handleUserData(request);
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in GET handler:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}

/**
 * POST handler for data API
 */
export async function POST(request: NextRequest) {
  try {
    // Parse action from searchParams
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const id = searchParams.get('id');

  // Handle different POST actions - ordering matters for dependency flows
  switch (action) {
    // Create/modify report actions
    case 'create-report':
      return handleCreateReport(request); // First create the report
    case 'update-report':
      return id ? handleUpdateReport(request, id) : NextResponse.json({ error: 'Missing report ID' }, { status: 400 });
    case 'save-results':
      return id ? handleSaveResults(request, id) : NextResponse.json({ error: 'Missing report ID' }, { status: 400 });
      
    // Transaction and search record actions  
    case 'create-transaction':
      return handleCreateTransaction(request);
    case 'search-records':
      return handleSearchRecords(request);
    
    // User data actions
    case 'award-tokens':
      return handleAwardTokens(request);
    case 'create-user':
      return handleCreateUser(request);
      
    // Query actions  
    case 'reports-by-email':
      return handleReportsByEmail(request);
      
    // Special handlers  
    case 'opt-out':
      // Redirect to our dedicated opt-out handler
      return handleOptOut(request);
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in POST handler:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}

// Validate Stripe Payment Intent ID format
function validatePaymentIntentId(id: string): boolean {
  // Payment Intent IDs follow the format: pi_XXXXXXXXXXXXXXXXXX
  return /^pi_[a-zA-Z0-9]{24,}$/.test(id);
}

// Handler functions for GET actions

/**
 * Get a specific report by ID
 */
async function handleReportById(reportId: string, request: NextRequest) {
  try {
    console.log(`Fetching report details for ID: ${reportId}`);

    // First check if we have a alphanumeric report ID (likely a report_id rather than a DB id)
    if (reportId && isNaN(Number(reportId)) && reportId.length > 0) {
      console.log(`Looking up report by alphanumeric identifier: ${reportId}`);
      const reportByIdentifier = await getSearchReportByReportId(reportId);
      
      if (reportByIdentifier) {
        // Use the numeric database ID instead
        reportId = reportByIdentifier.id.toString();
        console.log(`Found report with DB id: ${reportId} for identifier: ${reportId}`);
      } else {
        console.warn(`Report not found for identifier: ${reportId}`);
        return NextResponse.json({ error: 'Report not found' }, { status: 404 });
      }
    }

    // Now get the report using the numeric ID
    const report = await getSearchReportById(reportId);

    if (!report) {
      console.warn(`Report not found for ID: ${reportId}`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // No need for authentication checks since all users are guests

    // Get uploads for the report (only if status indicates completion)
    let uploads: any[] = [];
    if (report.status === 'completed') {
      console.log(`Report ${reportId} is complete. Fetching uploads.`);
      uploads = await getUploadsBySearchReportId(report.id);
      console.log(`Found ${uploads.length} uploads for report ${reportId}.`);
    } else {
      console.log(`Report ${reportId} status is ${report.status}. Not fetching uploads yet.`);
    }

    // Return the report data including status, progress, and uploads if ready
    return NextResponse.json({
      id: report.id,
      status: report.status,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
      resultCount: report.resultCount,
      isPrivate: report.isPrivate,
      progress: report.progress || 0,
      uploads: uploads, // Include uploads array (empty if not completed)
      facecheckIdSearch: report.facecheckIdSearch // Include facecheck_id_search data
    });

  } catch (error) {
    console.error(`Error fetching report ${reportId}:`, error);
    return NextResponse.json({ error: 'Error fetching report' }, { status: 500 });
  }
}

/**
 * Get list of reports - simplified for guest only flow
 */
async function handleReportsList(request: NextRequest) {
  try {
    return NextResponse.json({
      reports: [],
      count: 0,
      message: 'Please provide an email to see your reports'
    });
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json({ error: 'Error fetching reports' }, { status: 500 });
  }
}

/**
 * Get a report by its identifier
 */
async function handleReportByIdentifier(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const identifier = searchParams.get('identifier');

    if (!identifier) {
      return NextResponse.json({ error: 'Report identifier is required' }, { status: 400 });
    }

    console.log(`Fetching report details for identifier: ${identifier}`);

    // Use the new helper function to get the report by report_id
    const report = await getSearchReportByReportId(identifier);

    if (!report) {
      console.warn(`Report not found for identifier: ${identifier}`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Get uploads for the report (only if status indicates completion)
    let uploads: any[] = [];
    if (report.status === 'completed') {
      console.log(`Report with identifier ${identifier} is complete. Fetching uploads.`);
      uploads = await getUploadsBySearchReportId(report.id);
      console.log(`Found ${uploads.length} uploads for report with identifier ${identifier}.`);
    } else {
      console.log(`Report with identifier ${identifier} status is ${report.status}. Not fetching uploads yet.`);
    }

    // Return the report data including status, progress, and uploads if ready
    return NextResponse.json({
      id: report.id,
      status: report.status,
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
      resultCount: report.resultCount,
      isPrivate: report.isPrivate,
      progress: report.progress || 0,
      expiresAt: report.expiresAt, // Include expiration date
      uploads: uploads, // Include uploads array (empty if not completed)
      facecheckIdSearch: report.facecheckIdSearch // Include facecheck_id_search data
    });

  } catch (error) {
    console.error(`Error fetching report by identifier:`, error);
    return NextResponse.json({ error: 'Error fetching report' }, { status: 500 });
  }
}

/**
 * Get user data - checks auth and returns user tokens
 */
async function handleUserData(request: NextRequest) {
  try {
    // Check if user is authenticated
    const authResult = await import('@clerk/nextjs/server').then(clerk => clerk.auth());
    const { userId } = authResult;

    // If not authenticated, return empty data with loggedIn: false
    if (!userId) {
      return NextResponse.json({
        tokens: 0,
        loggedIn: false
      });
    }
    
    // Import DB functions
    const { findUserByClerkId } = await import('@/lib/db');
    
    // Find user in database
    const user = await findUserByClerkId(userId);
    
    // If user exists in database, return their token count
    if (user) {
      return NextResponse.json({
        tokens: user.tokens,
        loggedIn: true,
        userId: userId
      });
    }
    
    // User is authenticated with Clerk but not found in our database
    console.warn(`[user-data] User ${userId} authenticated but not in database`);
    return NextResponse.json({
      tokens: 0,
      loggedIn: true, 
      userId: userId,
      warning: "User record not found in database"
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    return NextResponse.json({ error: 'Error fetching user data' }, { status: 500 });
  }
}

// Handler functions for POST actions

/**
 * Create a new report
 */
async function handleCreateReport(request: NextRequest) {
  try {
    // Parse the request body
    const data = await request.json();
    const { userType, price, searchImageUrls, status, email } = data;

    // Create the search report
    const report = await createSearchReport({
      userId: null, // Always null for guest-only flow
      userType: 'guest',
      email: email, // Store guest email
      facecheckIdSearch: searchImageUrls[0], // Use the first searchImageUrl as facecheckIdSearch
      searchImageUrls: searchImageUrls,
      status: status,
      isPrivate: false, // Public reports for guests
      resultCount: 0, // Initially no results
    });

    return NextResponse.json(report);
  } catch (error) {
    console.error('Error creating search report:', error);
    return NextResponse.json({ error: 'Failed to create search report' }, { status: 500 });
  }
}

/**
 * Update a report
 */
async function handleUpdateReport(request: NextRequest, reportId: string) {
  try {
    // First check if we have an alphanumeric report ID (likely a report_id rather than a DB id)
    if (reportId && isNaN(Number(reportId)) && reportId.length > 0) {
      console.log(`Looking up report by alphanumeric identifier: ${reportId}`);
      const reportByIdentifier = await getSearchReportByReportId(reportId);
      
      if (reportByIdentifier) {
        // Use the numeric database ID instead
        reportId = reportByIdentifier.id.toString();
        console.log(`Found report with DB id: ${reportId} for identifier: ${reportId}`);
      } else {
        console.warn(`Report not found for identifier: ${reportId}`);
        return NextResponse.json({ error: 'Report not found' }, { status: 404 });
      }
    }

    // Get the report
    const report = await getSearchReportById(reportId);

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // No need for authentication check - all users are guests

    // Parse the request body
    const data = await request.json();

    // We need a valid numeric ID for the database update
    let numericReportId: number;
    try {
      numericReportId = parseInt(reportId, 10);
      if (isNaN(numericReportId)) {
        throw new Error(`Invalid numeric report ID: ${reportId}`);
      }
      console.log(`Using validated numeric report ID: ${numericReportId} for database operation`);
    } catch (parseError) {
      console.error(`Failed to parse report ID to integer: ${reportId}`, parseError);
      return NextResponse.json({ 
        error: 'Invalid report ID format for database operation',
        details: 'Report ID must be a valid integer'
      }, { status: 400 });
    }

    // Update the report using the numeric ID
    const updatedReport = await updateSearchReport(numericReportId, data);

    if (!updatedReport) {
      return NextResponse.json({ error: 'Failed to update report' }, { status: 500 });
    }

    return NextResponse.json(updatedReport);
  } catch (error) {
    console.error('Error updating report:', error);
    return NextResponse.json({ error: 'Error updating report' }, { status: 500 });
  }
}

/**
 * Save search results
 */
async function handleSaveResults(request: NextRequest, reportId: string) {
  try {
    // First check if we have a alphanumeric report ID (likely a report_id rather than a DB id)
    if (reportId && isNaN(Number(reportId)) && reportId.length > 0) {
      console.log(`Looking up report by alphanumeric identifier: ${reportId}`);
      const reportByIdentifier = await getSearchReportByReportId(reportId);
      
      if (reportByIdentifier) {
        // Use the numeric database ID instead
        reportId = reportByIdentifier.id.toString();
        console.log(`Found report with DB id: ${reportId} for identifier: ${reportId}`);
      } else {
        console.error(`No report found for identifier: ${reportId}`);
        return NextResponse.json({ error: 'Report not found' }, { status: 404 });
      }
    }

    // Now get the report using the numeric ID
    const report = await getSearchReportById(reportId);

    if (!report) {
      console.error(`Report not found for ID: ${reportId}`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Parse the request body
    const { results } = await request.json();

    if (!Array.isArray(results) || results.length === 0) {
      console.error(`Invalid results array for report ${reportId}`, results);
      return NextResponse.json({ error: 'Results array is required' }, { status: 400 });
    }

    console.log(`Saving ${results.length} results for report ${reportId}`);

    // Calculate expiration time (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);
    console.log(`Setting expiration time to ${expiresAt.toISOString()}`);

    // Ensure the results array is properly stringified - use a safe stringification
    let resultsJson;
    try {
      // Ensure we're not storing excessive data that might cause issues
      const processedResults = results.map(result => ({
        id: result.id,
        confidence: result.confidence,
        sourceUrl: result.sourceUrl,
        sourceType: result.sourceType,
        thumbnail: result.thumbnail,
        domain: result.domain,
        // Trim any other large fields as needed
      }));
      resultsJson = JSON.stringify(processedResults);
      console.log(`Results successfully stringified, length: ${resultsJson.length} characters`);
    } catch (stringifyError) {
      console.error(`Error stringifying results for report ${reportId}:`, stringifyError);
      return NextResponse.json({ error: 'Failed to process results data' }, { status: 500 });
    }

    // We need a valid numeric ID for the database update
    let numericReportId: number;
    try {
      numericReportId = parseInt(reportId, 10);
      if (isNaN(numericReportId)) {
        throw new Error(`Invalid numeric report ID: ${reportId}`);
      }
      console.log(`Using validated numeric report ID: ${numericReportId} for database operation`);
    } catch (parseError) {
      console.error(`Failed to parse report ID to integer: ${reportId}`, parseError);
      return NextResponse.json({ 
        error: 'Invalid report ID format for database operation',
        details: 'Report ID must be a valid integer'
      }, { status: 400 });
    }

    // Save the entire results array directly in the facecheck_id_search column
    // and set the expiration time
    try {
      // Use db.update with Drizzle SQL for better type safety
      const updatedReport = await db.update(schema.searchReports)
        .set({
          facecheckIdSearch: resultsJson,
          status: 'completed', // Make sure status is set to completed
          progress: 100, // Set to 100% when completed
          resultCount: results.length, // Update result count
          expiresAt: expiresAt,
          updatedAt: new Date() // Update the timestamp
        })
        .where(eq(schema.searchReports.id, numericReportId))
        .returning();

      if (!updatedReport || updatedReport.length === 0) {
        console.error(`Failed to update report ${reportId} with results`);
        return NextResponse.json({ error: 'Failed to update report with results' }, { status: 500 });
      }

      console.log(`Report ${reportId} updated with results and expiration time set to ${expiresAt.toISOString()}`);
      console.log(`Report status set to: ${updatedReport[0].status}, progress: ${updatedReport[0].progress}%`);
    } catch (updateError) {
      console.error(`Error updating report ${reportId} with results:`, updateError);
      return NextResponse.json({ error: 'Failed to update report with results' }, { status: 500 });
    }

    // Create an upload record for the results
    try {
      // Create a single upload record with the first result as input_0
      const upload = await createUpload({
        id_search: reportId,
        search_report_id: numericReportId,
        input_0: results.length > 0 ? results[0] : null,
        input_0_base64: results.length > 0 && results[0].thumbnail ? results[0].thumbnail : null,
        input_0_id_pic: results.length > 0 && results[0].sourceUrl ? results[0].sourceUrl : null,
        input_1: results.length > 1 ? results[1] : null,
        input_1_base64: results.length > 1 && results[1].thumbnail ? results[1].thumbnail : null,
        input_1_id_pic: results.length > 1 && results[1].sourceUrl ? results[1].sourceUrl : null,
        input_2: results.length > 2 ? results[2] : null,
        input_2_base64: results.length > 2 && results[2].thumbnail ? results[2].thumbnail : null,
        input_2_id_pic: results.length > 2 && results[2].sourceUrl ? results[2].sourceUrl : null,
        new_seen_count: 0,
        duplicates: 0
      });

      console.log(`Created upload record for report ${reportId}`);

      return NextResponse.json({
        success: true,
        uploadId: upload.id,
        expiresAt: expiresAt
      });
    } catch (createError) {
      console.error(`Error creating upload record for report ${reportId}:`, createError);
      // We won't fail the request here since the main results data is already saved
      return NextResponse.json({
        success: true,
        uploadId: null,
        warning: "Results saved but upload record could not be created",
        expiresAt: expiresAt
      });
    }
  } catch (error) {
    console.error(`Error saving search results for report ${reportId}:`, error);
    return NextResponse.json({ error: 'Error saving search results' }, { status: 500 });
  }
}

/**
 * Get reports by email
 */
async function handleReportsByEmail(request: NextRequest) {
  try {
    // Extract email from request body
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Use database query with proper SQL and parameters
    const result = await db.execute(
      sql`SELECT 
          id, 
          report_id, 
          status, 
          result_count, 
          created_at, 
          updated_at 
        FROM search_reports 
        WHERE email = ${email} 
        OR id IN (SELECT search_report_id FROM guest_transactions WHERE email = ${email})
        ORDER BY created_at DESC`
    );

    // Extract rows from the result
    const rows = result.rows || [];

    return NextResponse.json({
      reports: rows,
      count: rows.length
    });
  } catch (error) {
    console.error('Error fetching reports by email:', error);
    return NextResponse.json({ error: 'Failed to fetch reports' }, { status: 500 });
  }
}

/**
 * Create a transaction - simplified for guest only flow
 */
async function handleCreateTransaction(request: NextRequest) {
  try {
    // Parse the request body
    const data = await request.json();
    const { reportId, amount, status, email, paymentIntentId } = data;

    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    if (!email) {
      return NextResponse.json({ error: 'Email is required for guest transactions' }, { status: 400 });
    }
    
    // Handle alphanumeric report IDs
    let numericReportId = reportId;
    if (reportId && isNaN(Number(reportId)) && reportId.length > 0) {
      console.log(`[data-api] Looking up report by alphanumeric identifier: ${reportId}`);
      const reportByIdentifier = await getSearchReportByReportId(reportId);
      
      if (reportByIdentifier) {
        // Use the numeric database ID instead
        numericReportId = reportByIdentifier.id;
        console.log(`[data-api] Found report with DB id: ${numericReportId} for identifier: ${reportId}`);
      } else {
        console.warn(`[data-api] Report not found for identifier: ${reportId}`);
        return NextResponse.json({ error: 'Report not found' }, { status: 404 });
      }
    }

    // Validate payment intent ID if provided
    if (paymentIntentId) {
      if (!validatePaymentIntentId(paymentIntentId)) {
        console.error(`[data-api] Invalid Stripe payment ID format: ${paymentIntentId}`);
        return NextResponse.json({
          error: 'Invalid payment ID format. Expected format: pi_XXXXXXXXXXXX'
        }, { status: 400 });
      }
      console.log(`[data-api] Valid payment ID format: ${paymentIntentId}`);
    }

    // For guest transactions
    const transaction = await createGuestTransaction({
      reportId: numericReportId,
      amount,
      status,
      email: email,
      currency: 'usd',
      stripePaymentId: paymentIntentId,
      metadata: {
        source: 'direct_payment',
        service: 'face_search',
        paymentIntentFull: paymentIntentId, // Store full ID in metadata for redundancy
        originalReportId: reportId // Store the original ID that was passed in
      }
    });

    console.log(`[data-api] Created transaction for report ${reportId} with payment ID: ${paymentIntentId || 'none'}`);

    return NextResponse.json(transaction);
  } catch (error) {
    console.error('Error creating transaction:', error);
    return NextResponse.json({ error: 'Failed to create transaction' }, { status: 500 });
  }
}

/**
 * Create search records - simplified for guest only flow
 */
async function handleSearchRecords(request: NextRequest) {
  try {
    const { searchIds, clientSecret, email } = await request.json();

    if (!searchIds || !clientSecret) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    if (!email) {
      return NextResponse.json({ error: 'Email is required for guest searches' }, { status: 400 });
    }

    // Extract payment intent ID from client secret - improved to handle full Stripe format
    let paymentIntentId = clientSecret.split('_secret_')[0];

    // Validate payment intent ID format
    if (!validatePaymentIntentId(paymentIntentId)) {
      console.error(`[data-api] Invalid payment intent ID extracted from client secret: ${paymentIntentId}`);
      return NextResponse.json({ error: 'Invalid payment intent ID format' }, { status: 400 });
    }

    console.log(`[data-api] Using validated payment intent ID: ${paymentIntentId}`);

    // Helper function to get expiry date (30 days for guests)
    function getExpiryDate(): Date {
      const now = new Date();
      now.setDate(now.getDate() + 30); // 30 days for guests
      return now;
    }

    // Create search report with payment
    const searchReportData = {
      userId: null, // Always null for guest flow
      userType: 'guest',
      email: email,
      facecheckIdSearch: Array.isArray(searchIds) ? searchIds[0] : searchIds, // Ensure we have a string
      searchImageUrls: searchIds,
      status: 'processing',
      isPrivate: false, // Public reports for guests
      price: '5.00', // $5.00
      expiresAt: getExpiryDate()
    };

    const searchReport = await createSearchReport(searchReportData);

    // Create guest transaction with validated payment ID and report_id for reference
    await createGuestTransaction({
      email: email,
      amount: '5.00',
      status: 'completed',
      currency: 'usd',
      stripePaymentId: paymentIntentId,
      reportId: searchReport.id,
      metadata: {
        source: 'direct_payment',
        service: 'face_search',
        paymentIntentFull: paymentIntentId, // Store full ID in metadata for redundancy
        report_id: searchReport.report_id || searchReport.id.toString() // Store the report_id for future reference
      }
    });

    console.log(`[data-api] Created search record and transaction for payment ID: ${paymentIntentId}`);

    return NextResponse.json({
      success: true,
      reportId: searchReport.id
    });
  } catch (error: unknown) {
    console.error('Error creating search record:', error);
    return NextResponse.json({
      error: 'Failed to create search record'
    }, { status: 500 });
  }
}

/**
 * Handle opt-out requests by redirecting to dedicated handler
 */
async function handleOptOut(request: NextRequest) {
  console.log('[data-api] Received opt-out request, forwarding to dedicated handler');
  
  // Forward the request to our dedicated opt-out handler
  // This is more maintainable than duplicating logic here
  const optOutResponse = await fetch(new URL('/api/data/opt-out', request.url), {
    method: 'POST',
    headers: request.headers,
    body: request.body
  });
  
  // Return the response from the opt-out handler
  return NextResponse.json(await optOutResponse.json(), {
    status: optOutResponse.status
  });
}

/**
 * Update user after phone verification
 */
async function handleUserAfterVerification(request: NextRequest) {
  try {
    // Check if user is authenticated using Clerk
    const authResult = await import('@clerk/nextjs/server').then(clerk => clerk.auth());
    const { userId } = authResult;
    
    if (!userId) {
      console.warn("[update-user-after-verification] Unauthorized attempt to update user");
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Parse request body
    const { phoneVerified = true } = await request.json();
    
    console.log(`[update-user-after-verification] Updating user ${userId} after phone verification`);
    
    // Import db functions
    const { findUserByClerkId, updateUser, addUserTokens } = await import('@/lib/db');
    
    // Find the user in our database by Clerk ID
    const user = await findUserByClerkId(userId);
    
    // If user doesn't exist in our database yet, return an error
    if (!user) {
      console.error(`[update-user-after-verification] User with Clerk ID ${userId} not found in database.`);
      return NextResponse.json({ 
        error: 'User record not found. Account setup might still be in progress.',
      }, { status: 404 }); // Not Found status
    }
    
    // Update the user - set phone as verified and add 5 tokens
    const updatedUser = await updateUser(user.id, {
      verified: phoneVerified,
      tokens: 5 // Set tokens to 5 for new users
    });
    
    if (!updatedUser) {
      console.error(`[update-user-after-verification] Failed to update user ${userId}`);
      return NextResponse.json({ error: 'Failed to update user after verification' }, { status: 500 });
    }
    
    console.log(`[update-user-after-verification] Successfully updated user ${userId} after verification. Tokens: ${updatedUser.tokens}`);
    
    // Return success with updated user info
    return NextResponse.json({
      success: true,
      tokens: updatedUser.tokens,
      phoneVerified: updatedUser.verified
    });
  } catch (error) {
    console.error('[update-user-after-verification] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to update user after verification',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * Mark the user's free unlock as used
 */
async function handleUseFreeUnlock(request: NextRequest) {
  try {
    // Check if user is authenticated using Clerk
    const authResult = await import('@clerk/nextjs/server').then(clerk => clerk.auth());
    const { userId } = authResult;
    
    if (!userId) {
      console.warn("[use-free-unlock] Unauthorized attempt to use free unlock");
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    console.log(`[use-free-unlock] Marking free unlock as used for user ${userId}`);
    
    // Import db functions
    const { findUserByClerkId, updateUser } = await import('@/lib/db');
    
    // Find the user in our database by Clerk ID
    const user = await findUserByClerkId(userId);
    
    // If user doesn't exist in our database yet, return an error
    if (!user) {
      console.error(`[use-free-unlock] User with Clerk ID ${userId} not found in database.`);
      return NextResponse.json({ 
        error: 'User record not found. Account setup might still be in progress.',
      }, { status: 404 }); // Not Found status
    }
    
    // Check if the user has already used their free unlock
    if (user.initialFreeUnlockUsed) {
      console.warn(`[use-free-unlock] User ${userId} has already used their free unlock.`);
      return NextResponse.json({ 
        success: false,
        message: 'Free unlock already used',
        initialFreeUnlockUsed: true
      });
    }
    
    // Update the user to mark initial free unlock as used
    const updatedUser = await updateUser(user.id, {
      initialFreeUnlockUsed: true
    });
    
    if (!updatedUser) {
      console.error(`[use-free-unlock] Failed to update user ${userId}`);
      return NextResponse.json({ error: 'Failed to mark free unlock as used' }, { status: 500 });
    }
    
    console.log(`[use-free-unlock] Successfully marked free unlock as used for user ${userId}`);
    
    // Return success
    return NextResponse.json({
      success: true,
      initialFreeUnlockUsed: true
    });
  } catch (error) {
    console.error('[use-free-unlock] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to mark free unlock as used',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * Award tokens to authenticated user
 */
async function handleAwardTokens(request: NextRequest) {
  try {
    // Check if user is authenticated using Clerk
    const authResult = await import('@clerk/nextjs/server').then(clerk => clerk.auth());
    const { userId } = authResult;
    
    if (!userId) {
      console.warn("[award-tokens] Unauthorized attempt to award tokens");
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Parse request body
    const { tokens = 5, reason = 'initial_signup' } = await request.json();
    
    console.log(`[award-tokens] Attempting to award ${tokens} tokens to user ${userId} (reason: ${reason})`);
    
    // 1. Import db functions
    const { findUserByClerkId, addUserTokens } = await import('@/lib/db');
    
    // 2. Find the user in our database by Clerk ID
    const user = await findUserByClerkId(userId);
    
    // If user doesn't exist in our database yet, return an error.
    // User creation should be handled by the user.created webhook.
    if (!user) {
      console.error(`[award-tokens] User with Clerk ID ${userId} not found in database. Cannot award tokens.`);
      return NextResponse.json({ 
        error: 'User record not found. Account setup might still be in progress.',
      }, { status: 404 }); // Not Found status
    }
    
    // If user exists, add tokens to their balance
    const updatedUser = await addUserTokens(user.id, tokens);
    
    if (!updatedUser) {
      console.error(`[award-tokens] Failed to add tokens to user ${userId}`);
      return NextResponse.json({ error: 'Failed to add tokens' }, { status: 500 });
    }
    
    console.log(`[award-tokens] Successfully awarded ${tokens} tokens to user ${userId}. New balance: ${updatedUser.tokens}`);
    
    // Return success with updated token count
    return NextResponse.json({
      success: true,
      previousTokens: updatedUser.tokens - tokens,
      awardedTokens: tokens,
      newTokenBalance: updatedUser.tokens,
      reason: reason
    });
  } catch (error) {
    console.error('[award-tokens] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to award tokens',
      details: errorMessage
    }, { status: 500 });
  }
}

/**
 * Handle creating a new user record
 */
async function handleCreateUser(request: NextRequest) {
  try {
    // Get the current authenticated Clerk user ID
    const { userId: clerkId } = await auth();
    
    if (!clerkId) {
      return NextResponse.json({ error: 'User must be authenticated to create a record' }, { status: 401 });
    }
    
    // Parse request body
    const { email, name, phone } = await request.json();
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }
    
    // Check if user already exists with this Clerk ID
    const existingUser = await findUserByClerkId(clerkId);
    
    if (existingUser) {
      console.log(`[create-user] User with Clerk ID ${clerkId} already exists`);
      // Return error indicating user already exists instead of updating
      return NextResponse.json({
        success: false,
        error: 'User already exists',
        message: 'A user with this account already exists in the system.',
        user: {
          id: existingUser.id,
          email: existingUser.email,
          name: existingUser.name,
          tokens: existingUser.tokens,
        },
      }, { status: 409 }); // 409 Conflict status code
    }
    
    // Create a new user
    console.log(`[create-user] Creating new user with Clerk ID ${clerkId}`);
    const newUser = await createUser({
      clerkId,
      email,
      name,
      tokens: 0, // Start with 0 tokens - will be set to 5 after phone verification
      initialFreeUnlockUsed: false, // New users haven't used their free unlock yet
    });
    
    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        tokens: newUser.tokens,
      },
    });
  } catch (error) {
    console.error('[create-user] Error creating user:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: 'Failed to create user', details: errorMessage }, { status: 500 });
  }
}
