import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { optOutRequests } from '@/lib/db/schema';
import { Resend } from 'resend';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';

// Initialize Resend for email
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

// Secure storage path for opt-out files
const STORAGE_PATH = process.env.OPT_OUT_STORAGE_PATH || './opt-out-storage';

/**
 * Handle opt-out form submissions
 * This endpoint processes the face photo and ID photo for opt-out requests
 */
export async function POST(req: NextRequest) {
  console.log('[opt-out] Processing opt-out request');
  
  try {
    // Create storage directory if it doesn't exist
    await fs.mkdir(STORAGE_PATH, { recursive: true });
    
    // Generate a unique ID for this request
    const requestId = uuidv4();
    const faceDir = path.join(STORAGE_PATH, requestId);
    await fs.mkdir(faceDir, { recursive: true });
    
    // Get the form data
    const formData = await req.formData();
    const fullName = formData.get('fullName') as string;
    const email = formData.get('email') as string;
    const facePhotoFile = formData.get('facePhotoFile') as File;
    const idPhotoFile = formData.get('idPhotoFile') as File;
    
    // Validate the form data
    if (!fullName || !email || !facePhotoFile || !idPhotoFile) {
      return NextResponse.json({ 
        error: 'Missing required fields' 
      }, { status: 400 });
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ 
        error: 'Invalid email format' 
      }, { status: 400 });
    }
    
    // Store the face photo for processing
    const facePhotoBuffer = Buffer.from(await facePhotoFile.arrayBuffer());
    const facePath = path.join(faceDir, 'face.' + getExtension(facePhotoFile.name));
    await fs.writeFile(facePath, facePhotoBuffer);
    
    // Store the ID photo temporarily for verification
    const idPhotoBuffer = Buffer.from(await idPhotoFile.arrayBuffer());
    const idPath = path.join(faceDir, 'id.' + getExtension(idPhotoFile.name));
    await fs.writeFile(idPath, idPhotoBuffer);
    
    console.log(`[opt-out] Stored opt-out request files in ${faceDir}`);
    
    // Store request metadata in database
    await db.insert(optOutRequests).values({
      id: requestId,
      fullName,
      email,
      status: 'pending',
      facePhotoPath: facePath,
      idPhotoPath: idPath,
      requestDate: new Date(),
      // We leave processedDate as null since it hasn't been processed yet
    });
    
    console.log(`[opt-out] Created database record for opt-out request ${requestId}`);
    
    // Send confirmation email if Resend is configured
    if (resend) {
      try {
        await resend.emails.send({
          from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
          to: email,
          subject: 'FaceTrace Opt-Out Request Received',
          html: `
            <h1>Opt-Out Request Received</h1>
            <p>Dear ${fullName},</p>
            <p>We have received your request to opt out of FaceTrace's facial recognition search results.</p>
            <p>Your request ID is: <strong>${requestId}</strong></p>
            <p>We will process your request within 7 business days. You will receive another email when processing is complete.</p>
            <p>Thank you for your patience.</p>
            <p>Sincerely,<br>The FaceTrace Team</p>
          `
        });
        console.log(`[opt-out] Sent confirmation email to ${email}`);
      } catch (emailError) {
        console.error('[opt-out] Failed to send confirmation email:', emailError);
        // Continue processing even if email fails
      }
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Opt-out request submitted successfully',
      requestId
    });
    
  } catch (error) {
    console.error('[opt-out] Error processing opt-out request:', error);
    return NextResponse.json({ 
      error: 'Failed to process opt-out request'
    }, { status: 500 });
  }
}

/**
 * Get file extension from filename
 */
function getExtension(filename: string): string {
  const ext = path.extname(filename).toLowerCase();
  return ext.startsWith('.') ? ext.slice(1) : ext;
}
