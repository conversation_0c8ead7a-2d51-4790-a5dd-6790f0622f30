import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

// Initialize Resend for email
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

/**
 * Handle contact form submissions
 */
export async function POST(req: NextRequest) {
  console.log('[contact] Processing contact form submission');
  
  try {
    const { name, email, subject, message } = await req.json();
    
    // Validate the form data
    if (!name || !email || !message) {
      return NextResponse.json({ 
        error: 'Missing required fields' 
      }, { status: 400 });
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ 
        error: 'Invalid email format' 
      }, { status: 400 });
    }
    
    // Send email notification if Resen<PERSON> is configured
    if (resend) {
      try {
        // Send notification to support team
        await resend.emails.send({
          from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
          to: '<EMAIL>',
          subject: `Contact Form: ${subject || 'New Message'}`,
          html: `
            <h1>New Contact Form Submission</h1>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Subject:</strong> ${subject || 'No subject provided'}</p>
            <h3>Message:</h3>
            <p>${message.replace(/\n/g, '<br>')}</p>
            <hr>
            <p><small>Submitted via FaceTrace contact form</small></p>
          `
        });
        
        // Send confirmation email to user
        await resend.emails.send({
          from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
          to: email,
          subject: 'Thank you for contacting FaceTrace',
          html: `
            <h1>Thank you for your message</h1>
            <p>Dear ${name},</p>
            <p>We have received your message and will get back to you within 24 business hours.</p>
            <h3>Your message:</h3>
            <p><strong>Subject:</strong> ${subject || 'No subject provided'}</p>
            <p>${message.replace(/\n/g, '<br>')}</p>
            <p>Best regards,<br>The FaceTrace Team</p>
            <hr>
            <p><small>This is an automated confirmation email.</small></p>
          `
        });
        
        console.log(`[contact] Contact form emails sent successfully for ${email}`);
      } catch (emailError) {
        console.error('[contact] Failed to send contact form emails:', emailError);
        // Continue processing even if email fails
        return NextResponse.json({ 
          error: 'Message received but confirmation email could not be sent. We will still respond to your inquiry.' 
        }, { status: 500 });
      }
    } else {
      console.warn('[contact] Resend not configured, emails will not be sent');
      return NextResponse.json({ 
        error: 'Email service not configured. Please email us <NAME_EMAIL>' 
      }, { status: 500 });
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Message sent successfully'
    });
    
  } catch (error) {
    console.error('[contact] Error processing contact form:', error);
    return NextResponse.json({ 
      error: 'Failed to process contact form submission'
    }, { status: 500 });
  }
}
