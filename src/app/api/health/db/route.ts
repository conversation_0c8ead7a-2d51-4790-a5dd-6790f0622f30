import { NextRequest, NextResponse } from 'next/server';
import { checkDatabaseConnection } from '@/lib/db/index';

export async function GET(request: NextRequest) {
  try {
    console.log('[health/db] Testing database connection...');
    
    const isHealthy = await checkDatabaseConnection();
    
    if (isHealthy) {
      return NextResponse.json({ 
        status: 'healthy', 
        message: 'Database connection successful',
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json({ 
        status: 'unhealthy', 
        message: 'Database connection failed',
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }
  } catch (error) {
    console.error('[health/db] Database health check error:', error);
    return NextResponse.json({ 
      status: 'error', 
      message: 'Database health check failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
