import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { sql } from 'drizzle-orm';

/**
 * API route for Clerk user management operations
 * This API allows administrators to perform operations on Clerk users
 * such as adding tokens, changing roles, etc.
 * 
 * All endpoints are protected and require admin privileges
 */
export async function POST(req: Request) {
  try {
    // Get the auth header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Extract JWT token
    const token = authHeader.split(' ')[1];
    if (!token) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
    
    // In a real implementation, verify the token and extract user ID
    // For simplicity, we'll use basic auth verification here
    // Normally, you would verify the JWT using Clerk's functions
    let userId: string;
    let isAdmin: boolean;
    
    try {
      // This is a placeholder for actual JWT verification
      // In a real implementation, you would use Clerk's verification methods
      const decodedToken = JSON.parse(atob(token.split('.')[1]));
      userId = decodedToken.sub;
      isAdmin = decodedToken.publicMetadata?.role === 'admin';
      
      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
      }
      
      if (!isAdmin) {
        return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
      }
    } catch (error) {
      console.error('Error verifying token:', error);
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Parse the request body
    const { action, targetUserId, data } = await req.json();

    // Handle different actions
    switch (action) {
      case 'add-tokens':
        return handleAddTokens(targetUserId, data.amount);
      case 'set-role':
        return handleSetRole(targetUserId, data.role);
      case 'get-user-details':
        return handleGetUserDetails(targetUserId);
      case 'list-users':
        return handleListUsers(data?.limit, data?.offset);
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in clerk management API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Handle the add-tokens action
 * Adds tokens to a user's balance in both Clerk metadata and our database
 */
async function handleAddTokens(userId: string, amount: number) {
  if (!userId || typeof amount !== 'number' || amount <= 0) {
    return NextResponse.json({ error: 'Invalid user ID or token amount' }, { status: 400 });
  }

  try {
    // 1. Get the Clerk client instance
    const client = await clerkClient();
    // 2. Get the current user from Clerk
    const clerkUser = await client.users.getUser(userId);
    if (!clerkUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // 2. Get current token balance from Clerk metadata
    const currentTokens = (clerkUser.publicMetadata?.tokens as number) || 0;
    const newTokenBalance = currentTokens + amount;

    // 3. Update tokens in Clerk metadata using the client instance
    await client.users.updateUser(userId, {
      publicMetadata: {
        ...clerkUser.publicMetadata,
        tokens: newTokenBalance,
      },
    });

    // 4. Update tokens in our database
    const dbUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.clerkId, userId),
    });

    if (dbUser) {
      await db
        .update(schema.users)
        .set({
          tokens: sql`${schema.users.tokens} + ${amount}`,
          updatedAt: new Date(),
        })
        .where(sql`${schema.users.clerkId} = ${userId}`);
    } else {
      // If the user doesn't exist in our database yet, 
      // the webhook might not have fired or processed yet
      console.log(`User ${userId} not found in database, cannot update tokens.`);
    }

    return NextResponse.json({
      success: true,
      user: {
        id: userId,
        previousTokens: currentTokens,
        addedTokens: amount,
        newTokenBalance: newTokenBalance,
      },
    });
  } catch (error) {
    console.error(`Error adding tokens to user ${userId}:`, error);
    return NextResponse.json({ error: 'Failed to add tokens' }, { status: 500 });
  }
}

/**
 * Handle the set-role action
 * Updates a user's role in Clerk metadata
 */
async function handleSetRole(userId: string, role: string) {
  if (!userId || !role) {
    return NextResponse.json({ error: 'User ID and role are required' }, { status: 400 });
  }

  // Validate the role
  const validRoles = ['user', 'admin', 'moderator'];
  if (!validRoles.includes(role)) {
    return NextResponse.json({ error: 'Invalid role' }, { status: 400 });
  }

  try {
    // Get the Clerk client instance
    const client = await clerkClient();
    // Update the user's role in Clerk metadata
    const clerkUser = await client.users.getUser(userId);
    if (!clerkUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    await client.users.updateUser(userId, {
      publicMetadata: {
        ...clerkUser.publicMetadata,
        role,
      },
    });

    return NextResponse.json({
      success: true,
      user: {
        id: userId,
        newRole: role,
      },
    });
  } catch (error) {
    console.error(`Error setting role for user ${userId}:`, error);
    return NextResponse.json({ error: 'Failed to set user role' }, { status: 500 });
  }
}

/**
 * Handle the get-user-details action
 * Retrieves detailed information about a user
 */
async function handleGetUserDetails(userId: string) {
  if (!userId) {
    return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
  }

  try {
    // Get the Clerk client instance
    const client = await clerkClient();
    // Get user from Clerk
    const clerkUser = await client.users.getUser(userId);
    if (!clerkUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user from our database
    const dbUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.clerkId, userId),
    });

    return NextResponse.json({
      success: true,
      user: {
        id: clerkUser.id,
        email: clerkUser.emailAddresses[0]?.emailAddress,
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        createdAt: clerkUser.createdAt,
        lastSignInAt: clerkUser.lastSignInAt,
        publicMetadata: clerkUser.publicMetadata,
        // Include database-specific fields if the user exists in our database
        database: dbUser ? {
          id: dbUser.id,
          tokens: dbUser.tokens,
          createdAt: dbUser.createdAt,
          updatedAt: dbUser.updatedAt,
        } : null,
      },
    });
  } catch (error) {
    console.error(`Error getting details for user ${userId}:`, error);
    return NextResponse.json({ error: 'Failed to get user details' }, { status: 500 });
  }
}

/**
 * Handle the list-users action
 * Lists all users with pagination
 */
async function handleListUsers(limit: number = 10, offset: number = 0) {
  try {
    // Get the Clerk client instance
    const client = await clerkClient();
    // Get users from Clerk
    const clerkUsers = await client.users.getUserList({
      limit,
      offset,
    });

    // Get corresponding database records
    // The getUserList returns a PaginatedResourceResponse, so we need to access the data property
    const userArray = Array.isArray(clerkUsers) ? clerkUsers : clerkUsers.data || [];
    const clerkIds = userArray.map((user: any) => user.id);
    
    // Only attempt to query the database if we have clerk IDs
    let dbUsersMap: Record<string, any> = {};
    if (clerkIds.length > 0) {
      const userRecords = await db
        .select()
        .from(schema.users)
        .where(sql`${schema.users.clerkId} IN (${clerkIds.join(',')})`);
      
      // Index by clerkId for easier lookup
      dbUsersMap = userRecords.reduce((acc: Record<string, any>, user) => {
        acc[user.clerkId] = user;
        return acc;
      }, {});
    }

    // Combine Clerk and database user information
    const users = userArray.map((clerkUser: any) => ({
      id: clerkUser.id,
      email: clerkUser.emailAddresses[0]?.emailAddress,
      firstName: clerkUser.firstName,
      lastName: clerkUser.lastName,
      createdAt: clerkUser.createdAt,
      publicMetadata: clerkUser.publicMetadata,
      // Include database fields if available
      databaseInfo: dbUsersMap[clerkUser.id] ? {
        id: dbUsersMap[clerkUser.id].id,
        tokens: dbUsersMap[clerkUser.id].tokens,
      } : null,
    }));

    return NextResponse.json({
      success: true,
      totalCount: users.length,
      users,
    });
  } catch (error) {
    console.error('Error listing users:', error);
    return NextResponse.json({ error: 'Failed to list users' }, { status: 500 });
  }
}

/**
 * GET handler for retrieving user information
 * This can be used to get information about the current user
 */
export async function GET(req: Request) {
  try {
    // Get the auth header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Extract JWT token
    const token = authHeader.split(' ')[1];
    if (!token) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
    
    // This is a placeholder for actual JWT verification
    // In a real implementation, you would use Clerk's verification methods
    try {
      const decodedToken = JSON.parse(atob(token.split('.')[1]));
      const userId = decodedToken.sub;
      
      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
      }
      
      // Get the Clerk client instance
      const client = await clerkClient();
      // Get user from Clerk
      const clerkUser = await client.users.getUser(userId);
      if (!clerkUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      // Get user from database
      const dbUser = await db.query.users.findFirst({
        where: (users, { eq }) => eq(users.clerkId, userId),
      });

      return NextResponse.json({
        success: true,
        user: {
          id: clerkUser.id,
          email: clerkUser.emailAddresses[0]?.emailAddress,
          firstName: clerkUser.firstName,
          lastName: clerkUser.lastName,
          createdAt: clerkUser.createdAt,
          publicMetadata: clerkUser.publicMetadata,
          // Include database-specific fields if the user exists in our database
          database: dbUser ? {
            id: dbUser.id,
            tokens: dbUser.tokens,
            createdAt: dbUser.createdAt,
            updatedAt: dbUser.updatedAt,
          } : null,
        },
      });
    } catch (error) {
      console.error('Error verifying token:', error);
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
  } catch (error) {
    console.error('Error in GET clerk management API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
