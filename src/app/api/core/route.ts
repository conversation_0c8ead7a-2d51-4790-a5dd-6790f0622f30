// src/app/api/core/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Webhook } from "svix"; // For Clerk Webhook verification
import { headers } from "next/headers";
import { Stripe } from 'stripe';
import axios from 'axios';
import { eq, sql } from "drizzle-orm";
import { v4 as uuidv4 } from 'uuid'; // Import UUID for generating report identifiers


// Import DB schema and functions
import * as schema from '@/lib/db/schema';
import { db, getGuestTransactionByPaymentId, updateGuestTransaction, getReportTransactionByPaymentId, updateReportTransaction } from '@/lib/db'; // Ensure createOrUpdateStripeUser exists if using stripeUsers table

// --- Initializations ---

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
   apiVersion: `2025-02-24.acacia` // Use your target Stripe API version
});

// FaceCheck API Config
const FACECHECK_API_BASE_URL = 'https://facecheck.id';
const FACECHECK_API_TOKEN = process.env.FACECHECK_API_KEY || '';

// --- Helper Functions ---

// Database Ping (Optional but good for keep-alive on free tiers)
async function pingDatabase(): Promise<boolean> {
  try {
    await db.execute(sql`SELECT 1`); // Use sql from drizzle-orm import
    console.log('[core-api] Database ping successful:', new Date().toISOString());
    return true;
  } catch (error) {
    console.error('[core-api] Error pinging database:', error);
    return false;
  }
}

// Cache for faces count to avoid rate limiting
interface FacesCache {
  count: number;
  timestamp: number;
  isOnline: boolean;
  remainingCredits?: number;
  hasCreditsToSearch?: boolean;
}

// In-memory cache - will be reset on server restart
let facesCache: FacesCache | null = null;
const CACHE_TTL_MS = 15 * 60 * 1000; // 15 minutes cache lifetime

// Function to get faces count with caching
async function getFacesCountWithCache(): Promise<FacesCache> {
  const now = Date.now();
  
  // Return cached value if it's still fresh
  if (facesCache && (now - facesCache.timestamp) < CACHE_TTL_MS) {
    console.log('[core-api] Using cached faces count:', facesCache.count);
    return facesCache;
  }

  try {
    // Call FaceCheck API to get fresh count
    console.log('[core-api] Cache expired or empty, fetching fresh faces count');
    const faceCheckResponse = await axios.post(`https://facecheck.id/api/info`, {}, {
      headers: {
        'Accept': 'application/json',
        'Authorization': FACECHECK_API_TOKEN,
      },
      timeout: 30000 // 30 seconds timeout to prevent hanging
    });

    // Update the cache with new values
    facesCache = {
      count: faceCheckResponse.data.faces || 990000000,
      isOnline: faceCheckResponse.data.is_online || true,
    //  remainingCredits: faceCheckResponse.data.remaining_credits || 0,
    //  hasCreditsToSearch: faceCheckResponse.data.has_credits_to_search || false,
      timestamp: now
    };
    
    console.log('[core-api] Updated faces count cache:', facesCache);
    return facesCache;
  } catch (error) {
    console.error('[core-api] Error fetching fresh faces count:', error);
    
    // If we have a stale cache, return that instead of failing
    if (facesCache) {
      console.log('[core-api] Returning stale cache after fetch error');
      return facesCache;
    }
    
    // If no cache at all, return default values
    return {
      count: 990000000, // Default to 990M
      isOnline: true,
      timestamp: now
    };
  }
}


// --- API Route Handlers ---

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      // Keep relevant GET actions, remove unused ones
      case 'ping-db':
        const status = await pingDatabase();
        return NextResponse.json({ success: status, timestamp: new Date().toISOString() });

      case 'faces':
        try {
          // Use the caching function instead of direct API call
          const cachedData = await getFacesCountWithCache();
          
          // Return the cached or freshly fetched data
          return NextResponse.json({
            faces: cachedData.count,
            is_online: cachedData.isOnline,
         //   remaining_credits: cachedData.remainingCredits || 0,
           // has_credits_to_search: cachedData.hasCreditsToSearch || false,
            cached: (Date.now() - cachedData.timestamp) > 0 // Indicate if data is from cache
          });
        } catch (error) {
          console.error('[core-api] Error in faces count handler:', error);
          // Return a default response to prevent UI breaking
          return NextResponse.json({
            faces: 990000000, // Default to 990M
            is_online: true,
          //  remaining_credits: 0,
          //  has_credits_to_search: false,
            error: 'Failed to fetch faces count information'
          });
        }

      case 'credits':
        try {
          // Call FaceCheck API to get remaining credits
          const faceCheckResponse = await axios.post(`${FACECHECK_API_BASE_URL}/api/info`, {}, {
            headers: {
              'Accept': 'application/json',
               'Authorization': FACECHECK_API_TOKEN,
            },
          });

          console.log('[core-api] Credits info retrieved successfully');

          // Return the remaining credits
          return NextResponse.json({
            remaining_credits: faceCheckResponse.data.remaining_credits || 0,
            has_credits_to_search: faceCheckResponse.data.has_credits_to_search || false
          });
        } catch (error) {
          console.error('[core-api] Error fetching credits:', error);
          return NextResponse.json({
            //remaining_credits: 0,
           //  has_credits_to_search: false,
            error: 'Failed to fetch credits information'
          });
        }

      default:
        return NextResponse.json({ error: 'Invalid GET action' }, { status: 400 });
    }
  } catch (error) {
    console.error('[core-api] Error in GET handler:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    // Keep essential POST actions
    switch (action) {
      case 'proxy':
        return await handleProxy(request); // Handles FaceCheck calls
      case 'create-payment-intent':
        return await handleCreatePaymentIntent(request); // Creates Stripe Payment Intent
      case 'clerk-webhook':
        return await handleClerkWebhook(request); // Handles user sync from Clerk
      case 'stripe-webhook':
        return await handleStripeWebhook(request); // Handles payment status from Stripe

      // Add other essential POST actions if any (e.g., creating Stripe customer explicitly if needed)
      // case 'create-stripe-customer': return await handleCreateStripeCustomer(request);

      default:
        return NextResponse.json({ error: 'Invalid POST action' }, { status: 400 });
    }
  } catch (error) {
    console.error('[core-api] Error in POST handler:', error);
    const message = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


// --- Specific Action Implementations ---

/**
 * Proxies requests to the FaceCheck API.
 * Handles /api/upload_pic (multipart) and /api/search (json).
 */
async function handleProxy(request: NextRequest) {
  try {
    await pingDatabase(); // Optional keep-alive

    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path'); // e.g., '/api/upload_pic' or '/api/search'

    if (!path || !FACECHECK_API_TOKEN) {
      const errorMsg = !path ? 'Missing API path for proxy' : 'FaceCheck API Key not configured';
      console.error(`[core-proxy] Error: ${errorMsg}`);
      return NextResponse.json({ error: errorMsg }, { status: 400 });
    }

    const targetUrl = `${FACECHECK_API_BASE_URL}${path}`;
    const headers = {
      'Accept': 'application/json',
      'Authorization': FACECHECK_API_TOKEN, // No 'Bearer ' prefix for FaceCheck
    };

    console.log(`[core-proxy] Forwarding request to: ${targetUrl}`);

    let faceCheckResponse;

    if (path === '/api/upload_pic') {
      // Handle multipart/form-data for image upload
      const formData = await request.formData();

      // FaceCheck expects 'images' as the field name, not 'file'
      // Check if we need to rename the field
      const originalFormData = formData;
      let modifiedFormData;

      // If the form data contains 'file' but not 'images', we need to rename it
      if (originalFormData.has('file') && !originalFormData.has('images')) {
        modifiedFormData = new FormData();
        // Get the file from the original form data
        const file = originalFormData.get('file');
        // Add it with the correct field name
        modifiedFormData.append('images', file as Blob);

        // Add any other fields from the original form data
        for (const [key, value] of originalFormData.entries()) {
          if (key !== 'file') {
            modifiedFormData.append(key, value);
          }
        }

        // Add id_search if not present
        if (!modifiedFormData.has('id_search')) {
          modifiedFormData.append('id_search', '');
        }

        console.log('[core-proxy] Modified form data to use field name "images" instead of "file"');
      } else {
        // Use the original form data
        modifiedFormData = originalFormData;
      }

      // Note: Axios might handle FormData differently in edge runtimes.
      // Consider using native fetch if issues arise or if running in edge.
      faceCheckResponse = await axios.post(targetUrl, modifiedFormData, {
        headers: {
          ...headers,
          // Axios typically sets Content-Type automatically for FormData
        },
        timeout: 300000, // Increase timeout for uploads (30s)
      });
      console.log(`[core-proxy] Upload response status: ${faceCheckResponse.status}`);

      // Check if the upload was successful and create a search result record with a unique identifier
      if (faceCheckResponse.status === 200 && faceCheckResponse.data) {
        try {
          // Get the id_search value from the form data or response
          const idSearch = modifiedFormData.get('id_search') || faceCheckResponse.data.id_search;
          // Get URL from the form data (blob URL)
          const blobUrl = modifiedFormData.get('images') ? URL.createObjectURL(modifiedFormData.get('images') as Blob) : '';

          // Extract report ID if present in the request
          const urlParams = new URL(request.url).searchParams;
          const reportId = urlParams.get('reportId');

          if (reportId && idSearch) {
            // Generate a unique report identifier
            const reportIdentifier = uuidv4();

            // Determine the Stripe payment ID if available
            let stripePaymentId = '';
            // Check URL for paymentIntentId parameter
            const paymentIntentId = urlParams.get('paymentIntentId');
            if (paymentIntentId) {
              stripePaymentId = paymentIntentId;
            }

            // We no longer create search results records here
            // The uploads table is used instead and is handled by the search API
            console.log(`[core-proxy] Using report identifier ${reportIdentifier} for report ${reportId}`);

            // Include the report identifier in the response
            faceCheckResponse.data.reportIdentifier = reportIdentifier;
          }
        } catch (error) {
          console.error('[core-proxy] Error processing report identifier:', error);
          // Continue without failing the request even if there's an error
        }
      }

    } else if (path === '/api/search') {
      // Handle JSON body for search status polling
      const jsonData = await request.json();
      faceCheckResponse = await axios.post(targetUrl, jsonData, {
        headers: {
          ...headers,
          'Content-Type': 'application/json',
        },
        timeout: 300000, // Timeout for search status check (25s)
      });
       console.log(`[core-proxy] Search response status: ${faceCheckResponse.status}, Progress: ${faceCheckResponse.data?.progress}, Output: ${!!faceCheckResponse.data?.output}`);

    } else if (path === '/api/info') {
      // Handle API info request
      try {
        faceCheckResponse = await axios.post(targetUrl, {}, {
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
          timeout: 8000 // 8 seconds timeout for info requests
        });
        console.log(`[core-proxy] Info response status: ${faceCheckResponse.status}`, faceCheckResponse.data);
        
        // Validate the faces count in the response
        if (faceCheckResponse.data && typeof faceCheckResponse.data.faces !== 'number') {
          console.warn(`[core-proxy] Invalid faces count in API response:`, faceCheckResponse.data);
          
          // Ensure we always return a faces count even if the API doesn't provide one
          if (!faceCheckResponse.data.faces) {
            faceCheckResponse.data.faces = 990000000; // Default to 990 million
            console.log(`[core-proxy] Added default faces count of 990M to response`);
          } else if (typeof faceCheckResponse.data.faces === 'string') {
            // Try to parse faces count from string
            try {
              const parsedCount = parseInt(faceCheckResponse.data.faces);
              if (!isNaN(parsedCount)) {
                faceCheckResponse.data.faces = parsedCount;
                console.log(`[core-proxy] Parsed faces count from string: ${parsedCount}`);
              } else {
                faceCheckResponse.data.faces = 990000000;
                console.log(`[core-proxy] Replaced unparseable faces count with 990M`);
              }
            } catch (parseError) {
              faceCheckResponse.data.faces = 990000000;
              console.log(`[core-proxy] Error parsing faces count, using default 990M`);
            }
          }
        }
      } catch (infoError) {
        console.error(`[core-proxy] Error fetching API info:`, infoError);
        // Return a default info response instead of failing
        return NextResponse.json({
          faces: 990000000, // 990 million
         // is_online: true,
       //   remaining_credits: 0, // No credits by default
      //    has_credits_to_search: false
        });
      }

    } else {
      console.warn(`[core-proxy] Unhandled proxy path: ${path}`);
      return NextResponse.json({ error: `Proxying for path '${path}' not implemented.` }, { status: 400 });
    }

    // Return FaceCheck's response body and status code
    return NextResponse.json(faceCheckResponse.data, { status: faceCheckResponse.status });

  } catch (error) {
    console.error('[core-proxy] Proxy Error:', error);
    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 502; // Bad Gateway if upstream fails
      const errorData = error.response?.data || { error: `Upstream request failed: ${error.message}` };
      console.error('[core-proxy] Upstream Error Details:', { status, data: errorData });
      // Avoid leaking detailed upstream errors unless necessary
      return NextResponse.json({ error: "Failed to communicate with the FaceCheck service." }, { status });
    }
    // Generic internal server error for other issues
    return NextResponse.json({ error: 'Internal server error during proxy request.' }, { status: 500 });
  }
}

/**
 * Creates a Stripe Payment Intent for the standard search price.
 */
async function handleCreatePaymentIntent(request: NextRequest) {
    try {
        const { email } = await request.json(); // Expect email in request body

        if (!email || typeof email !== 'string') {
            return NextResponse.json({ error: 'Valid email is required' }, { status: 400 });
        }

        console.log(`[core-payment] Creating $5 payment intent for ${email}`);

        // Find or create Stripe customer
        let customer: Stripe.Customer;
        const existingCustomers = await stripe.customers.list({ email: email, limit: 1 });

        if (existingCustomers.data.length > 0) {
            customer = existingCustomers.data[0];
            console.log(`[core-payment] Using existing Stripe customer: ${customer.id}`);
        } else {
            customer = await stripe.customers.create({ email });
            console.log(`[core-payment] Created new Stripe customer: ${customer.id}`);
        }

        // --- Optionally update our stripeUsers table ---
        // This part is needed ONLY if you implement the stripeUsers table/relation
        // const authDetails = getAuth(request);
        // if (authDetails.userId) {
        //     const user = await findUserByClerkId(authDetails.userId);
        //     if (user) {
        //          await createOrUpdateStripeUser({
        //             userId: user.id,
        //             stripeCustomerId: customer.id,
        //             email: email // Use the email confirmed for payment
        //         });
        //     }
        // }
        // --- End optional stripeUsers update ---


        const amount = 500; // $5.00 in cents
        const currency = 'usd';

        // Create PaymentIntent
        const paymentIntent = await stripe.paymentIntents.create({
            amount: amount,
            currency: currency,
            customer: customer.id, // Link to Stripe customer
            // setup_future_usage: 'off_session', // Optional: if you plan to save cards
            metadata: {
                customer_email: email, // Store email in metadata for reference
                service: 'facetrace_search',
                // Add other metadata if needed (e.g., internal user ID if available)
            },
            automatic_payment_methods: {
                enabled: true, // Allow Stripe to manage payment methods
            },
        });

        console.log(`[core-payment] Payment Intent ${paymentIntent.id} created for customer ${customer.id}.`);

        return NextResponse.json({ clientSecret: paymentIntent.client_secret });

    } catch (error) {
        console.error('[core-payment] Error creating Payment Intent:', error);
        const message = error instanceof Error ? error.message : 'Failed to create payment intent';
        return NextResponse.json({ error: message }, { status: 500 });
    }
}


/**
 * Handles incoming webhooks from Clerk to sync user data.
 */
async function handleClerkWebhook(req: Request) {
    const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

    if (!WEBHOOK_SECRET) {
        console.error("[clerk-webhook] CLERK_WEBHOOK_SECRET is not set");
        return new Response("Webhook secret not configured", { status: 500 });
    }

    // Get headers for Svix verification
    const headersList = await headers();
    const svix_id = headersList.get("svix-id");
    const svix_timestamp = headersList.get("svix-timestamp");
    const svix_signature = headersList.get("svix-signature");

    if (!svix_id || !svix_timestamp || !svix_signature) {
        console.warn("[clerk-webhook] Missing Svix headers");
        return new Response("Missing webhook headers", { status: 400 });
    }

    // Read the raw body
    const payload = await req.text(); // Read as text for verification

    // Verify the webhook signature
    const wh = new Webhook(WEBHOOK_SECRET);
    let evt: Record<string, any>; // Use a generic object type for the event

    try {
        evt = wh.verify(payload, {
            "svix-id": svix_id,
            "svix-timestamp": svix_timestamp,
            "svix-signature": svix_signature,
        }) as Record<string, any>; // Assert type after verification
    } catch (err) {
        console.error("[clerk-webhook] Error verifying webhook:", err);
        return new Response("Webhook verification failed", { status: 400 });
    }

    // Handle the event based on its type
    const eventType = evt.type;
    const eventData = evt.data; // Access data part of the event
    console.log(`[clerk-webhook] Received verified event: ${eventType}`);

    try {
        switch (eventType) {
            case "user.created":
                const { id: clerkId_created, email_addresses: emails_created, first_name: fn_created, last_name: ln_created } = eventData;
                const primaryEmail_created = emails_created?.find((e: any) => e.id === eventData.primary_email_address_id)?.email_address;
                 if (!primaryEmail_created) {
                    console.error(`[clerk-webhook] user.created: No primary email found for Clerk ID ${clerkId_created}`);
                    return new Response("Primary email not found", { status: 400 });
                 }
                 const name_created = `${fn_created || ''} ${ln_created || ''}`.trim();

                await db.insert(schema.users).values({
                    clerkId: clerkId_created,
                    email: primaryEmail_created,
                    name: name_created || null,
                    verified: false, // Assume email not verified initially unless event confirms it
                    tokens: 0, // Initial tokens set to 0, will be updated to 5 after phone verification
                    createdAt: new Date(eventData.created_at), // Use timestamp from Clerk event if possible
                    updatedAt: new Date(eventData.updated_at),
                }).onConflictDoNothing(); // Avoid errors if webhook retries
                console.log(`[clerk-webhook] User ${clerkId_created} created/synced in DB.`);
                break;

            case "user.updated":
                const { id: clerkId_updated, email_addresses: emails_updated, primary_email_address_id, first_name: fn_updated, last_name: ln_updated } = eventData;
                const primaryEmail_updated = emails_updated?.find((e: any) => e.id === primary_email_address_id)?.email_address;
                 if (!primaryEmail_updated) {
                    console.error(`[clerk-webhook] user.updated: No primary email found for Clerk ID ${clerkId_updated}`);
                    return new Response("Primary email not found", { status: 400 });
                 }
                 const name_updated = `${fn_updated || ''} ${ln_updated || ''}`.trim();

                await db.update(schema.users)
                    .set({
                        email: primaryEmail_updated,
                        name: name_updated || null,
                        updatedAt: new Date(eventData.updated_at), // Use Clerk's timestamp
                    })
                    .where(eq(schema.users.clerkId, clerkId_updated));
                console.log(`[clerk-webhook] User ${clerkId_updated} updated in DB.`);
                break;

            // Add email verification handling if needed (e.g., award tokens)
            // case "email.verified": // Check exact event type in Clerk docs
            //     const { /* relevant fields like user_id */ } = eventData;
            //     // Find user by clerkId (user_id)
            //     // Update user: set verified = true, maybe tokens += 3
            //     console.log(`[clerk-webhook] Email verified for user ${eventData.user_id}`);
            //     break;

             case "user.deleted":
                const { id: clerkId_deleted, deleted } = eventData;
                 if (deleted) { // Check the 'deleted' flag
                    await db.delete(schema.users).where(eq(schema.users.clerkId, clerkId_deleted));
                    console.log(`[clerk-webhook] User ${clerkId_deleted} deleted from DB.`);
                 } else {
                     console.warn(`[clerk-webhook] Received user.deleted event for ${clerkId_deleted} but deleted flag is false.`);
                 }
                 break;

            default:
                console.log(`[clerk-webhook] Received unhandled event type: ${eventType}`);
        }

        return new Response("Webhook processed successfully", { status: 200 });

    } catch (dbError) {
        console.error(`[clerk-webhook] Database error handling event ${eventType}:`, dbError);
        return new Response("Internal server error handling webhook", { status: 500 });
    }
}


/**
 * Handles incoming webhooks from Stripe to update payment status.
 */
async function handleStripeWebhook(request: NextRequest) {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || '';
    if (!webhookSecret) {
        console.error('[stripe-webhook] STRIPE_WEBHOOK_SECRET is not configured.');
        return new Response('Webhook secret not configured', { status: 500 });
    }

    const signature = (await headers()).get('stripe-signature');
    if (!signature) {
        console.error('[stripe-webhook] Missing stripe-signature header');
        return new Response('Missing webhook signature', { status: 400 });
    }

    const payload = await request.text(); // Read raw body for verification
    let event: Stripe.Event;

    try {
        event = stripe.webhooks.constructEvent(payload, signature, webhookSecret);
    } catch (err) {
        const message = err instanceof Error ? err.message : 'Unknown error';
        console.error(`[stripe-webhook] Webhook signature verification failed: ${message}`);
        return new Response(`Webhook Error: ${message}`, { status: 400 });
    }

    console.log(`[stripe-webhook] Received verified event: ${event.type}`);

    // Handle the event
    try {
        const paymentIntent = event.data.object as Stripe.PaymentIntent; // Common object type
        const paymentIntentId = paymentIntent?.id;

        if (!paymentIntentId) {
             console.warn(`[stripe-webhook] Event ${event.type} missing Payment Intent ID.`);
             return new Response('Missing Payment Intent ID in webhook object', { status: 400 });
        }

        // Find corresponding transaction in our DB (check both guest and user tables)
        const guestTx = await getGuestTransactionByPaymentId(paymentIntentId);
        const reportTx = await getReportTransactionByPaymentId(paymentIntentId);

        if (!guestTx && !reportTx) {
             console.warn(`[stripe-webhook] No matching transaction found in DB for Payment Intent ID: ${paymentIntentId}`);
             // Respond 200 OK to Stripe, as it's not an error on their end if we don't track it
             return new Response('Transaction not found, webhook acknowledged.', { status: 200 });
        }

        switch (event.type) {
            case 'payment_intent.succeeded':
                console.log(`[stripe-webhook] PaymentIntent ${paymentIntentId} succeeded.`);
                if (guestTx) {
                    await updateGuestTransaction(guestTx.id, { status: 'completed', failureReason: null, updatedAt: new Date() });
                }
                if (reportTx) {
                     await updateReportTransaction(reportTx.id, { status: 'completed', failureReason: null, updatedAt: new Date() });
                }
                break;

            case 'payment_intent.payment_failed':
                 const failureReason = paymentIntent.last_payment_error?.message || 'Payment failed (no specific reason provided)';
                console.log(`[stripe-webhook] PaymentIntent ${paymentIntentId} failed. Reason: ${failureReason}`);
                 if (guestTx) {
                    await updateGuestTransaction(guestTx.id, { status: 'failed', failureReason: failureReason, updatedAt: new Date() });
                }
                if (reportTx) {
                     await updateReportTransaction(reportTx.id, { status: 'failed', failureReason: failureReason, updatedAt: new Date() });
                }
                break;

            // Add handlers for other events if needed (e.g., charge.refunded)

            default:
                console.log(`[stripe-webhook] Unhandled event type: ${event.type}`);
        }

        return new Response('Webhook received and processed.', { status: 200 });

    } catch (dbError) {
         console.error(`[stripe-webhook] Database error handling event ${event.type}:`, dbError);
        return new Response('Internal server error handling webhook', { status: 500 });
    }
}
