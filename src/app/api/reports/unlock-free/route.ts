import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import {
  getSearchReportById,
  getSearchReportByReportId,
  updateSearchReport
} from '@/lib/db/index';

// Check if authentication is disabled
const isAuthDisabled = process.env.DISABLE_AUTH === 'true';

/**
 * Free unlock endpoint - bypasses payment requirements
 * This endpoint allows unlocking reports without payment for development/testing
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { reportId, email } = await req.json();
    
    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    // TEMPORARY DISABLE: Skip auth check if authentication is disabled
    let userId = null;
    if (!isAuthDisabled) {
      const authResult = await auth();
      userId = authResult.userId;
    }
    
    console.log(`[unlock-free] Processing free unlock for report ${reportId}, user ${userId || 'guest'}`);
    
    // Get the report to unlock - Handle both numeric IDs and string path identifiers
    let report;
    
    // Check if reportId is a number or string path identifier
    if (/^\d+$/.test(reportId)) {
      // It's a numeric ID
      console.log(`[unlock-free] Using database ID ${reportId}`);
      report = await getSearchReportById(reportId);
    } else {
      // It's a string path identifier (URL path id)
      console.log(`[unlock-free] Using URL path ID ${reportId}`);
      report = await getSearchReportByReportId(reportId);
    }
    
    if (!report) {
      console.warn(`[unlock-free] Report ${reportId} not found`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // Check if report is already unlocked
    if (!report.isPrivate) {
      console.log(`[unlock-free] Report ${reportId} is already unlocked`);
      return NextResponse.json({ 
        success: true, 
        message: 'Report is already unlocked'
      });
    }
    
    // Update report to unlock it (free unlock)
    await updateSearchReport(report.id, {
      isPrivate: false,
      price: '0.00', // Set price to $0.00 since this was a free unlock
      stripe_pm_id: 'free_unlock_' + Date.now(), // Store a free unlock identifier
      // Keep existing user ID - we don't change user associations on unlock
    });
    
    console.log(`[unlock-free] Successfully unlocked report ${reportId} via free unlock`);
    
    // Return success
    return NextResponse.json({
      success: true,
      message: 'Report unlocked successfully (free)',
      reportId: report.report_id
    });
  } catch (error) {
    console.error('[unlock-free] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to unlock report',
      details: errorMessage
    }, { status: 500 });
  }
}
