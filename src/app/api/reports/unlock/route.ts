import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getSearchReportById, getSearchReportByReportId, updateSearchReport } from '@/lib/db/index';
import <PERSON><PERSON> from 'stripe';

// Create Stripe instance with the specific API version
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not defined in environment variables');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-02-24.acacia', // Specified version from requirements
  typescript: true,
});

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { reportId, paymentIntentId, email } = await req.json();
    
    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    if (!paymentIntentId) {
      return NextResponse.json({ error: 'Payment Intent ID is required' }, { status: 400 });
    }

    // Get current user (if signed in)
    const { userId } = await auth();
    
    console.log(`[unlock] Processing payment unlock for report ${reportId} with payment ${paymentIntentId}, user ${userId || 'guest'}`);
    
    // 1. Verify payment with Stripe
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      // Ensure the payment was successful
      if (paymentIntent.status !== 'succeeded') {
        console.warn(`[unlock] Payment ${paymentIntentId} status is ${paymentIntent.status}, not succeeded`);
        return NextResponse.json({ 
          error: 'Payment was not successful',
          status: paymentIntent.status
        }, { status: 400 });
      }
      
      // Verify the payment amount is $5.00 (500 cents)
      if (paymentIntent.amount !== 500) {
        console.warn(`[unlock] Payment ${paymentIntentId} amount ${paymentIntent.amount} does not match expected 500 cents`);
        return NextResponse.json({ 
          error: 'Payment amount does not match expected amount',
          amount: paymentIntent.amount
        }, { status: 400 });
      }
      
      // Check that this payment hasn't been used for a previous unlock
      // This would be ideal to prevent double-use of the same payment
      // Would require a table of used payment intents or checking a payment_used flag on the report
      
    } catch (error) {
      console.error('[unlock] Error verifying payment with Stripe:', error);
      return NextResponse.json({ 
        error: 'Could not verify payment with Stripe',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }
    
    // 2. Get the report to unlock - Handle both numeric IDs and string path identifiers
    let report;
    
    // Check if reportId is a number or string path identifier
    if (/^\d+$/.test(reportId)) {
      // It's a numeric ID
      console.log(`[unlock] Using database ID ${reportId}`);
      report = await getSearchReportById(reportId);
    } else {
      // It's a string path identifier (URL path id)
      console.log(`[unlock] Using URL path ID ${reportId}`);
      report = await getSearchReportByReportId(reportId);
    }
    
    if (!report) {
      console.warn(`[unlock] Report ${reportId} not found`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // 3. Check if report is already unlocked
    if (!report.isPrivate) {
      console.log(`[unlock] Report ${reportId} is already unlocked`);
      return NextResponse.json({ 
        success: true, 
        message: 'Report is already unlocked'
      });
    }
    
    // 4. Update report to unlock it
    await updateSearchReport(report.id, {
      isPrivate: false,
      price: '5.00', // Set price to $5.00 since this was a payment-based unlock
      stripe_pm_id: paymentIntentId, // Store the payment intent ID
      // Keep existing user ID - we don't change user associations on unlock
    });
    
    console.log(`[unlock] Successfully unlocked report ${reportId} via payment ${paymentIntentId}`);
    
    // 5. Return success
    return NextResponse.json({
      success: true,
      message: 'Report unlocked successfully',
      reportId: report.report_id
    });
  } catch (error) {
    console.error('[unlock] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to unlock report',
      details: errorMessage
    }, { status: 500 });
  }
}
