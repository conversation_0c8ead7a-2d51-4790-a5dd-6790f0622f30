import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getSearchReportById, getSearchReportByReportId, updateSearchReport, findUserByClerkId, updateUser } from '@/lib/db/index';

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { reportId } = await req.json();
    
    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    // Get current user (must be signed in)
    const { userId: clerkId } = await auth();
    
    if (!clerkId) {
      console.warn(`[unlock-for-new-user] Unauthorized attempt to unlock report ${reportId}`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    console.log(`[unlock-for-new-user] Processing free unlock for new user ${clerkId} for report ${reportId}`);
    
    // 1. Get the report to unlock - Handle both numeric IDs and string path identifiers
    let report;
    
    // Check if reportId is a number or string path identifier
    if (/^\d+$/.test(reportId)) {
      // It's a numeric ID
      console.log(`[unlock-for-new-user] Using database ID ${reportId}`);
      report = await getSearchReportById(reportId);
    } else {
      // It's a string path identifier (URL path id)
      console.log(`[unlock-for-new-user] Using URL path ID ${reportId}`);
      report = await getSearchReportByReportId(reportId);
    }
    
    if (!report) {
      console.warn(`[unlock-for-new-user] Report ${reportId} not found`);
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }
    
    // 2. Check if report is already unlocked
    if (!report.isPrivate) {
      console.log(`[unlock-for-new-user] Report ${reportId} is already unlocked`);
      return NextResponse.json({ 
        success: true, 
        message: 'Report is already unlocked'
      });
    }
    
    // 3. Find the user in our database
    console.log(`[unlock-for-new-user] Attempting to find user with Clerk ID: ${clerkId} for report ${reportId}`);
    const user = await findUserByClerkId(clerkId);
    console.log(`[unlock-for-new-user] Result of findUserByClerkId for ${clerkId}:`, user ? `Found (ID: ${user.id})` : 'NOT FOUND');

    if (!user) {
      console.warn(`[unlock-for-new-user] User with clerk ID ${clerkId} not found in database`);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // 4. Check if the user is eligible for the initial free unlock
    if (user.initialFreeUnlockUsed) {
      console.warn(`[unlock-for-new-user] User ${clerkId} (DB ID: ${user.id}) has already used their free unlock. Returning 409.`);
      return NextResponse.json({ success: false, error: 'You have already used your first free report unlock.' }, { status: 409 }); // 409 Conflict
    }
    
    // 5. DIRECTLY update report to unlock it and mark the user's free unlock as used
    console.log(`[unlock-for-new-user] Unlocking report ${reportId} for new user ${clerkId} as a free promotion`);
    
    try {
      // Update the report to unlock it
      await updateSearchReport(report.id, {
        isPrivate: false,
      });
      
      // Mark the user's free unlock as used
      await updateUser(user.id, {
        initialFreeUnlockUsed: true
      });
      
      console.log(`[unlock-for-new-user] Successfully unlocked report ${reportId} for new user ${clerkId} and marked initialFreeUnlockUsed=true`);
      
      // 6. Return success
      return NextResponse.json({
        success: true,
        message: 'Report unlocked successfully for new user',
        reportId: report.report_id,
        tokens: user.tokens // Send back current token count for UI reference
      });
    } catch (updateError) {
      console.error(`[unlock-for-new-user] Failed to update report ${reportId}:`, updateError);
      return NextResponse.json({ 
        error: 'Failed to unlock report', 
        details: updateError instanceof Error ? updateError.message : 'Unknown error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('[unlock-for-new-user] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to unlock report',
      details: errorMessage
    }, { status: 500 });
  }
}
