import { GoogleGenerativeA<PERSON>, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import { NextRequest, NextResponse } from 'next/server';

// Initialize the Gemini API with your API key
// You'll need to add your API key to your environment variables
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY || '');

// Available Gemini models
export const GEMINI_MODELS = {
  GEMINI_PRO: 'gemini-pro',
  GEMINI_PRO_VISION: 'gemini-pro-vision',
  GEMINI_2_5_PRO_PREVIEW: 'gemini-2.5-pro-exp-03-25', // Using experimental version for free tier access
};

// Default generation config based on provided examples
export const DEFAULT_GENERATION_CONFIG = {
  temperature: 1,
  topP: 0.95,
  topK: 64,
  maxOutputTokens: 65536,
  responseMimeType: "text/plain",
};

// Safety settings
const SAFETY_SETTINGS = [
  {
    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      prompt, 
      model = GEMINI_MODELS.GEMINI_2_5_PRO_PREVIEW,
      temperature = DEFAULT_GENERATION_CONFIG.temperature,
      topP = DEFAULT_GENERATION_CONFIG.topP,
      topK = DEFAULT_GENERATION_CONFIG.topK,
      maxOutputTokens = DEFAULT_GENERATION_CONFIG.maxOutputTokens,
      history = [],
      chatMode = false
    } = body;

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    const generationConfig = {
      temperature,
      topP,
      topK,
      maxOutputTokens,
      responseMimeType: DEFAULT_GENERATION_CONFIG.responseMimeType,
    };

    // For text-only input, setup model with configuration
    const geminiModel = genAI.getGenerativeModel({ 
      model,
      generationConfig,
      safetySettings: SAFETY_SETTINGS,
    });

    let result;
    if (chatMode) {
      // Use chat mode
      const chatSession = geminiModel.startChat({
        generationConfig,
        history,
      });
      
      result = await chatSession.sendMessage(prompt);
      
      // Handle potential multiple candidates and parts
      const candidates = result.response.candidates || [];
      const responseParts = [];
      
      for (const candidate of candidates) {
        if (candidate.content?.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData) {
              // Store inline data references to return to client
              responseParts.push({
                type: 'inlineData',
                mimeType: part.inlineData.mimeType,
                data: part.inlineData.data
              });
            } else if (part.text) {
              responseParts.push({
                type: 'text',
                text: part.text
              });
            }
          }
        }
      }
      
      return NextResponse.json({ 
        response: result.response.text(),
        parts: responseParts,
        candidates: candidates
      });
    } else {
      // Regular content generation (non-chat)
      result = await geminiModel.generateContent(prompt);
      const response = await result.response;
      return NextResponse.json({ response: response.text() });
    }
  } catch (error) {
    console.error('Gemini API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

// For multimodal input (text + images), use this handler
export async function PUT(request: NextRequest) {
  try {
    const formData = await request.formData();
    const prompt = formData.get('prompt') as string;
    const imageFile = formData.get('image') as File;
    const modelName = (formData.get('model') as string) || GEMINI_MODELS.GEMINI_PRO_VISION;
    
    // Extract generation config settings if provided
    const temperature = formData.get('temperature') ? 
      parseFloat(formData.get('temperature') as string) : 
      DEFAULT_GENERATION_CONFIG.temperature;
    
    const topP = formData.get('topP') ? 
      parseFloat(formData.get('topP') as string) : 
      DEFAULT_GENERATION_CONFIG.topP;
    
    const topK = formData.get('topK') ? 
      parseInt(formData.get('topK') as string, 10) : 
      DEFAULT_GENERATION_CONFIG.topK;
    
    const maxOutputTokens = formData.get('maxOutputTokens') ? 
      parseInt(formData.get('maxOutputTokens') as string, 10) : 
      DEFAULT_GENERATION_CONFIG.maxOutputTokens;

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Configure generation settings
    const generationConfig = {
      temperature,
      topP,
      topK,
      maxOutputTokens,
      responseMimeType: DEFAULT_GENERATION_CONFIG.responseMimeType,
    };

    // For text + image input, use the gemini-pro-vision model with config
    const model = genAI.getGenerativeModel({ 
      model: modelName,
      generationConfig,
      safetySettings: SAFETY_SETTINGS,
    });

    let result;
    
    if (imageFile) {
      // Handle image + text input
      const imageData = await imageFile.arrayBuffer();
      const imageParts = [
        {
          inlineData: {
            data: Buffer.from(imageData).toString('base64'),
            mimeType: imageFile.type,
          },
        },
      ];

      // Generate content from prompt + image
      result = await model.generateContent([prompt, ...imageParts]);
    } else {
      // Fall back to text-only if no image was provided
      result = await model.generateContent(prompt);
    }

    const response = await result.response;
    const text = response.text();

    return NextResponse.json({ response: text });
  } catch (error) {
    console.error('Gemini API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
