'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { AlertCircle } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function SARFormPage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="max-w-3xl mx-auto"
          >
            <motion.h1 
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-8"
            >
              Subject Access Request (SAR) Form
            </motion.h1>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8"
            >
              <div className="prose prose-blue dark:prose-invert max-w-none mb-8">
                <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Introduction</h2>
                <p className="text-gray-700 dark:text-gray-300">
                  Under data protection laws, you have the right to request access to the personal information we hold about you. 
                  This form is designed to help you make a Subject Access Request (SAR).
                </p>
                
                <div className="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 p-4 my-6">
                  <div className="flex">
                    <AlertCircle className="h-5 w-5 text-blue-500 dark:text-blue-400 mr-2" />
                    <p className="text-blue-700 dark:text-blue-400 text-sm">
                      We are committed to protecting your privacy rights and will process your request within 30 calendar days as required by law.
                    </p>
                  </div>
                </div>
              </div>
              
              <form className="space-y-6">
                <div className="bg-blue-50/50 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800 mb-6">
                  <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Your Details</h3>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Full Name</label>
                      <input type="text" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Email Address</label>
                      <input type="email" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Account Email (if different)</label>
                      <input type="email" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Phone Number</label>
                      <input type="tel" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-2">I am a:</label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="radio" name="userType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                        <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Current user</span>
                      </label>
                      <label className="flex items-center">
                        <input type="radio" name="userType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                        <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Former user</span>
                      </label>
                      <label className="flex items-center">
                        <input type="radio" name="userType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                        <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Person whose image may appear in search results</span>
                      </label>
                      <label className="flex items-center">
                        <input type="radio" name="userType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                        <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Other</span>
                      </label>
                      <input type="text" placeholder="Please specify" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md mt-1" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-blue-50/50 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800 mb-6">
                  <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Type of Request</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400 mb-3">Please indicate what type of request you are making:</p>
                  
                  <div className="grid md:grid-cols-2 gap-2">
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Access to my personal data</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Information about how my data is being processed</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Rectification of inaccurate personal data</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Erasure of my personal data ("right to be forgotten")</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Restriction of processing of my personal data</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Data portability (receiving a copy of my data)</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Objection to processing</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Information about automated decision-making</span>
                    </label>
                    
                    <label className="flex items-center p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30 md:col-span-2">
                      <input type="radio" name="requestType" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800" />
                      <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">Other</span>
                    </label>
                    <div className="md:col-span-2">
                      <input type="text" placeholder="Please specify" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-blue-50/50 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800 mb-6">
                  <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Details of the Information Requested</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400 mb-3">Please provide specific details about the information you are requesting. Be as specific as possible to help us efficiently process your request:</p>
                  
                  <textarea 
                    className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" 
                    rows={5}
                    placeholder="E.g., 'I would like all personal data you hold about me, including account details, search history, and uploaded images.'"
                  ></textarea>
                  
                  <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-2 mt-6">Time Period</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400 mb-3">Please specify the time period for which you are requesting information:</p>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">From</label>
                      <input type="date" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">To</label>
                      <input type="date" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-blue-50/50 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800 mb-6">
                  <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Identity Verification</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400 mb-3">
                    To protect your privacy and security, we need to verify your identity before fulfilling your request. 
                    Please attach one of the following documents:
                  </p>
                  
                  <ul className="list-disc pl-6 mb-4 text-sm text-blue-700 dark:text-blue-400">
                    <li>A clear photo of your face (similar to what you would use for our face search service)</li>
                    <li>A copy of a government-issued photo ID with sensitive information redacted (only your photo and name should be visible)</li>
                  </ul>
                  
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-2">Upload Verification Document</label>
                    <input type="file" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md bg-white dark:bg-gray-700" />
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">Max file size: 5MB. Accepted formats: JPG, PNG, PDF.</p>
                  </div>
                </div>
                
                <div className="bg-blue-50/50 dark:bg-blue-900/20 p-4 rounded-md border border-blue-100 dark:border-blue-800 mb-6">
                  <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-4">Declaration</h3>
                  
                  <div className="mb-4">
                    <p className="text-sm text-blue-700 dark:text-blue-400">
                      I, <input type="text" className="border-b border-blue-300 dark:border-blue-700 bg-transparent mx-1 px-2 w-64" placeholder="Your full name" />, confirm that I am the data subject named in this form.
                    </p>
                  </div>
                  
                  <p className="text-sm text-blue-700 dark:text-blue-400 mb-4">I understand that:</p>
                  <ol className="list-decimal pl-6 mb-4 text-sm text-blue-700 dark:text-blue-400">
                    <li>You may need additional information to confirm my identity and process my request.</li>
                    <li>My request will be processed within 30 calendar days of receipt of a fully completed form and proper identification.</li>
                    <li>The information provided in this form will be used solely for the purpose of identifying the personal data I am requesting and responding to my request.</li>
                  </ol>
                  
                  <label className="flex items-center mt-4">
                    <input type="checkbox" className="h-4 w-4 text-blue-600 border-blue-300 dark:border-blue-800 rounded" />
                    <span className="ml-2 text-sm text-blue-700 dark:text-blue-400">I certify that the information provided on this form is correct and true to the best of my knowledge.</span>
                  </label>
                  
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Digital Signature (Type your full name)</label>
                    <input type="text" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                  </div>
                  
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Date</label>
                    <input type="date" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md" />
                  </div>
                </div>
                
                <button 
                  type="submit" 
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors"
                >
                  Submit SAR Request
                </button>
              </form>
              
              <div className="mt-8 text-sm text-blue-700 dark:text-blue-400">
                <h3 className="font-medium mb-2">What Happens Next?</h3>
                <ol className="list-decimal pl-6">
                  <li>We will acknowledge receipt of your request within 3 business days.</li>
                  <li>We may contact you for additional information if needed to verify your identity.</li>
                  <li>We will provide a response to your request within 30 calendar days.</li>
                  <li>If your request is complex, we may extend the response period by up to an additional 60 days, and we will inform you of any extension.</li>
                </ol>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 