'use client';

import * as React from "react";
import { motion } from "framer-motion";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function RefundPolicyPage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="prose prose-blue dark:prose-invert max-w-4xl mx-auto"
          >
            <motion.h1 
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-6"
            >
              Refund Policy
            </motion.h1>
            
            <motion.p 
              variants={itemVariants}
              className="text-gray-500 dark:text-gray-400 mb-8"
            >
              Effective Date: March 8, 2025
            </motion.p>

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Overview</h2>
              <p className="mb-4 text-gray-700 dark:text-gray-300">
                We are committed to providing a high-quality service and ensuring customer satisfaction. If you are unhappy for any reason, you are entitled to a one-time full refund, no questions asked. This good-faith refund allows you to try our service risk-free. However, once a refund has been issued, any future purchases made on our platform will be subject to the standard refund policy outlined below.
              </p>
              <p className="mb-4 text-gray-700 dark:text-gray-300">
                We strive to resolve any concerns before a chargeback occurs. Chargebacks create unnecessary complications for both parties, and we encourage customers to contact us directly so we can work toward a fair resolution. We will do everything possible to address your concerns and ensure a satisfactory outcome. If you are experiencing an issue, please reach out to our support team, and we will make every effort to resolve it quickly and fairly.
              </p>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Standard Refund Eligibility</h2>
              
              <div className="bg-blue-50 dark:bg-blue-900/30 p-6 rounded-lg border border-blue-200 dark:border-blue-800 mb-6">
                <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-3">Eligible for a Refund:</h3>
                <p className="mb-2 text-gray-700 dark:text-gray-300">A subscription fee may be refunded if requested within 14 days of purchase, provided that:</p>
                <ul className="list-disc ml-6 space-y-1 text-blue-700 dark:text-blue-400">
                  <li>You have not used any of the subscription features.</li>
                  <li>You have not performed any searches.</li>
                  <li>You have not set up any alerts.</li>
                  <li>Technical issues on our part prevented you from using the service.</li>
                </ul>
              </div>

              <div className="bg-red-50 dark:bg-red-900/30 p-6 rounded-lg border border-red-200 dark:border-red-800">
                <h3 className="text-lg font-medium text-red-800 dark:text-red-300 mb-3">Not Eligible for a Refund:</h3>
                <p className="mb-2 text-gray-700 dark:text-gray-300">Refunds cannot be issued under the following conditions:</p>
                <ul className="list-disc ml-6 space-y-1 text-red-700 dark:text-red-400">
                  <li>You have performed one or more searches.</li>
                  <li>You have set up one or more alerts.</li>
                  <li>You have used any premium features.</li>
                  <li>You have deleted your account.</li>
                  <li>You have violated our Terms of Service.</li>
                  <li>Your subscription is older than 14 days.</li>
                </ul>
              </div>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Refund Process</h2>
              <p className="mb-3 text-gray-700 dark:text-gray-300">To request a refund:</p>
              <ol className="list-decimal ml-6 space-y-2 mb-4 text-gray-700 dark:text-gray-300">
                <li><strong>Submit a Request:</strong> Contact our customer support team via our contact form or <NAME_EMAIL>.</li>
                <li><strong>Provide Information:</strong> Include your account email, purchase date, and reason for the refund request.</li>
                <li><strong>Verification:</strong> Our team will review your eligibility based on the criteria outlined above.</li>
                <li><strong>Decision:</strong> You will receive a response within 5 business days regarding the status of your refund request.</li>
              </ol>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Payment Processing</h2>
              <p className="mb-3 text-gray-700 dark:text-gray-300">If your refund is approved:</p>
              <ul className="list-disc ml-6 space-y-2 text-gray-700 dark:text-gray-300">
                <li>The refund will be issued to the original payment method used for purchase.</li>
                <li>Processing times vary by payment provider:
                  <ul className="list-disc ml-6 mt-2 space-y-1">
                    <li>Credit/Debit Cards: 5-10 business days.</li>
                    <li>PayPal: 3-5 business days.</li>
                    <li>Other Payment Methods: Varies depending on the provider.</li>
                  </ul>
                </li>
              </ul>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Pro-Rated Refunds</h2>
              <p className="text-gray-700 dark:text-gray-300">
                In certain cases, we may offer pro-rated refunds for unused portions of long-term subscriptions. These are reviewed on a case-by-case basis and are not guaranteed.
              </p>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Exceptional Circumstances</h2>
              <p className="mb-4 text-gray-700 dark:text-gray-300">
                We understand that unforeseen circumstances may arise. At our sole discretion, we may consider refunds outside of our standard policy in cases such as:
              </p>
              <ul className="list-disc ml-6 space-y-1 mb-4 text-gray-700 dark:text-gray-300">
                <li>Extended service outages.</li>
                <li>Documented medical emergencies.</li>
                <li>Other extenuating circumstances (supporting documentation may be required).</li>
              </ul>
              <p className="text-gray-700 dark:text-gray-300">Each request will be individually reviewed based on the specific situation.</p>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Cancellation vs. Refund</h2>
              <ul className="list-disc ml-6 space-y-2 text-gray-700 dark:text-gray-300">
                <li><strong>Cancellation:</strong> Stops automatic renewal but allows access until the end of the current billing period.</li>
                <li><strong>Refund:</strong> Returns payment and immediately terminates access to services.</li>
              </ul>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Changes to This Policy</h2>
              <p className="text-gray-700 dark:text-gray-300">
                We may update this Refund Policy periodically. Any changes will be posted on our website, and the effective date will be updated accordingly.
              </p>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="my-8 h-px bg-blue-200 dark:bg-blue-800" 
            />

            <motion.section 
              variants={itemVariants}
              className="mb-10"
            >
              <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">Contact Us</h2>
              <p className="mb-2 text-gray-700 dark:text-gray-300">
                If you have any questions about this Refund Policy or need assistance with a refund request, please contact us:
              </p>
              <ul className="list-none space-y-2 text-gray-700 dark:text-gray-300">
                <li className="flex items-center">
                  <span className="mr-2">📧</span> Email: <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 ml-1 hover:underline"><EMAIL></a>
                </li>
                <li className="flex items-center">
                  <span className="mr-2">📩</span> Support Form: Available on our website
                </li>
              </ul>
            </motion.section>

            <motion.div 
              variants={itemVariants}
              className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mt-12"
            >
              <h3 className="text-xl font-medium text-blue-800 dark:text-blue-300 mb-4">Request a Refund</h3>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Your Email</label>
                  <input type="email" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="The email associated with your account" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Purchase Date</label>
                  <input type="date" className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Reason for Refund Request</label>
                  <select className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select a reason</option>
                    <option value="first-time">First-time refund request (no questions asked)</option>
                    <option value="notused">Haven't used the service</option>
                    <option value="techissue">Technical issues</option>
                    <option value="notasexpected">Service not as expected</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">Additional Details</label>
                  <textarea className="w-full px-3 py-2 border border-blue-300 dark:border-blue-800 dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500" rows={4} placeholder="Please provide any additional information to support your refund request"></textarea>
                </div>
                
                <button 
                  type="submit" 
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-900"
                >
                  Submit Refund Request
                </button>
              </form>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 