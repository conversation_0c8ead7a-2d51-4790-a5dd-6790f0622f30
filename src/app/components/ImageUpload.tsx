'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import Image from 'next/image';
import { Button } from './Button';
import { convertHeicToJpeg, compressImage } from '../services/apiService';

interface ProcessedImage {
  id: string;
  file: File;
  previewUrl: string;
  originalSize: number;
  compressedSize: number;
}

interface ImageUploadProps {
  onImagesSelect: (files: File[]) => void;
  isProcessing: boolean;
  onCompressionChange?: (isCompressing: boolean) => void;
  onReset?: () => void;
}

export default function ImageUpload({ onImagesSelect, isProcessing, onCompressionChange, onReset }: ImageUploadProps) {
  const [processedImages, setProcessedImages] = useState<ProcessedImage[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [isCompressing, setIsCompressing] = useState(false);

  // Format file size to human-readable format
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const processFile = async (file: File): Promise<ProcessedImage> => {
    try {
      // Convert HEIC to JPEG if needed
      const convertedFile = await convertHeicToJpeg(file);
      
      // Get original size before compression
      const originalSize = convertedFile.size;
      
      // Compress image to 6MB or less as per API requirement
      const compressedFile = await compressImage(convertedFile);
      
      // Create a unique ID for this image
      const id = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(compressedFile);
      
      return {
        id,
        file: compressedFile,
        previewUrl,
        originalSize,
        compressedSize: compressedFile.size
      };
    } catch (error) {
      console.error('Error processing image:', error);
      throw error;
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      try {
        setDragActive(false);
        setIsCompressing(true);
        if (onCompressionChange) onCompressionChange(true);
        
        // Alert if trying to upload more than 3 images total
        const totalImages = processedImages.length + acceptedFiles.length;
        if (totalImages > 3) {
          alert(`Maximum of 3 images allowed. Only the first ${3 - processedImages.length} will be processed.`);
        }
        
        // Limit to 3 images total
        const filesToProcess = acceptedFiles.slice(0, Math.min(acceptedFiles.length, 3 - processedImages.length));
        
        if (processedImages.length >= 3) {
          alert('Maximum of 3 images allowed. Please remove some images first.');
          setIsCompressing(false);
          if (onCompressionChange) onCompressionChange(false);
          return;
        }
        
        // Process each file
        const newProcessedImages = await Promise.all(
          filesToProcess.map(processFile)
        );
        
        // Update state with new processed images
        setProcessedImages(prev => [...prev, ...newProcessedImages]);
        
        // Pass all files to parent component
        const allFiles = [...processedImages, ...newProcessedImages].map(img => img.file);
        onImagesSelect(allFiles);
      } catch (error) {
        console.error('Error processing images:', error);
        alert('Failed to process one or more images. Please try again.');
      } finally {
        setIsCompressing(false);
        if (onCompressionChange) onCompressionChange(false);
      }
    }
  }, [processedImages, onImagesSelect, onCompressionChange]);

  const removeImage = (id: string) => {
    // Remove the image from the processed images list
    const updatedImages = processedImages.filter(img => img.id !== id);
    
    // Revoke the object URL to avoid memory leaks
    const imageToRemove = processedImages.find(img => img.id === id);
    if (imageToRemove) {
      URL.revokeObjectURL(imageToRemove.previewUrl);
    }
    
    // If this was the last image, reset the entire component
    if (updatedImages.length === 0) {
      setProcessedImages([]);
      // Pass empty array to parent component
      onImagesSelect([]);
      // Call onReset if provided
      if (onReset) onReset();
    } else {
      // Otherwise just update the images
      setProcessedImages(updatedImages);
      // Pass updated files to parent component
      const allFiles = updatedImages.map(img => img.file);
      onImagesSelect(allFiles);
    }
  };

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      processedImages.forEach(img => {
        URL.revokeObjectURL(img.previewUrl);
      });
    };
  }, [processedImages]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/jpg': [],
      'image/png': [],
      'image/webp': [],
      'image/heic': [],
    },
    maxSize: 50 * 1024 * 1024, // 50MB max as per requirements
    disabled: isProcessing || isCompressing || processedImages.length >= 3,
  });

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div 
        {...getRootProps()} 
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300 relative
          ${isDragActive ? 'border-[#92A5FF] bg-[#92A5FF]/5' : 'border-gray-300 dark:border-gray-700 hover:border-[#92A5FF] hover:bg-[#92A5FF]/5'}
          ${processedImages.length > 0 ? 'border-[#92A5FF]' : ''}
          ${isProcessing || isCompressing || processedImages.length >= 3 ? 'opacity-70 cursor-not-allowed' : ''}
        `}
        onDragEnter={() => setDragActive(true)}
        onDragLeave={() => setDragActive(false)}
        onDrop={() => setDragActive(false)}
      >
        <input {...getInputProps()} disabled={isProcessing || isCompressing || processedImages.length >= 3} />
        
        {/* Reset Button - Moved to top right corner */}
        {processedImages.length > 0 && !isProcessing && !isCompressing && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              // Revoke all object URLs
              processedImages.forEach(img => {
                URL.revokeObjectURL(img.previewUrl);
              });
              
              // Clear state
              setProcessedImages([]);
              
              // Notify parent component
              onImagesSelect([]);
              
              // Call onReset if provided
              if (onReset) onReset();
            }}
            className="absolute top-3 right-3 z-10 bg-red-500 text-white rounded-full p-2 shadow-sm hover:bg-red-600 transition-colors"
            title="Reset All Images"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 6L6 18M6 6l12 12"></path>
            </svg>
          </button>
        )}
        
        {processedImages.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="mb-6 bg-[#92A5FF]/10 p-5 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-[#92A5FF] w-16 h-16">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17 8 12 3 7 8"></polyline>
                <line x1="12" y1="3" x2="12" y2="15"></line>
              </svg>
            </div>
            <p className="mb-3 text-lg font-semibold text-gray-700 dark:text-gray-300">
              Drag & drop up to 3 photos here or click to browse
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800/60 px-4 py-2 rounded-full">
              JPEG, JPG, PNG, WEBP, HEIC (max 50MB each)
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center w-full">
            {/* Mobile view: Horizontal slider with reduced padding */}
            <div className="flex w-full overflow-x-auto pb-4 sm:hidden snap-x snap-mandatory scrollbar-hide">
              <div className="flex space-x-3 px-1">
                {processedImages.map((img) => (
                  <div key={img.id} className="flex-shrink-0 w-60 snap-center">
                    <div className="relative border rounded-md overflow-hidden shadow-lg transform transition-transform hover:scale-[1.02] group h-40">
                      {/* Mobile view - simplified card with gradient background */}
                      <div className="w-full h-40 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-t-md p-3 flex flex-col justify-center items-center text-white relative z-10">
                        <div className="text-xl font-bold mb-1">
                          {processedImages.indexOf(img) + 1} of 3
                        </div>
                        <div className="text-lg">
                          Images Selected
                        </div>
                        <div className="mt-2 text-xl font-mono bg-white/20 px-3 py-1 rounded-md">
                          {img.file.name.split('.').pop()?.toUpperCase()}
                        </div>
                      </div>

                      {/* Remove button - visible without hover on mobile */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeImage(img.id);
                        }}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 z-20 opacity-100 transition-opacity shadow-sm"
                        disabled={isProcessing}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                    
                    {/* File info for mobile */}
                    <div className="p-2 bg-gray-50 dark:bg-gray-800 text-xs rounded-b-md">
                      <p className="truncate text-gray-700 dark:text-gray-300 font-medium">
                        {img.file.name}
                      </p>
                    </div>
                  </div>
                ))}
                
                {/* Add more button as part of the slider on mobile */}
                {processedImages.length < 3 && (
                  <div className="flex-shrink-0 w-60 snap-center">
                    <label
                      htmlFor="add-more-mobile"
                      className="h-40 rounded-md border-2 border-dashed border-gray-200 dark:border-gray-700 hover:border-[#92A5FF] hover:bg-[#92A5FF]/5 flex items-center justify-center cursor-pointer transition-all duration-300"
                    >
                      <input
                        type="file"
                        id="add-more-mobile"
                        className="sr-only"
                        accept="image/*,.heic"
                        multiple
                        {...getInputProps()} // Use dropzone input props
                      />
                      <div className="text-center">
                        <div className="w-12 h-12 bg-[#92A5FF]/15 rounded-full flex items-center justify-center mx-auto mb-2">
                          <svg className="w-7 h-7 text-[#92A5FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                        </div>
                        <span className="text-sm text-[#92A5FF] font-bold block mb-1">Add more</span>
                        <p className="text-xs text-blue-600 dark:text-blue-300 px-3 py-0.5 rounded-full bg-blue-50 dark:bg-blue-900/20">
                          {3 - processedImages.length} more allowed
                        </p>
                      </div>
                    </label>
                  </div>
                )}
              </div>
            </div>
            
            {/* Desktop view: Grid layout */}
            <div className="hidden sm:grid sm:grid-cols-2 md:grid-cols-3 gap-4 w-full mb-4">
              {processedImages.map((img) => (
                <div key={img.id} className="relative border rounded-md overflow-hidden shadow-lg transform transition-transform hover:scale-[1.02] group">
                  <div className="relative w-full h-40">
                    <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#92A5FF]/70 to-[#FFA1BF]/70 text-white py-1.5 px-3 text-xs font-medium z-10 backdrop-blur-sm flex justify-between items-center rounded-b-none">
                      <span>{processedImages.indexOf(img) + 1} of 3</span>
                      <span className="uppercase">{img.originalSize !== img.compressedSize ? 'Compressed' : 'Original'}</span>
                    </div>
                    <Image 
                      src={img.previewUrl} 
                      alt="Preview" 
                      fill
                      style={{ objectFit: 'cover' }}
                      className="rounded-md z-10"
                      priority={processedImages.indexOf(img) === 0}
                    />
                  </div>
                  
                  {/* Remove button - visible on both mobile and desktop */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeImage(img.id);
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 z-20 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity shadow-sm"
                    disabled={isProcessing}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                  
                  {/* File information - adjusted for mobile */}
                  <div className="p-2 bg-gray-50 dark:bg-gray-800 text-xs">
                    <p className="truncate text-gray-700 dark:text-gray-300 font-medium">
                      {img.file.name}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400 sm:block">
                      Original: {formatFileSize(img.originalSize)}
                    </p>
                    <p className="text-green-600 dark:text-green-400 sm:block">
                      Compressed: {formatFileSize(img.compressedSize)}
                    </p>
                  </div>
                </div>
              ))}
              
              {processedImages.length < 3 && (
                <label
                  htmlFor="add-more-desktop"
                  className="flex items-center justify-center border-2 border-dashed rounded-md h-40 text-gray-400 hover:border-[#92A5FF] hover:bg-[#92A5FF]/5 transition-all duration-300 cursor-pointer"
                >
                  <input
                    type="file"
                    id="add-more-desktop"
                    className="sr-only"
                    accept="image/*,.heic"
                    multiple
                    {...getInputProps()} // Use dropzone input props
                  />
                  <div className="text-center">
                    <div className="w-10 h-10 bg-[#92A5FF]/10 rounded-full flex items-center justify-center mx-auto mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#92A5FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <p className="text-sm font-medium">Add more images</p>
                    <p className="text-xs text-[#92A5FF]">{3 - processedImages.length} more allowed</p>
                  </div>
                </label>
              )}
            </div>
            
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {processedImages.length < 3
                ? 'Drag & drop more photos or click to browse'
                : 'Maximum number of images reached (3)'}
            </p>
          </div>
        )}
        
        {isCompressing && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-sm sm:bg-black/50 rounded-lg z-20">
            <div className="flex flex-col items-center text-white bg-black/60 p-3 rounded-xl">
              <svg className="animate-spin h-8 w-8 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-base font-medium">
                Compressing images...
              </p>
            </div>
          </div>
        )}
        
        {isProcessing && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-sm sm:bg-black/50 rounded-lg z-20">
            <div className="flex flex-col items-center text-white bg-black/60 p-3 rounded-xl">
              <svg className="animate-spin h-8 w-8 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-base font-medium">
                Processing...
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
