'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { getFacesCount } from '../services/apiService';

const formatNumberWithCommas = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '...';
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

type LogoProps = {
  className?: string;
  vertical?: boolean;
};

export default function Logo({ className = '', vertical = false }: LogoProps) {
  const [facesCount, setFacesCount] = useState<number | undefined>(undefined); // Initialize as undefined
  const [isLogoHovered, setIsLogoHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchFacesCount = async () => {
      try {
        setIsLoading(true);
        const count = await getFacesCount();
        setFacesCount(count);
      } catch (error) {
        console.error('Error fetching faces count:', error);
        // Keep using undefined on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchFacesCount();
  }, []);

  return (
    <Link href="/" className={`flex ${vertical ? 'flex-col items-center' : 'flex-wrap items-center'} ${className}`}>
      <div className={`relative h-10 w-10 sm:h-12 sm:w-12 ${vertical ? 'mb-1 sm:mb-2' : ''}`}
        onMouseEnter={() => setIsLogoHovered(true)}
        onMouseLeave={() => setIsLogoHovered(false)}
      >
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          animate={isLogoHovered ? { scale: [1, 1.05, 1] } : {}}
          transition={{ duration: 0.5 }}
        >
          {/* Custom background for the logo */}
          <div className="relative">
            {/* Logo background gradient - changed to oval */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 scale-x-[0.9]"></div>

            {/* Geometric FaceTrace logo SVG */}
            <svg className="relative h-10 w-10 sm:h-12 sm:w-12 z-10" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              {/* Face outline - geometric with bolder stroke filling entire space */}
              <polygon 
                points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25" 
                stroke="white" 
                strokeWidth="4" 
                strokeOpacity="1" 
                fill="none" 
                strokeLinejoin="round"
              />
              
              {/* Biometric identification grid */}
              <path d="M50,10 L50,90" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />
              <path d="M15,50 L85,50" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />
              <path d="M25,25 L75,75" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />
              <path d="M25,75 L75,25" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />
              
              {/* Eyes - geometric with thicker stroke */}
              <rect x="32" y="40" width="8" height="7" rx="1" stroke="white" strokeWidth="2.5" strokeOpacity="1" fill="none" />
              <rect x="60" y="40" width="8" height="7" rx="1" stroke="white" strokeWidth="2.5" strokeOpacity="1" fill="none" />
              
              {/* Mouth - straight line for serious expression */}
              <path d="M35,65 L65,65" stroke="white" strokeWidth="2" strokeOpacity="0.9" fill="none" strokeLinecap="round" />
              
              {/* Intersection points with larger dots */}
              <circle cx="50" cy="10" r="1.5" fill="white" fillOpacity="1" />
              <circle cx="25" cy="25" r="1.5" fill="white" fillOpacity="1" />
              <circle cx="15" cy="50" r="1.5" fill="white" fillOpacity="1" />
              <circle cx="25" cy="75" r="1.5" fill="white" fillOpacity="1" />
              <circle cx="50" cy="90" r="1.5" fill="white" fillOpacity="1" />
              <circle cx="75" cy="75" r="1.5" fill="white" fillOpacity="1" />
              <circle cx="85" cy="50" r="1.5" fill="white" fillOpacity="1" />
              <circle cx="75" cy="25" r="1.5" fill="white" fillOpacity="1" />
              
              {/* Biometric measurement points */}
              <circle cx="50" cy="45" r="1" fill="white" fillOpacity="0.7" />
              <circle cx="50" cy="65" r="1" fill="white" fillOpacity="0.7" />
              <circle cx="35" cy="50" r="1" fill="white" fillOpacity="0.7" />
              <circle cx="65" cy="50" r="1" fill="white" fillOpacity="0.7" />
              
              {/* Scanner effect - enhanced */}
              <motion.path 
                d="M15 50 L85 50" 
                stroke="#00FFFF" 
                strokeWidth="1.2" 
                strokeOpacity="0.7"
                initial={{ opacity: 0.3 }}
                animate={{ 
                  y: [0, 20, 0, -20, 0],
                  opacity: [0.3, 0.8, 0.3]
                }}
                transition={{ 
                  duration: 2.5, 
                  repeat: Infinity,
                  repeatType: "loop"
                }}
              />
            </svg>
          </div>
        </motion.div>
      </div>
      
      <div className={`flex flex-col ${vertical ? 'items-center' : 'ml-1 sm:ml-2'}`}>
        <div className="font-bold text-base sm:text-lg flex items-center">
          <span className="font-['Electrolize','SF_Mono',monospace] tracking-wide bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">FaceTrace</span>
          <div className="mx-0.5 w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
          <span className="font-['Electrolize','SF_Mono',monospace] bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent">Pro</span>
        </div>
        
        <div className={`flex items-center ${vertical ? 'justify-center w-full max-w-[150px]' : ''} bg-blue-50 dark:bg-blue-900/50 rounded-md px-2 py-0.5 shadow-sm border border-blue-100 dark:border-blue-800 backdrop-blur-sm mt-1`}>
          <span className="text-[9px] font-bold text-blue-700 mr-1 dark:text-blue-300 font-['Electrolize','SF_Mono',monospace] uppercase tracking-wide">Faces:</span>
          {isLoading || facesCount === undefined ? (
            <motion.span
              className="font-['Electrolize','SF_Mono',monospace] text-[9px] text-blue-800 font-semibold dark:text-blue-200 tracking-tight"
              animate={{ opacity: [1, 0.5, 1] }}
              transition={{ 
                duration: 1.2, 
                repeat: Infinity,
                ease: "easeInOut" 
              }}
            >
              ...
            </motion.span>
          ) : (
            <motion.span
              className="font-['Electrolize','SF_Mono',monospace] text-[9px] text-blue-800 font-semibold dark:text-blue-200 tracking-tight"
              animate={{ opacity: isLogoHovered ? [1, 0.7, 1] : 1 }}
              transition={{ duration: 1, repeat: isLogoHovered ? Infinity : 0 }}
            >
              {formatNumberWithCommas(facesCount)}
            </motion.span>
          )}
        </div>
      </div>
    </Link>
  );
} 