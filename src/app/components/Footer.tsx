'use client';

import { Insta<PERSON>, Mail, MapPin, Heart } from "lucide-react";
import Link from 'next/link';
import React from "react";

// Custom TikTok icon since it's not in lucide-react
const TikTokIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M9 12a4 4 0 1 0 4 4V4a5 5 0 0 0 5 5" />
  </svg>
);

export default function Footer() {
  return (
    <footer className="bg-blue-950 text-blue-200 py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* FaceTrace Info */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-white mb-4">FaceTrace</h3>
            <p className="text-sm text-blue-100/90 leading-relaxed">
            Discover where your photos appear online by analyzing visual characteristics and comparing them with public web content. FaceTrace is a privacy-focused platform helping you understand your online presence.
            </p>
          </div>

          {/* Main Links */}
          <div>
            <h3 className="text-xl font-bold text-white mb-4">Main Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/#pricing" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Pricing
                </Link>
              </li>
              <li>
                <Link href="/opt-out" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Opt-Out
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          {/* Support & Legal */}
          <div>
            <h3 className="text-xl font-bold text-white mb-4">Support & Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/contact" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms-of-use" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Terms of Use
                </Link>
              </li>
              <li>
                <Link href="/legal" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Legal Disclaimer
                </Link>
              </li>
              <li>
                <Link href="/csr" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Corporate Responsibility
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-xl font-bold text-white mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/report-bug" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Report a Bug
                </Link>
              </li>
              <li>
                <Link href="/refund-policy" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Refund Policy
                </Link>
              </li>
              <li>
                <Link href="/dmca-takedown" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  DMCA Takedown
                </Link>
              </li>
              <li>
                <Link href="/sar-form" className="text-blue-100/90 hover:text-blue-200 transition-colors">
                  Data Access Request
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Connect */}
          <div>
            <h3 className="text-xl font-bold text-white mb-4">Connect</h3>
            <div className="flex gap-4 mb-6">
              <a href="https://instagram.com/facetrace.pro" target="_blank" rel="noopener noreferrer" className="text-blue-100/90 hover:text-blue-200 transition-colors" aria-label="Instagram">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="https://www.tiktok.com/@facetrace.pro" target="_blank" rel="noopener noreferrer" className="text-blue-100/90 hover:text-blue-200 transition-colors" aria-label="TikTok">
                <TikTokIcon className="w-5 h-5" />
              </a>
              <a href="https://discord.gg/GqcmwyeDpu" target="_blank" rel="noopener noreferrer" className="text-blue-100/90 hover:text-blue-200 transition-colors" aria-label="Discord">
                <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 127.14 96.36" fill="currentColor">
                  <path d="M107.7,8.07A105.15,105.15,0,0,0,81.47,0a72.06,72.06,0,0,0-3.36,6.83A97.68,97.68,0,0,0,49,6.83,72.37,72.37,0,0,0,45.64,0,105.89,105.89,0,0,0,19.39,8.09C2.79,32.65-1.71,56.6.54,80.21h0A105.73,105.73,0,0,0,32.71,96.36,77.7,77.7,0,0,0,39.6,85.25a68.42,68.42,0,0,1-10.85-5.18c.91-.66,1.8-1.34,2.66-2a75.57,75.57,0,0,0,64.32,0c.87.71,1.76,1.39,2.66,2a68.68,68.68,0,0,1-10.87,5.19,77,77,0,0,0,6.89,11.1A105.25,105.25,0,0,0,126.6,80.22h0C129.24,52.84,122.09,29.11,107.7,8.07ZM42.45,65.69C36.18,65.69,31,60,31,53s5-12.74,11.43-12.74S54,46,53.89,53,48.84,65.69,42.45,65.69Zm42.24,0C78.41,65.69,73.25,60,73.25,53s5-12.74,11.44-12.74S96.23,46,96.12,53,91.08,65.69,84.69,65.69Z"/>
                </svg>
              </a>
            </div>
            <div className="space-y-2">
              <a href="mailto:<EMAIL>" className="flex items-center gap-2 text-blue-100/90 hover:text-blue-200 transition-colors">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </a>
              <div className="flex items-start gap-2 text-blue-100/90">
                <div className="mt-1 flex-shrink-0">
                  <MapPin className="w-4 h-4" />
                </div>
                <span>Miami Beach, Florida</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-blue-900 mt-12 pt-8 text-center">
          <p className="text-blue-100/90 text-sm">
            © {new Date().getFullYear()} FaceTrace. All rights reserved.
          </p>
          <p className="font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#5158f6] text-sm mt-2 flex items-center justify-center">
            Built with <Heart className="h-3 w-3 mx-1 text-blue-500" /> in Miami Beach
          </p>
        </div>
      </div>
    </footer>
  );
}
