'use client';

import Image from 'next/image';
import { useState, useEffect, useMemo } from 'react';
import { SearchResult } from '../services/apiService';
import { Button } from './Button';
import { exportResultsAsCSV, createSearchAlert } from '../services/apiService';
import { extractUrlFromBase64, exportResultsToTextFile } from '../utils/extractResults';

interface ReportPageProps {
  results: SearchResult[];
  loading: boolean;
  reportId?: string; // Add report ID to allow updating report data
}

interface ExtractedResult {
  id: string;
  score: number;
  url: string;
  sourceUrl: string;
  thumbnail: string;
}

// Background pattern and gradient styles
const styles = {
  bgPattern: {
    backgroundColor: '#f7faff',
    backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%),
    radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
    backgroundSize: '100% 100%',
    backgroundPosition: 'center'
  },
  aiFaceBg: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 30% 30%, rgba(146, 165, 255, 0.15) 0%, rgba(146, 165, 255, 0) 70%),
      radial-gradient(circle at 70% 70%, rgba(255, 161, 191, 0.15) 0%, rgba(255, 161, 191, 0) 70%),
      url("data:image/svg+xml,%3Csvg width='800' height='800' viewBox='0 0 800 800' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%2392A5FF' stroke-width='1' stroke-opacity='0.2' d='M400,40 L250,680 M400,40 L550,680 M250,680 L550,680 M200,300 L600,300 M250,450 L550,450 M300,100 L300,300 M500,100 L500,300 M300,100 L500,100 M220,180 C220,180 300,200 400,200 C500,200 580,180 580,180'/%3E%3C/svg%3E")`,
    backgroundSize: '150% 150%',
    backgroundPosition: 'center',
    opacity: 0.4,
    pointerEvents: 'none' as const,
    zIndex: -1,
  }
};

export default function ReportPage({ results, loading, reportId }: ReportPageProps) {
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [selectedWebsites, setSelectedWebsites] = useState<string[]>([]);
  const [alertEmail, setAlertEmail] = useState('');
  const [isCreatingAlert, setIsCreatingAlert] = useState(false);
  const [alertSuccess, setAlertSuccess] = useState(false);
  const [alertError, setAlertError] = useState<string | null>(null);
  const [extractedResults, setExtractedResults] = useState<ExtractedResult[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [backgroundFocus, setBackgroundFocus] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  
  // Process incoming results to extract embedded URLs
  useEffect(() => {
    if (results.length > 0) {
      const processed = results.map(result => {
        // Extract embedded URL from base64 data if possible
        const extractedUrl = result.thumbnail ? extractUrlFromBase64(result.thumbnail) : null;
        
        return {
          id: result.id,
          score: result.confidence,
          url: extractedUrl || result.sourceUrl,
          sourceUrl: result.sourceUrl,
          thumbnail: result.thumbnail || ''
        };
      });
      
      setExtractedResults(processed as ExtractedResult[]);
      
      // If we have a reportId, create search_results entries
      if (reportId && results.length > 0) {
        saveSearchResults(reportId, results);
      }
    } else {
      setExtractedResults([]);
    }
  }, [results, reportId]);
  
  // Function to save search results to database
  const saveSearchResults = async (reportId: string, results: SearchResult[]) => {
    try {
      setIsSaving(true);
      // Use the correct endpoint: /api/data?action=save-results
      const response = await fetch(`/api/data?action=save-results&id=${reportId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId,
          results,
          userId: null // Explicitly set userId to null for guest transactions
        }),
      });
      
      if (!response.ok) {
        console.error('Failed to save search results');
      }
    } catch (error) {
      console.error('Error saving search results:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  const filteredResults = activeFilter 
    ? results.filter(r => r.sourceType === activeFilter)
    : results;
    
  const handleExportCSV = () => {
    setIsExporting(true);
    try {
      exportResultsAsCSV(filteredResults);
    } finally {
      setTimeout(() => setIsExporting(false), 1000);
    }
  };
  
  const handleExportTXT = () => {
    setIsExporting(true);
    try {
      // Export the extracted URLs in the format shown in the Python script
      const urlsForExport = extractedResults
        .filter(result => result.url) // Only include results with valid URLs
        .map(result => ({
          score: result.score,
          url: result.url
        }));
        
      exportResultsToTextFile(urlsForExport);
    } finally {
      setTimeout(() => setIsExporting(false), 1000);
    }
  };
  
  const handleCreateAlert = async () => {
    if (!alertEmail) {
      setAlertError('Please enter your email address');
      return;
    }
    
    try {
      setIsCreatingAlert(true);
      setAlertError(null);
      
      // Create alert with the email and reportId
      const searchId = reportId || 'unknown';
      await createSearchAlert(searchId, alertEmail);
      
      setAlertSuccess(true);
      setAlertEmail('');
    } catch (error) {
      console.error('Error creating alert:', error);
      setAlertError('Failed to create alert. Please try again.');
    } finally {
      setIsCreatingAlert(false);
    }
  };
  
  // Common container to maintain consistent background during all states
  const containerClasses = "container mx-auto px-4 py-8 max-w-6xl";
  
  // Loading state with the same background as the results view
  if (loading) {
    return (
      <div className={containerClasses}>
        {/* Enhanced Report Header Card - Skeleton Version */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-6 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold text-white">Processing Your Search</h2>
            </div>
          </div>
          
          <div className="p-8">
            <div className="flex flex-col items-center justify-center">
              <div className="w-20 h-20 mb-4">
                <svg className="animate-spin w-full h-full text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Generating Your Report</h3>
              <p className="text-gray-600 dark:text-gray-400 text-center max-w-md mb-4">
                We're searching across our database for matches. This usually takes under a minute.
              </p>
              
              {/* Fake progress bar to indicate activity */}
              <div className="w-full max-w-md bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2 overflow-hidden">
                <div className="bg-blue-600 h-2.5 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Analyzing potential matches...</p>
            </div>
          </div>
        </div>
        
        {/* Placeholder grid to maintain layout */}
        <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm p-4">
              <div className="animate-pulse">
                <div className="bg-gray-300 dark:bg-gray-600 w-full h-48 rounded-md mb-4"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-5/6 mb-2"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-4/6 mb-4"></div>
                <div className="flex justify-end">
                  <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  // No results state with the same background as the results view
  if (results.length === 0) {
    return (
      <div className={containerClasses}>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-6 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold text-white">Search Complete</h2>
            </div>
          </div>
          
          <div className="p-8 text-center">
            <svg className="w-20 h-20 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Matches Found</h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              We couldn't find any matches for this image in our database. This could mean this face hasn't appeared online, or the matches are below our confidence threshold.
            </p>
            <div className="mt-6">
              <Button
                variant="primary"
                onClick={() => window.location.href = '/search'}
              >
                Try Another Search
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Enhanced Report Header Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-white">Search Report Results</h2>
            <div className="flex space-x-2">
              <Button 
                variant="outline"
                size="sm"
                className="bg-white/10 text-white border-white/20 hover:bg-white/20"
                onClick={() => window.print()}
                leftIcon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                  </svg>
                }
              >
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="bg-white/10 text-white border-white/20 hover:bg-white/20"
                disabled={true}
                leftIcon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                }
              >
                Delete Report
              </Button>
            </div>
          </div>
        </div>
        
        <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Stats Summary */}
          <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              <h3 className="font-semibold text-blue-800 dark:text-blue-300">Total Matches</h3>
            </div>
            <p className="text-3xl font-bold text-blue-900 dark:text-blue-200">{results.length}</p>
            <p className="text-sm text-blue-700 dark:text-blue-400 mt-1">Across {new Set(results.map(r => r.sourceType)).size} source types</p>
          </div>
          
          <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <svg className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
              </svg>
              <h3 className="font-semibold text-purple-800 dark:text-purple-300">Confidence</h3>
            </div>
            <p className="text-3xl font-bold text-purple-900 dark:text-purple-200">
              {Math.max(...results.map(r => r.confidence)).toFixed(1)}%
            </p>
            <p className="text-sm text-purple-700 dark:text-purple-400 mt-1">Highest match confidence</p>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center mb-2">
                  <svg className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                  <h3 className="font-semibold text-green-800 dark:text-green-300">Actions</h3>
                </div>
                <p className="text-sm text-green-700 dark:text-green-400 mt-1">Download or file removal</p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportCSV}
                  isLoading={isExporting}
                  className="text-green-600 dark:text-green-400 border-green-300 dark:border-green-700"
                >
                  CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportTXT}
                  isLoading={isExporting}
                  className="text-green-600 dark:text-green-400 border-green-300 dark:border-green-700"
                >
                  URLs
                </Button>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="w-full mt-2 text-gray-600 dark:text-gray-400"
              disabled={true}
            >
              Removal Request
            </Button>
          </div>
        </div>
      </div>
      
      {/* Filter Section */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button
          onClick={() => setActiveFilter(null)}
          variant={activeFilter === null ? "primary" : "outline"}
          size="sm"
        >
          All Sources ({results.length})
        </Button>
        <Button
          onClick={() => setActiveFilter('social')}
          variant={activeFilter === 'social' ? "primary" : "outline"}
          size="sm"
        >
          Social Media
        </Button>
        <Button
          onClick={() => setActiveFilter('news')}
          variant={activeFilter === 'news' ? "primary" : "outline"}
          size="sm"
        >
          News
        </Button>
        <Button
          onClick={() => setActiveFilter('government')}
          variant={activeFilter === 'government' ? "primary" : "outline"}
          size="sm"
        >
          Government
        </Button>
        <Button
          onClick={() => setActiveFilter('other')}
          variant={activeFilter === 'other' ? "primary" : "outline"}
          size="sm"
        >
          Other
        </Button>
      </div>
      
      {/* Confidence Meter */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 w-full">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Confidence Distribution</h3>
          <div className="flex items-center space-x-1">
            <div className="h-3 rounded-l-full bg-red-500" style={{ width: `${(results.filter(r => r.confidence < 70).length / results.length) * 100}%` }}></div>
            <div className="h-3 bg-yellow-500" style={{ width: `${(results.filter(r => r.confidence >= 70 && r.confidence < 83).length / results.length) * 100}%` }}></div>
            <div className="h-3 bg-blue-500" style={{ width: `${(results.filter(r => r.confidence >= 83 && r.confidence < 90).length / results.length) * 100}%` }}></div>
            <div className="h-3 rounded-r-full bg-green-500" style={{ width: `${(results.filter(r => r.confidence >= 90).length / results.length) * 100}%` }}></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>50%</span>
            <span>70%</span>
            <span>83%</span>
            <span>90%</span>
            <span>100%</span>
          </div>
        </div>
      </div>
      
      {/* Results grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredResults.map((result) => {
          // Find the corresponding extracted result
          const extracted = extractedResults.find(er => er.id === result.id);
          const extractedUrl = extracted?.url || result.sourceUrl;
          
          return (
            <div key={result.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
              <div className="relative aspect-video bg-gray-100 dark:bg-gray-900">
                <Image 
                  src={result.thumbnail || '/placeholder-image.jpg'}
                  alt={result.title || "Search result"} 
                  fill
                  style={{ objectFit: 'cover' }}
                  className="rounded-t-lg"
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded-full">
                  {result.confidence.toFixed(1)}%
                </div>
                <div className={`absolute bottom-0 left-0 right-0 h-1 ${getConfidenceColorClass(result.confidence)}`}></div>
              </div>
              
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                    {result.title || "Unknown Source"}
                  </h3>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSourceTypeClass(result.sourceType || 'other')}`}>
                    {result.sourceType || 'Other'}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <span className="font-medium">Source:</span> {result.title || "Unknown"}
                </p>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 break-words">
                  <span className="font-medium">URL:</span>{' '}
                  <a 
                    href={extractedUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {extractedUrl.length > 40 ? extractedUrl.substring(0, 40) + '...' : extractedUrl}
                  </a>
                </p>
                
                {result.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <span className="font-medium">Date:</span> {
                      result.description.match(/Found on (.+)/) && 
                      result.description.match(/Found on (.+)/)![1] || 
                      result.description
                    }
                  </p>
                )}
                
                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(extractedUrl, '_blank')}
                    leftIcon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                      </svg>
                    }
                  >
                    Visit Source
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Alert subscription */}
      <div className="mt-10 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-center mb-3">
          <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Get Notified of New Matches
          </h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          We'll scan the web regularly and alert you when new matches are found.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <input
            type="email"
            value={alertEmail}
            onChange={(e) => setAlertEmail(e.target.value)}
            placeholder="Enter your email"
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
          <Button
            onClick={handleCreateAlert}
            isLoading={isCreatingAlert}
            leftIcon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
            }
          >
            Create Alert
          </Button>
        </div>
        
        {alertError && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">{alertError}</p>
        )}
        
        {alertSuccess && (
          <p className="mt-2 text-sm text-green-600 dark:text-green-400">
            Alert created successfully! We'll notify you of any new matches.
          </p>
        )}
      </div>
    </div>
  );
}

// Helper function to get color class based on confidence score
function getConfidenceColorClass(confidence: number): string {
  if (confidence >= 90) return 'bg-green-500';
  if (confidence >= 83) return 'bg-blue-500';
  if (confidence >= 70) return 'bg-yellow-500';
  return 'bg-red-500';
}

// Helper function to get color class based on source type
function getSourceTypeClass(sourceType: string): string {
  switch (sourceType) {
    case 'social':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200';
    case 'news':
      return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200';
    case 'government':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700/50 dark:text-gray-200';
  }
}
