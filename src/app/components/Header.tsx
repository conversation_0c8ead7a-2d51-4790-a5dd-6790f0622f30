//src/app/components/Header.tsx
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Logo from './Logo';
import { motion, AnimatePresence } from 'framer-motion';
import { UserButton, SignedIn, SignedOut, SignInButton, SignUpButton } from '@clerk/nextjs';

// Check if authentication is disabled
const isAuthDisabled = process.env.NEXT_PUBLIC_DISABLE_AUTH === 'true';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isScrollingUp, setIsScrollingUp] = useState(true);
  const [lastScrollPos, setLastScrollPos] = useState(0);
  const pathname = usePathname();
  // Add a mounted state to prevent hydration mismatch
  const [isMounted, setIsMounted] = useState(false);
  
  // Set mounted state to true after component mounts
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // Track scroll position for the sticky header effect
  useEffect(() => {
    const handleScroll = () => {
      const position = window.scrollY;
      
      // Determine scroll direction
      setIsScrollingUp(position < lastScrollPos);
      setLastScrollPos(position);
      
      // Set scroll state
      setScrollPosition(position);
      setIsScrolled(position > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [lastScrollPos]);
  
  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);
  
  // Function to check if a link is active
  const isActiveLink = (href: string): boolean => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname === href || pathname.startsWith(`${href}/`);
  };
  
  // Navigation links
  const navLinks = [
    { 
      name: "SEARCH", 
      href: "/search",
      isHighlighted: true,
      icon: (
        <svg className="mr-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.3-4.3"></path>
        </svg>
      )
    },
    { 
      name: "ABOUT", 
      href: "/about",
      tooltip: "Learn more about our services" 
    },
    { 
      name: "OPT-OUT", 
      href: "/opt-out",
      tooltip: "Remove your data" 
    },
    { 
      name: "FAQ", 
      href: "/faq",
      tooltip: "Frequently asked questions" 
    },
    { 
      name: "SUPPORT", 
      href: "/support",
      tooltip: "Get help with our products" 
    }
  ];

  const getBgOpacity = () => {
    if (!isScrolled) return 0.8; // At the top - slightly transparent
    return isScrollingUp ? 0.9 : 0.95; // More transparent when scrolling
  };

  return (
    <>
      <header 
        className={`fixed w-full z-40 transition-all duration-300 ${isScrolled ? 'py-1 sm:py-2' : 'py-1 sm:py-4'}`}
        style={{ transform: isScrolled && !isScrollingUp ? 'translateY(-100%)' : 'translateY(0)', transition: 'transform 0.3s ease-in-out' }}
      >
        <div className="container max-w-full mx-auto px-2 sm:px-6">
          <div 
            className="rounded-full shadow-md px-2 sm:px-6 py-1.5 sm:py-3 flex items-center justify-between backdrop-blur-md"
            style={{ 
              backgroundColor: `rgba(255, 255, 255, ${getBgOpacity()})`,
              borderBottom: isScrolled ? '1px solid rgba(226, 232, 240, 0.5)' : 'none'
            }}
          >
            {/* Logo section (left) - make logo responsive */}
            <div className="flex-shrink-0 transform origin-left scale-90 sm:scale-100">
              <Logo />
            </div>
            
            {/* Mobile menu button */}
            <button 
              className="md:hidden p-1.5 sm:p-2 text-gray-600 dark:text-gray-200 z-50"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {isMenuOpen ? (
                  <path d="M18 6L6 18M6 6l12 12" />
                ) : (
                  <path d="M3 12h18M3 6h18M3 18h18" />
                )}
              </svg>
            </button>
            
            {/* Desktop navigation - centered */}
            <nav className="hidden md:flex items-center justify-center flex-1 mx-4">
              <div className="flex items-center space-x-8">
                {navLinks.map((link) => {
                  const isActive = isActiveLink(link.href);
                  return (
                    <Link 
                      key={link.name}
                      href={link.href}
                      className={`relative inline-flex items-center justify-center text-sm group ${
                        isActive
                          ? "text-blue-600 dark:text-blue-400 font-medium"
                          : "text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 font-medium"
                      } transition-all duration-200`}
                      title={link.tooltip}
                    >
                      {link.icon}
                      {link.name}
                      {isActive ? (
                        <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-blue-600"></span>
                      ) : (
                        <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-200"></span>
                      )}
                    </Link>
                  );
                })}
              </div>
            </nav>

            {/* Right side - Button Section - TEMPORARY DISABLE: Hidden when auth is disabled */}
            <div className="hidden md:flex items-center space-x-4">
              {/* Only render auth components after component has mounted on client */}
              {!isAuthDisabled && isMounted ? (
                <>
                  <SignedIn>
                    {/* When signed in, show UserButton (avatar) */}
                    <UserButton
                      afterSignOutUrl="/"
                      appearance={{
                        elements: {
                          userButtonAvatarBox: "w-8 h-8 rounded-full"
                        }
                      }}
                    />
                  </SignedIn>
                  <SignedOut>
                    {/* When signed out, show Login/Signup buttons */}
                    <div className="flex items-center space-x-2">
                      <SignInButton mode="modal">
                        <button className="text-sm text-gray-700 dark:text-gray-200 font-medium px-4 py-1.5 rounded-full border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                          Login
                        </button>
                      </SignInButton>
                      <SignUpButton mode="modal">
                        <button className="text-sm text-white font-medium px-4 py-1.5 rounded-full bg-blue-600 hover:bg-blue-700 transition-all duration-200">
                          Signup
                        </button>
                      </SignUpButton>
                    </div>
                  </SignedOut>
                </>
              ) : isAuthDisabled ? (
                // Free production version - no auth indicators needed
                <div className="w-[144px] h-9"></div>
              ) : (
                // Display a placeholder during server render to prevent hydration issues
                <div className="w-[144px] h-9"></div>
              )}
            </div>
          </div>
        </div>
        
        {/* Mobile navigation - Apple-style menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div 
              className="md:hidden fixed inset-0 z-30"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              {/* Background overlay */}
              <motion.div 
                className="absolute inset-0 bg-black/40 backdrop-blur-sm"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setIsMenuOpen(false)}
              />
              
              {/* Menu content */}
              <motion.div 
                className="absolute inset-x-0 top-0 min-h-screen bg-gradient-to-b from-blue-600 to-blue-700 pt-20 px-4 pb-6 overflow-y-auto"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                {/* Close button in top-right corner */}
                <motion.button
                  className="absolute top-4 right-4 p-2 rounded-full bg-white/10 hover:bg-white/20 text-white transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                  aria-label="Close menu"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M18 6L6 18M6 6l12 12" />
                  </svg>
                </motion.button>

                <div className="flex flex-col space-y-5 py-4">
                  {navLinks.map((link, index) => {
                    const isActive = isActiveLink(link.href);
                    
                    return (
                      <motion.div
                        key={link.name}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 + index * 0.05 }}
                      >
                        <Link 
                          href={link.href}
                          className={`${
                            isActive 
                              ? "text-white font-bold" 
                              : "text-white/90 hover:text-white"
                          } transition-colors text-xl flex items-center py-2 border-b border-white/10`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {link.icon}
                          {link.name}
                        </Link>
                      </motion.div>
                    );
                  })}
                  
                  {/* Mobile auth options - TEMPORARY DISABLE: Hidden when auth is disabled */}
                  <div className="pt-4 border-t border-white/10 flex flex-col gap-3">
                    {!isAuthDisabled && isMounted ? (
                      <>
                        <SignedIn>
                          <div className="flex items-start">
                            <UserButton
                              afterSignOutUrl="/"
                              appearance={{
                                elements: {
                                  userButtonAvatarBox: "w-12 h-12 rounded-full",
                                  userButtonTrigger: "text-white",
                                  userButtonBox: "z-10 relative"
                                }
                              }}
                            />
                            <div
                              className="flex items-center cursor-pointer"
                              onClick={() => {
                                const userButton = document.querySelector('[data-clerk-ui="userButton"]') as HTMLElement;
                                if (userButton) userButton.click();
                              }}
                            >
                              <div className="absolute left-0 w-full h-12 z-0"></div>
                              <span className="ml-3 text-white text-lg">Account</span>
                            </div>
                          </div>
                        </SignedIn>
                        <SignedOut>
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                          >
                            <SignInButton mode="modal">
                              <button className="w-full text-center text-base text-white font-medium py-3 rounded-lg border border-white/30 hover:bg-white/10 transition-all duration-200">
                                Login
                              </button>
                            </SignInButton>
                          </motion.div>
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                          >
                            <SignUpButton mode="modal">
                              <button className="w-full text-center text-base text-blue-600 font-medium py-3 rounded-lg bg-white hover:bg-gray-100 transition-all duration-200">
                                Signup
                              </button>
                            </SignUpButton>
                          </motion.div>
                        </SignedOut>
                      </>
                    ) : isAuthDisabled ? (
                      // Free production version - no auth indicators needed
                      <div className="h-20"></div>
                    ) : (
                      // Placeholder during server render
                      <div className="h-20"></div>
                    )}
                  </div>
                  
                  <motion.div 
                    className="mt-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                  >
                    <div className="text-sm text-blue-600  px-3 py-2 rounded-lg inline-flex mx-auto border bg-white border-white/20 items-center gap-1">
                      <span className="font-medium">Built with</span> 
                      <span className="text-blue-300">♥</span> 
                      <span className="font-medium">in Miami Beach</span>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>
      
      {/* Spacer to prevent content from being hidden under the fixed header - responsive height */}
      <div className={`h-14 sm:h-${isScrolled ? '16' : '24'} transition-all duration-300`} />
    </>
  );
}
