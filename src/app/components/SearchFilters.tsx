'use client';

import { useState } from 'react';
import { Button } from './Button';

interface SearchFiltersProps {
  onFilterChange: (filters: FilterOptions) => void;
}

export interface FilterOptions {
  categories: string[];
  confidenceLevel: number;
}

export default function SearchFilters({ onFilterChange }: SearchFiltersProps) {
  const [filters, setFilters] = useState<FilterOptions>({
    categories: ['Social Media', 'Scammers', 'Sex Offenders', 'Mugshots', 'News & Blogs'],
    confidenceLevel: 50,
  });

  const toggleCategory = (category: string) => {
    const updatedCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    
    const updatedFilters = {
      ...filters,
      categories: updatedCategories,
    };
    
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  const handleConfidenceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    const updatedFilters = {
      ...filters,
      confidenceLevel: value,
    };
    
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  // Custom filter buttons with icons
  const FilterButton = ({ category, icon, activeColor }: { category: string; icon: React.ReactNode; activeColor: string }) => {
    const isActive = filters.categories.includes(category);
    
    return (
      <button
        onClick={() => toggleCategory(category)}
        className={`flex items-center rounded-full px-3 py-1.5 text-sm transition-colors whitespace-nowrap ${
          isActive
            ? `${activeColor} text-white`
            : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
        }`}
      >
        <span className="w-4 h-4 mr-1">{icon}</span>
        {category}
      </button>
    );
  };

  return (
    <div className="w-full max-w-3xl mx-auto flex flex-col sm:flex-row gap-4 sm:gap-2 p-4">
      {/* Scrollable filter buttons container */}
      <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide -mx-2 px-2">
        <FilterButton 
          category="Social Media" 
          activeColor="bg-[#2964DD]"
          icon={
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073z"/>
            </svg>
          }
        />
        
        <FilterButton 
          category="Scammers" 
          activeColor="bg-[#e53e3e]"
          icon={
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 1a11 11 0 100 22 11 11 0 000-22zm0 20a9 9 0 110-18 9 9 0 010 18zm0-16a1 1 0 00-1 1v6a1 1 0 002 0V6a1 1 0 00-1-1zm0 10a1 1 0 100 2 1 1 0 000-2z"/>
            </svg>
          }
        />
        
        <FilterButton 
          category="Sex Offenders" 
          activeColor="bg-[#805ad5]"
          icon={
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 22a10 10 0 1 1 0-20 10 10 0 0 1 0 20zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-3.54-4.46a1 1 0 0 1 1.42-1.42 3 3 0 0 0 4.24 0 1 1 0 0 1 1.42 1.42 5 5 0 0 1-7.08 0zM9 11a1 1 0 1 1 0-2 1 1 0 0 1 0 2zm6 0a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
            </svg>
          }
        />
        
        <FilterButton 
          category="Mugshots" 
          activeColor="bg-[#dd6b20]"
          icon={
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M4 4v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8.83a2 2 0
              0 0-.59-1.42L16 4a2 2 0 0 0-1.41-.59H6a2 2 0 0 0-2 2zm12 0l4 4h-4V4zm-6
              12a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm-2-8h8v2H8V8zm0 8h1.41a3.89 3.89 0 0 1-.68
              2H8v-2zm8 2v-2h1.41a3.89 3.89 0 0 1-.68 2H16z" />
            </svg>
          }
        />
        
        <FilterButton 
          category="News & Blogs" 
          activeColor="bg-[#38a169]"
          icon={
            <svg fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 20H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2zM5 6v12h14V6H5zm2 3h10v1H7V9zm0 3h10v1H7v-1zm0 3h7v1H7v-1z"/>
            </svg>
          }
        />
      </div>
      
      {/* Satisfaction guarantee - responsive version */}
      <div className="mt-2 sm:mt-0 sm:ml-auto">
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center sm:text-right max-w-[250px] sm:max-w-none mx-auto sm:mx-0">
          100% Satisfaction Guarantee: Results or refund
        </div>
      </div>
    </div>
  );
} 