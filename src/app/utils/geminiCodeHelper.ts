import { GoogleGenerativeA<PERSON> } from '@google/generative-ai';
import { GEMINI_MODELS } from '@/app/api/gemini/route';

// Initialize the Gemini API with your API key
const apiKey = process.env.NEXT_PUBLIC_GOOGLE_GEMINI_API_KEY || process.env.GOOGLE_GEMINI_API_KEY || '';
const genAI = new GoogleGenerativeAI(apiKey);

// Define common prompts for coding tasks
const PROMPTS = {
  DEBUG: "Debug the following code and explain the issue(s). Provide a solution:\n\n",
  OPTIMIZE: "Optimize the following code for better performance and readability:\n\n",
  EXPLAIN: "Explain how the following code works in detail:\n\n",
  SUGGEST: "Suggest improvements for the following code:\n\n",
  TEST: "Generate unit tests for the following code:\n\n",
  COMPLETE: "Complete the following code based on the comments or surrounding context:\n\n",
  REFACTOR: "Refactor the following code to follow best practices:\n\n",
  CONVERT: "Convert this code to {language}:\n\n",
};

/**
 * GeminiCodeHelper class provides methods for using Gemini AI to assist with coding tasks
 */
export class GeminiCodeHelper {
  private model = GEMINI_MODELS.GEMINI_2_5_PRO_PREVIEW;
  
  /**
   * Set the model to use for generating content
   * @param model - The model name (e.g., 'gemini-pro', 'gemini-pro-vision')
   */
  setModel(model: string) {
    this.model = model;
    return this;
  }

  /**
   * Debug code and get suggestions for fixing issues
   * @param code - The code to debug
   * @param language - Optional programming language for context
   */
  async debugCode(code: string, language?: string) {
    const prompt = PROMPTS.DEBUG + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    return this.generateResponse(prompt);
  }

  /**
   * Optimize code for better performance and readability
   * @param code - The code to optimize
   * @param language - Optional programming language for context
   */
  async optimizeCode(code: string, language?: string) {
    const prompt = PROMPTS.OPTIMIZE + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    return this.generateResponse(prompt);
  }

  /**
   * Get an explanation of how code works
   * @param code - The code to explain
   * @param language - Optional programming language for context
   */
  async explainCode(code: string, language?: string) {
    const prompt = PROMPTS.EXPLAIN + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    return this.generateResponse(prompt);
  }

  /**
   * Get suggestions for improving code
   * @param code - The code to get suggestions for
   * @param language - Optional programming language for context
   */
  async suggestImprovements(code: string, language?: string) {
    const prompt = PROMPTS.SUGGEST + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    return this.generateResponse(prompt);
  }

  /**
   * Generate unit tests for code
   * @param code - The code to generate tests for
   * @param language - Optional programming language for context
   * @param framework - Optional testing framework to use (e.g., 'jest', 'mocha')
   */
  async generateTests(code: string, language?: string, framework?: string) {
    let prompt = PROMPTS.TEST + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    if (framework) {
      prompt += `\n\nPlease use the ${framework} testing framework.`;
    }
    return this.generateResponse(prompt);
  }

  /**
   * Complete partial code based on context
   * @param code - The partial code to complete
   * @param language - Optional programming language for context
   */
  async completeCode(code: string, language?: string) {
    const prompt = PROMPTS.COMPLETE + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    return this.generateResponse(prompt);
  }

  /**
   * Refactor code to follow best practices
   * @param code - The code to refactor
   * @param language - Optional programming language for context
   */
  async refactorCode(code: string, language?: string) {
    const prompt = PROMPTS.REFACTOR + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    return this.generateResponse(prompt);
  }

  /**
   * Convert code from one language to another
   * @param code - The code to convert
   * @param fromLanguage - The source programming language
   * @param toLanguage - The target programming language
   */
  async convertCode(code: string, fromLanguage: string, toLanguage: string) {
    const prompt = PROMPTS.CONVERT.replace('{language}', toLanguage) + 
      `\`\`\`${fromLanguage}\n${code}\n\`\`\``;
    return this.generateResponse(prompt);
  }

  /**
   * Request a custom code-related task
   * @param code - The code to work with
   * @param instruction - Custom instructions for the AI
   * @param language - Optional programming language for context
   */
  async customCodeTask(code: string, instruction: string, language?: string) {
    const prompt = `${instruction}\n\n` + (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    return this.generateResponse(prompt);
  }

  /**
   * Generate error explanations with potential solutions
   * @param errorMessage - The error message to explain
   * @param code - Optional code context where the error occurred
   * @param language - Optional programming language for context
   */
  async explainError(errorMessage: string, code?: string, language?: string) {
    let prompt = `Explain this error and provide potential solutions:\n\n${errorMessage}`;
    
    if (code) {
      prompt += `\n\nHere is the code context:\n\n` + 
        (language ? `\`\`\`${language}\n${code}\n\`\`\`` : code);
    }
    
    return this.generateResponse(prompt);
  }

  /**
   * Provide technical documentation on a specific topic
   * @param topic - The programming topic to document
   * @param framework - Optional framework or library name
   * @param version - Optional version of the framework/library
   */
  async getTechnicalDocs(topic: string, framework?: string, version?: string) {
    let prompt = `Provide detailed technical documentation about ${topic}`;
    
    if (framework) {
      prompt += ` in ${framework}`;
      if (version) {
        prompt += ` version ${version}`;
      }
    }
    
    prompt += ". Include code examples, best practices, and common pitfalls.";
    return this.generateResponse(prompt);
  }

  /**
   * Generate a response from Gemini API
   * @param prompt - The prompt to send to the API
   * @private
   */
  private async generateResponse(prompt: string): Promise<string> {
    try {
      // Get the model
      const geminiModel = genAI.getGenerativeModel({ model: this.model });
      
      // Generate content from the prompt
      const result = await geminiModel.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error("Error generating response from Gemini:", error);
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Export a singleton instance for easy use
export const geminiCodeHelper = new GeminiCodeHelper();

// Export types for better developer experience
export type CodeLanguage = 
  | 'javascript' | 'typescript' | 'python' | 'java' | 'c#' | 'c++' | 'go' 
  | 'rust' | 'php' | 'ruby' | 'swift' | 'kotlin' | 'html' | 'css' | 'sql'
  | string;

export type TestingFramework = 'jest' | 'mocha' | 'vitest' | 'pytest' | 'junit' | 'xunit' | string;
