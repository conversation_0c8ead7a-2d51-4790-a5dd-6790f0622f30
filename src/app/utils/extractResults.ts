/**
 * Utilities for extracting and exporting search results
 */

/**
 * Extract URL from base64 encoded data
 * This function attempts to find URLs embedded within base64 data returned by the FaceCheck API
 * 
 * @param base64Data - The base64 encoded string that might contain URLs
 * @returns The extracted URL or null if none found
 */
export function extractUrlFromBase64(base64Data: string): string | null {
  if (!base64Data) return null;
  
  try {
    // Decode the base64 string to extract potentially embedded URLs
    // First, remove the data:image prefix if present
    let decodedData = base64Data;
    
    if (base64Data.startsWith('data:image')) {
      // Extract just the base64 part
      const base64Part = base64Data.split(',')[1];
      if (base64Part) {
        decodedData = base64Part;
      }
    }
    
    // Attempt to decode the base64 string
    try {
      decodedData = atob(decodedData);
    } catch (error) {
      console.warn('Failed to decode base64 data:', error);
      // Continue with the original string
    }
    
    // Use regex to search for URLs in the decoded data
    // This pattern looks for common URL structures (http/https)
    const urlPattern = /(https?:\/\/[^\s]+)/g;
    const matches = decodedData.match(urlPattern);
    
    if (matches && matches.length > 0) {
      // Return the first URL found
      return matches[0];
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting URL from base64:', error);
    return null;
  }
}

/**
 * Format search results for export to a text file
 * 
 * @param results - Array of results with scores and URLs
 * @returns Formatted string with score and URL on each line
 */
export function formatResultsForExport(results: { score: number, url: string }[]): string {
  if (!results || results.length === 0) {
    return 'No results found.';
  }
  
  // Format each result as a line with score and URL
  return results
    .map(result => `${result.score.toFixed(2)}: ${result.url}`)
    .join('\n');
}

/**
 * Export search results to a text file
 * 
 * @param results - Array of results with scores and URLs to export
 */
export function exportResultsToTextFile(results: { score: number, url: string }[]): void {
  if (!results || results.length === 0) {
    console.warn('No results to export');
    return;
  }
  
  try {
    // Format the results
    const formattedContent = formatResultsForExport(results);
    
    // Create a blob with the formatted content
    const blob = new Blob([formattedContent], { type: 'text/plain;charset=utf-8' });
    
    // Create a download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `extracted_urls_${new Date().toISOString().split('T')[0]}.txt`);
    
    // Append to document, click, and clean up
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Release the blob URL
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting results to text file:', error);
  }
} 