"use client";

import { useState, FormEvent } from "react";
import { Button } from "@/app/components/Button";
import Image from "next/image";

export default function GeminiPage() {
  const [prompt, setPrompt] = useState("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [response, setResponse] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setImageFile(file);

    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setImagePreview(null);
    }
  };

  const handleTextOnlySubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!prompt.trim()) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch("/api/gemini", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to get a response");
      }
      
      setResponse(data.response);
    } catch (err: any) {
      setError(err.message || "An error occurred");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!prompt.trim()) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const formData = new FormData();
      formData.append("prompt", prompt);
      if (imageFile) {
        formData.append("image", imageFile);
      }

      const response = await fetch("/api/gemini", {
        method: "PUT",
        body: formData,
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to get a response");
      }
      
      setResponse(data.response);
    } catch (err: any) {
      setError(err.message || "An error occurred");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const clearAll = () => {
    setPrompt("");
    setImageFile(null);
    setImagePreview(null);
    setResponse(null);
    setError(null);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Google Gemini API Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Input</h2>
          <form onSubmit={imageFile ? handleImageSubmit : handleTextOnlySubmit}>
            <div className="mb-4">
              <label htmlFor="prompt" className="block mb-2">
                Enter your prompt:
              </label>
              <textarea
                id="prompt"
                className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
                rows={4}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Ask Gemini something..."
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="image" className="block mb-2">
                Upload an image (optional, for Gemini Vision):
              </label>
              <input
                id="image"
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="w-full p-2 border rounded-md dark:bg-gray-800 dark:border-gray-700"
              />
            </div>

            {imagePreview && (
              <div className="mb-4">
                <p className="mb-2">Image preview:</p>
                <div className="relative h-48 w-full">
                  <Image
                    src={imagePreview}
                    alt="Preview"
                    fill
                    style={{ objectFit: "contain" }}
                  />
                </div>
              </div>
            )}

            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={isLoading || !prompt.trim()}
                data-loading={isLoading}
              >
                {isLoading ? "Processing..." : "Generate Response"}
              </Button>
              <Button type="button" onClick={clearAll} variant="secondary">
                Clear All
              </Button>
            </div>
          </form>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Gemini Response</h2>
          {error && (
            <div className="p-4 mb-4 bg-red-100 text-red-800 rounded-md dark:bg-red-900 dark:text-red-200">
              {error}
            </div>
          )}
          {response ? (
            <div className="p-4 border rounded-md whitespace-pre-wrap dark:bg-gray-800 dark:border-gray-700">
              {response}
            </div>
          ) : (
            <div className="p-4 border rounded-md text-gray-500 dark:bg-gray-800 dark:border-gray-700">
              Response will appear here
            </div>
          )}
        </div>
      </div>

      <div className="mt-10">
        <h2 className="text-xl font-semibold mb-4">API Documentation</h2>
        <div className="p-4 border rounded-md dark:bg-gray-800 dark:border-gray-700">
          <h3 className="text-lg font-medium mb-2">Text-only Endpoint</h3>
          <pre className="bg-gray-100 p-2 rounded dark:bg-gray-900">
            {`POST /api/gemini
Content-Type: application/json

{
  "prompt": "Your text prompt here",
  "model": "gemini-pro" // Optional, defaults to gemini-pro
}`}
          </pre>

          <h3 className="text-lg font-medium mt-4 mb-2">Multimodal Endpoint (Text + Image)</h3>
          <pre className="bg-gray-100 p-2 rounded dark:bg-gray-900">
            {`PUT /api/gemini
Content-Type: multipart/form-data

FormData:
- prompt: "Your text prompt here"
- image: [image file]
- model: "gemini-pro-vision" // Optional, defaults to gemini-pro-vision`}
          </pre>
        </div>
      </div>

      <div className="mt-10">
        <h2 className="text-xl font-semibold mb-4">Setup Instructions</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>
            Get your Gemini API key from{" "}
            <a
              href="https://ai.google.dev/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 underline"
            >
              Google AI Studio
            </a>
          </li>
          <li>Add the API key to your environment variables as <code className="bg-gray-100 p-1 rounded dark:bg-gray-900">GOOGLE_GEMINI_API_KEY</code></li>
          <li>Install the Google Generative AI package: <code className="bg-gray-100 p-1 rounded dark:bg-gray-900">npm install @google/generative-ai</code></li>
          <li>Restart your development server</li>
        </ol>
      </div>
    </div>
  );
}
