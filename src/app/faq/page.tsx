 'use client';

import * as React from "react";
import { ChevronDown, HelpCircle, Shield, BookOpen, Search, CreditCard, Smartphone } from "lucide-react";
import { motion } from "framer-motion";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { useState } from "react";

// Define proper TypeScript interfaces for our data
interface FAQ {
  question: string;
  answer: string;
}

interface Category {
  title: string;
  faqs: FAQ[];
}

type CategoryKey = 'general' | 'privacy' | 'legal' | 'search' | 'billing' | 'technical';

// Icons for FAQ categories
const categoryIcons: Record<CategoryKey, React.ElementType> = {
  general: HelpCircle,
  privacy: Shield,
  legal: BookOpen,
  search: Search,
  billing: CreditCard,
  technical: Smartphone
};

const categoryColors: Record<CategoryKey, { bg: string; text: string; light: string; border: string }> = {
  general: { 
    bg: "bg-blue-100 dark:bg-blue-900/30", 
    text: "text-blue-600 dark:text-blue-400",
    light: "bg-blue-50 dark:bg-blue-900/10",
    border: "border-blue-200 dark:border-blue-800"
  },
  privacy: { 
    bg: "bg-indigo-100 dark:bg-indigo-900/30", 
    text: "text-indigo-600 dark:text-indigo-400",
    light: "bg-indigo-50 dark:bg-indigo-900/10",
    border: "border-indigo-200 dark:border-indigo-800"
  },
  legal: { 
    bg: "bg-purple-100 dark:bg-purple-900/30", 
    text: "text-purple-600 dark:text-purple-400",
    light: "bg-purple-50 dark:bg-purple-900/10",
    border: "border-purple-200 dark:border-purple-800"
  },
  search: { 
    bg: "bg-cyan-100 dark:bg-cyan-900/30", 
    text: "text-cyan-600 dark:text-cyan-400",
    light: "bg-cyan-50 dark:bg-cyan-900/10",
    border: "border-cyan-200 dark:border-cyan-800"
  },
  billing: { 
    bg: "bg-emerald-100 dark:bg-emerald-900/30", 
    text: "text-emerald-600 dark:text-emerald-400",
    light: "bg-emerald-50 dark:bg-emerald-900/10",
    border: "border-emerald-200 dark:border-emerald-800"
  },
  technical: { 
    bg: "bg-amber-100 dark:bg-amber-900/30", 
    text: "text-amber-600 dark:text-amber-400",
    light: "bg-amber-50 dark:bg-amber-900/10",
    border: "border-amber-200 dark:border-amber-800"
  }
};

const faqCategories = {
  general: {
    title: "General Information",
    faqs: [
      {
        question: "How is FaceTrace different from other search engines?",
        answer: "FaceTrace specializes in advanced reverse image search technology, specifically designed to find visually similar images from publicly available sources. Unlike general search engines, it focuses on social media, media archives, and publicly indexed web content."
      },
      {
        question: "Does FaceTrace work on all types of images?",
        answer: "FaceTrace performs best with clear, front-facing photos but may return fewer results for low-quality, blurry, or heavily edited images. It does not support images containing multiple faces."
      },
      {
        question: "Can I search for multiple images at once?",
        answer: "Currently, FaceTrace allows users to upload one image at a time per search. For multiple searches, you'll need to upload each image separately."
      },
      {
        question: "How long does a search take?",
        answer: "Searches typically complete within a few seconds to a minute, depending on the image complexity and available indexed sources."
      },
      {
        question: "Will FaceTrace tell me a person's name?",
        answer: "No, FaceTrace does not identify or name individuals. It only retrieves publicly available images that visually match the uploaded photo."
      }
    ]
  },
  privacy: {
    title: "Privacy & Security",
    faqs: [
      {
        question: "Are my uploaded images stored permanently?",
        answer: "No, FaceTrace does not store uploaded images. Each search is processed in real-time, and images are automatically deleted after completion."
      },
      {
        question: "Does FaceTrace track my searches?",
        answer: "No, FaceTrace does not track, store, or log search history. All searches are automatically cleared within 24 hours for privacy protection."
      },
      {
        question: "Does FaceTrace collect personal data?",
        answer: "No, we do not collect, store, or sell personal data. FaceTrace only indexes publicly available content and does not process personal identifiable information."
      },
      {
        question: "Can I request to remove an image from search results?",
        answer: "Yes, if your image appears in search results, you can submit a request via our Image Removal Page to de-list it from our index. However, we cannot remove images from third-party websites."
      },
      {
        question: "Does FaceTrace share search data with law enforcement?",
        answer: "No, FaceTrace does not provide search data to law enforcement agencies or third parties."
      }
    ]
  },
  legal: {
    title: "Legal & Ethics",
    faqs: [
      {
        question: "Is FaceTrace legal to use?",
        answer: "Yes, FaceTrace operates within legal boundaries by only indexing publicly accessible images and complying with GDPR, CCPA, and fair use laws."
      },
      {
        question: "Can FaceTrace be used for identity verification?",
        answer: "No, FaceTrace is not designed for identity verification. It is a reverse image search tool that only finds visually similar images from the web."
      },
      {
        question: "Does FaceTrace comply with GDPR & CCPA?",
        answer: "Yes, FaceTrace follows GDPR and CCPA regulations, ensuring that users can request data removal and that their privacy is protected."
      },
      {
        question: "Are images sourced from private social media accounts?",
        answer: "No, FaceTrace does not access or index private accounts, messages, or restricted content. Only publicly available images are included in search results."
      },
      {
        question: "Does FaceTrace store biometric data?",
        answer: "No, FaceTrace does not collect, analyze, or store biometric identifiers. Our technology is designed for visual pattern matching, not identity tracking."
      },
      {
        question: "Can businesses or law enforcement use FaceTrace?",
        answer: "FaceTrace is intended for personal research and awareness. It is not designed for law enforcement, corporate background checks, or surveillance purposes."
      },
      {
        question: "What happens if someone misuses FaceTrace?",
        answer: "Misuse of FaceTrace for harassment, stalking, or unauthorized searches is strictly prohibited. Violating our Terms of Service may result in account suspension or legal action."
      }
    ]
  },
  search: {
    title: "Search Results",
    faqs: [
      {
        question: "What does the search score indicate?",
        answer: "Each search result shows a confidence score (0-100) indicating how closely the found image matches your uploaded photo. Higher scores represent more confident matches.\n\nScore Categories:\n• 90–100: Certain Match\n• 83–89: Confident Match\n• 70–82: Possible Match\n• Below 70: Low Confidence Match"
      },
      {
        question: "Why am I getting no results?",
        answer: "Several factors may affect results: low image quality, uncommon image sources, or the image may not be indexed in our database. Try uploading a clearer, more front-facing photo for better results."
      },
      {
        question: "How accurate are the search results?",
        answer: "FaceTrace uses advanced visual matching technology, but accuracy depends on image quality, lighting, angles, and whether the person appears in publicly indexed content. Results should be considered visual matches rather than identity confirmations."
      },
      {
        question: "Can I search for celebrities or public figures?",
        answer: "Yes, you can search for publicly known individuals. However, remember that results only show where visually similar images appear online and don't confirm identity."
      },
      {
        question: "What are the red flags in search results?",
        answer: "Red flags appear when our system detects potential concerns: images appearing on suspicious websites, multiple inconsistent profiles, or other patterns that might indicate misuse of someone's likeness."
      }
    ]
  },
  billing: {
    title: "Billing & Subscriptions",
    faqs: [
      {
        question: "What subscription plans does FaceTrace offer?",
        answer: "FaceTrace offers several subscription tiers with different features and search allowances. Please visit our Pricing page for current plans and pricing details."
      },
      {
        question: "Can I cancel my subscription at any time?",
        answer: "Yes, you can cancel your subscription at any time through your account dashboard. Your access will continue until the end of your current billing period."
      },
      {
        question: "Is there a free trial available?",
        answer: "Yes, new users can access a limited number of searches to try our service before subscribing to a paid plan."
      },
      {
        question: "How do I get a refund?",
        answer: "For refund inquiries, please refer to our Refund Policy page or contact our customer support team within 14 days of purchase."
      }
    ]
  },
  technical: {
    title: "Technical Support",
    faqs: [
      {
        question: "Does FaceTrace have a mobile app?",
        answer: "No, FaceTrace is currently a web-based platform optimized for desktop and mobile browsers. We do not have official mobile apps for iOS or Android at this time."
      },
      {
        question: "Which browsers work best with FaceTrace?",
        answer: "FaceTrace works optimally with modern browsers like Chrome, Firefox, Safari, and Edge. We recommend keeping your browser updated for the best experience."
      },
      {
        question: "What should I do if a search gets stuck?",
        answer: "If a search appears stuck, try refreshing the page or clearing your browser cache. If problems persist, please report the issue through our support page."
      },
      {
        question: "How do I report a bug or technical issue?",
        answer: "You can report technical issues through our Report Bug page. Please include as much detail as possible about the problem you're experiencing."
      }
    ]
  }
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function FAQPage() {
  const [openCategory, setOpenCategory] = useState<CategoryKey>("general");

  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container mx-auto px-4 max-w-[90rem]">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            <motion.h1 
              className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-4"
              variants={itemVariants}
            >
              Frequently Asked Questions
            </motion.h1>
            <motion.p 
              className="text-gray-600 dark:text-gray-300 text-xl max-w-3xl mx-auto"
              variants={itemVariants}
            >
              Find comprehensive answers to all your questions about FaceTrace
            </motion.p>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            {Object.entries(faqCategories).map(([key, category], index) => {
              const Icon = categoryIcons[key as CategoryKey];
              const colorClasses = categoryColors[key as CategoryKey];
              
              return (
                <motion.button
                  key={key}
                  variants={itemVariants}
                  onClick={() => setOpenCategory(key as CategoryKey)}
                  className={`flex items-center p-6 sm:p-8 rounded-xl backdrop-blur-sm border shadow-md transition-all duration-200 hover:shadow-lg
                    ${openCategory === key ? `${colorClasses.bg} border-${colorClasses.border}` : 'bg-white/80 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/80'}`}
                >
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center mr-5 ${colorClasses.bg}`}>
                    <Icon className={`w-6 h-6 ${colorClasses.text}`} />
                  </div>
                  <div className="text-left">
                    <span className={`text-lg font-medium ${openCategory === key ? colorClasses.text : 'text-gray-900 dark:text-white'}`}>
                      {category.title}
                    </span>
                  </div>
                </motion.button>
              );
            })}
          </motion.div>

          <motion.div 
            className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg p-6 md:p-8 mb-8"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            {Object.entries(faqCategories).map(([key, category]) => {
              if (key !== openCategory) return null;
              
              const colorClasses = categoryColors[key as CategoryKey];
              
              return (
                <div key={key} className="space-y-6">
                  <div className="flex items-center mb-6">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-4 ${colorClasses.bg}`}>
                      {React.createElement(categoryIcons[key as CategoryKey], { 
                        className: `w-5 h-5 ${colorClasses.text}` 
                      })}
                    </div>
                    <h2 className={`text-2xl font-bold ${colorClasses.text}`}>{category.title}</h2>
                  </div>

                  <Accordion type="single" collapsible className="space-y-4">
                    {category.faqs.map((faq, index) => (
                      <AccordionItem 
                        key={index} 
                        value={`item-${index}`} 
                        className={`rounded-lg border ${colorClasses.border} ${colorClasses.light} overflow-hidden`}
                      >
                        <AccordionTrigger className="px-6 py-4 hover:no-underline">
                          <div className="flex items-start text-left">
                            <span className="text-gray-900 dark:text-white font-medium">{faq.question}</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="px-6 pb-4 pt-0">
                          <div className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                            {faq.answer}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              );
            })}
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 