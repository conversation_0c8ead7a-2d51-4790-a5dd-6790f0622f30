'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { Shield, Users, FileText, Eye, Clock, User, Globe, AlertTriangle, Lock, Mail, Search } from "lucide-react";
import Header from "../components/Header";
import Footer from "../components/Footer";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.4 } }
};

export default function PrivacyPolicyPage() {
  // Define CSS for background pattern and gradient
  const styles = {
    bgPattern: {
      backgroundColor: '#f7faff',
      backgroundImage: `
        radial-gradient(circle at 10% 10%, rgba(146, 165, 255, 0.05) 0%, rgba(146, 165, 255, 0) 50%),
        radial-gradient(circle at 90% 90%, rgba(255, 161, 191, 0.05) 0%, rgba(255, 161, 191, 0) 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
      `,
      backgroundSize: '30px 30px'
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30" 
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%), 
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
      
      <Header />
      
      <main className="flex-1 py-10 md:py-16 relative z-10">
        <div className="container max-w-[90rem] mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="max-w-4xl mx-auto"
          >
            <motion.div
              variants={itemVariants}
              className="text-center mb-12"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/50 mb-4">
                <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-[#1d3b6c] dark:text-white mb-4">
                Privacy Policy
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Effective Date: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
              </p>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                    Introduction
                  </h2>
                  <p className="text-gray-700 dark:text-gray-300">
                    Welcome to our Privacy Policy. This document outlines how we collect, use, disclose, and safeguard your information when you use our services. We are committed to protecting your privacy and handling your data with transparency and care.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                    Information We Collect
                  </h2>
                  
                  <div className="space-y-4 mt-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                      <h3 className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mb-2">Information You Provide to Us:</h3>
                      <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>Personal identification information (name, email address, phone number)</li>
                        <li>Images containing your face when you use our search engine</li>
                        <li>Account credentials</li>
                        <li>Communications with our support team</li>
                      </ul>
                    </div>
                    
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                      <h3 className="text-xl font-medium text-[#1d3b6c] dark:text-blue-400 mb-2">Information Collected Automatically:</h3>
                      <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>Usage data (search queries, features accessed)</li>
                        <li>Device information (browser type, IP address, device type)</li>
                        <li>Cookies and similar tracking technologies</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                  <Eye className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                    How We Use Your Information
                  </h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    We use your information for the following purposes:
                  </p>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                      <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>Providing our face search services</li>
                        <li>Improving and optimizing our platform</li>
                        <li>Responding to your inquiries and support requests</li>
                      </ul>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                      <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                        <li>Enforcing our Terms of Service</li>
                        <li>Protecting against unauthorized access and fraud</li>
                        <li>Complying with legal obligations</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                  <Search className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                    Search Engine Functionality
                  </h2>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    Our face search technology works by:
                  </p>
                  <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                    <ol className="list-decimal pl-6 text-gray-700 dark:text-gray-300 space-y-2">
                      <li>Processing facial features from images you upload</li>
                      <li>Comparing these features against our index of publicly available images</li>
                      <li>Providing links to websites where similar faces appear</li>
                    </ol>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mt-4">
                    We do not search social media platforms or private accounts. Our technology is designed for self-monitoring purposes and should only be used to search for your own face.
                  </p>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="grid md:grid-cols-2 gap-8">
                <div className="flex flex-col">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                      <Clock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white">
                      Data Retention
                    </h2>
                  </div>
                  <div className="flex-grow bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                    <p className="text-gray-700 dark:text-gray-300 mb-2">
                      We retain your data for the following periods:
                    </p>
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-2">
                      <li>Account information: For as long as you maintain an active account</li>
                      <li>Search results: 30 days after completion of search</li>
                      <li>Search images: 48 hours after processing</li>
                      <li>Communications: Up to 12 months after our last interaction</li>
                    </ul>
                  </div>
                </div>
                
                <div className="flex flex-col">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                      <User className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white">
                      Your Privacy Rights
                    </h2>
                  </div>
                  <div className="flex-grow bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                    <p className="text-gray-700 dark:text-gray-300 mb-2">
                      Depending on your location, you may have the rights to:
                    </p>
                    <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-2">
                      <li>Access your personal data</li>
                      <li>Correct inaccurate data</li>
                      <li>Delete your data</li>
                      <li>Opt out of certain processing</li>
                      <li>Export your data in a portable format</li>
                    </ul>
                    <p className="text-gray-700 dark:text-gray-300 mt-2">
                      To exercise these rights, please contact us through our designated channels.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="space-y-8">
                <div className="flex items-start gap-4">
                  <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                    <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                      Opt-Out Process
                    </h2>
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                      <p className="text-gray-700 dark:text-gray-300 mb-2">
                        If you wish to exclude your face from our search results:
                      </p>
                      <ol className="list-decimal pl-6 text-gray-700 dark:text-gray-300 space-y-2">
                        <li>Submit a request through our opt-out form</li>
                        <li>Provide a clear photo of your face for identification purposes</li>
                        <li>Submit identity verification (ID with personal information redacted except for photo)</li>
                      </ol>
                      <p className="text-gray-700 dark:text-gray-300 mt-2">
                        Once verified, we will remove associated facial data from our search index. Note that this does not remove original images from their source websites.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                    <Lock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                      Data Security
                    </h2>
                    <p className="text-gray-700 dark:text-gray-300 mb-2">
                      We implement appropriate technical and organizational measures to protect your personal information, including:
                    </p>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300">
                          <li>Encryption of sensitive data</li>
                          <li>Access controls and authentication procedures</li>
                        </ul>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300">
                          <li>Regular security assessments</li>
                          <li>Employee training on data protection</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="grid md:grid-cols-2 gap-8">
                <div className="flex flex-col">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                      <Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white">
                      International Data Transfers
                    </h2>
                  </div>
                  <div className="flex-grow bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                    <p className="text-gray-700 dark:text-gray-300">
                      Your information may be transferred to and processed in countries outside your residence. We ensure appropriate safeguards are in place for such transfers in compliance with applicable data protection laws.
                    </p>
                  </div>
                </div>
                
                <div className="flex flex-col">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                      <AlertTriangle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white">
                      Children's Privacy
                    </h2>
                  </div>
                  <div className="flex-grow bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                    <p className="text-gray-700 dark:text-gray-300">
                      Our services are not intended for individuals under 18 years of age. We do not knowingly collect information from children. If we discover we have collected information from a child, we will delete it promptly.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-blue-100 dark:border-blue-700 shadow-lg p-8 mb-8"
            >
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-2">
                    Changes to This Privacy Policy
                  </h2>
                  <p className="text-gray-700 dark:text-gray-300">
                    We may update this Privacy Policy periodically to reflect changes in our practices or legal requirements. We will notify you of any material changes by posting the new policy on our website.
                  </p>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="bg-blue-50/80 dark:bg-blue-900/20 backdrop-blur-sm rounded-xl border border-blue-200 dark:border-blue-800 shadow-lg p-8 text-center"
            >
              <div className="flex flex-col items-center">
                <div className="bg-white dark:bg-gray-800 p-3 rounded-full shadow-md mb-4">
                  <Mail className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 className="text-2xl font-semibold text-[#1d3b6c] dark:text-white mb-4">
                  Contact Us
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  If you have questions about this Privacy Policy or our privacy practices, please contact us at:
                </p>
                <div className="inline-block bg-white dark:bg-gray-800 px-6 py-3 rounded-lg shadow-sm">
                  <p className="text-blue-600 dark:text-blue-400 font-medium"><EMAIL></p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
} 