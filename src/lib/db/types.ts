// src/lib/db/types.ts
import { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import * as schema from './schema';

// Infer types from the schema that exists in the database
export type User = InferSelectModel<typeof schema.users>;
export type UserInsert = InferInsertModel<typeof schema.users>;

export type GuestUser = InferSelectModel<typeof schema.guestUsers>;
export type GuestUserInsert = InferInsertModel<typeof schema.guestUsers>;

export type SearchReport = InferSelectModel<typeof schema.searchReports>;
export type SearchReportInsert = InferInsertModel<typeof schema.searchReports>;

export type Upload = InferSelectModel<typeof schema.uploads>;
export type UploadInsert = InferInsertModel<typeof schema.uploads>;

export type GuestTransaction = InferSelectModel<typeof schema.guestTransactions>;
export type GuestTransactionInsert = InferInsertModel<typeof schema.guestTransactions>;

export type ReportTransaction = InferSelectModel<typeof schema.reportTransactions>;
export type ReportTransactionInsert = InferInsertModel<typeof schema.reportTransactions>;

export type ClerkTransaction = InferSelectModel<typeof schema.clerkTransactions>;
export type ClerkTransactionInsert = InferInsertModel<typeof schema.clerkTransactions>;

export type Refund = InferSelectModel<typeof schema.refunds>;
export type RefundInsert = InferInsertModel<typeof schema.refunds>;

export type SearchResultRecord = InferSelectModel<typeof schema.searchResults>;
export type SearchResultInsert = InferInsertModel<typeof schema.searchResults>;

// Helper types for API responses
export type SearchResult = {
  id: string;
  confidence: number;
  title?: string;
  sourceUrl: string;
  thumbnail?: string;
  domain?: string;
  rawData?: any;
};
