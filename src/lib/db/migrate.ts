// src/lib/db/migrate.ts
import { neon, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { migrate } from 'drizzle-orm/neon-http/migrator';
import * as schema from './schema';
import { config } from 'dotenv';

// Load environment variables
config();

// Configure Neon to work in serverless environments
neonConfig.fetchConnectionCache = true;

// Database URL must be set
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

// This script runs migrations on the database
async function runMigrations() {
  console.log('🏗️ Running migrations...');
  
  try {
    // Create Neon SQL client with type casting to fix compatibility issues
    const sql = neon(process.env.DATABASE_URL!) as any;
    
    // Create database client
    const db = drizzle(sql, { schema });
    
    // Ensure meta directory and journal file exist
    const path = require('path');
    const fs = require('fs');
    
    const drizzleRoot = path.join(process.cwd(), 'drizzle');
    const metaPath = path.join(drizzleRoot, 'meta');
    const journalPath = path.join(metaPath, '_journal.json');
    
    // Create meta directory if it doesn't exist
    if (!fs.existsSync(metaPath)) {
      fs.mkdirSync(metaPath, { recursive: true });
    }
    
    // Create journal file if it doesn't exist
    if (!fs.existsSync(journalPath)) {
      fs.writeFileSync(journalPath, '{"version":"5","dialect":"pg","entries":[]}');
    }
    
    // Run migrations with explicitly configured paths
    await migrate(db, { 
      migrationsFolder: './drizzle/migrations',
      migrationsTable: 'drizzle_migrations',
      // Use directory structure relative to current working directory
    });
    
    console.log('✅ Migrations complete!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigrations();
