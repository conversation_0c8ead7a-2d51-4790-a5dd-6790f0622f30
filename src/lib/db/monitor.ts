import { neon, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { sql } from 'drizzle-orm';
import * as schema from './schema';

// Configure Neon to work in serverless environments
neonConfig.fetchConnectionCache = true;

// Database URL fallback to the hardcoded value if environment variable is not set
const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://facetracepro_owner:<EMAIL>/facetracepro?sslmode=require';

export type DatabaseStats = {
  isConnected: boolean;
  tables: Record<string, number>;
  timestamp: string;
  connectionTime: number;
  error?: string;
};

/**
 * Pings the database to keep it alive
 * @returns Boolean indicating success or failure
 */
export async function pingDatabase(): Promise<boolean> {
  try {
    // Create a direct connection for the ping
    const neonConnection = neon(DATABASE_URL);
    const db = drizzle(neonConnection, { schema });

    // Use the sql import from drizzle-orm to run a simple query
    await db.execute(sql`SELECT 1`);

    return true;
  } catch (error) {
    console.error('Error pinging database:', error);
    return false;
  }
}

/**
 * Gets detailed statistics about the database
 * @returns Database statistics and table counts
 */
export async function getDatabaseStats(): Promise<DatabaseStats> {
  const startTime = Date.now();

  try {
    // Create a connection
    const neonConnection = neon(DATABASE_URL);
    const db = drizzle(neonConnection, { schema });

    // Collect table counts
    const results: Record<string, number> = {};

    // Users table count
    const usersCount = await db.select({ count: sql<number>`count(*)` })
      .from(schema.users);
    results.users = usersCount[0].count;

    // Search reports table count
    const searchReportsCount = await db.select({ count: sql<number>`count(*)` })
      .from(schema.searchReports);
    results.searchReports = searchReportsCount[0].count;

    // Uploads table count
    const uploadsCount = await db.select({ count: sql<number>`count(*)` })
      .from(schema.uploads);
    results.uploads = uploadsCount[0].count;

    // Guest transactions table count
    const guestTransactionsCount = await db.select({ count: sql<number>`count(*)` })
      .from(schema.guestTransactions);
    results.guestTransactions = guestTransactionsCount[0].count;

    // Report transactions table count
    const reportTransactionsCount = await db.select({ count: sql<number>`count(*)` })
      .from(schema.reportTransactions);
    results.reportTransactions = reportTransactionsCount[0].count;



    return {
      isConnected: true,
      tables: results,
      timestamp: new Date().toISOString(),
      connectionTime: Date.now() - startTime,
    };
  } catch (error) {
    console.error('Error getting database stats:', error);
    return {
      isConnected: false,
      tables: {},
      timestamp: new Date().toISOString(),
      connectionTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}