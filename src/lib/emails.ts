import { Resend } from 'resend';

// Initialize the email client (replace with your actual API key in environment variables)
const resend = new Resend(process.env.RESEND_API_KEY);

type SearchReportEmailData = {
  reportId: string;
  resultCount: number;
  previewUrls: string[];
};

/**
 * Send an email with search report details
 * @param email Recipient email address
 * @param data Report data to include in the email
 */
export async function sendSearchReportEmail(
  email: string,
  data: SearchReportEmailData
) {
  try {
    const { reportId, resultCount } = data;
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const reportUrl = `${baseUrl}/r/${reportId}`;

    const response = await resend.emails.send({
      from: 'FaceTracePro <<EMAIL>>',
      to: email,
      subject: 'Your FaceTracePro Search Results Are Ready',
      html: `
        <div>
          <h1>Your Search Results Are Ready</h1>
          <p>Your search has been completed with ${resultCount} matches.</p>
          <p>View your full report here: <a href="${reportUrl}">${reportUrl}</a></p>
          <p>Thank you for using FaceTracePro!</p>
        </div>
      `,
    });

    return response;
  } catch (error) {
    console.error('Failed to send email:', error);
    throw error;
  }
} 