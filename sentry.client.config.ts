// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

// Global flag to track if Replay has been initialized
const GLOBAL_SENTRY_REPLAY_KEY = '__SENTRY_REPLAY_INITIALIZED__';

// Check if DSN is available before initializing
const dsn = process.env.NEXT_PUBLIC_SENTRY_DSN || process.env.SENTRY_DSN;

// Determine environment
const environment = process.env.NODE_ENV || 'development';
const isDevelopment = environment === 'development';

// Check if we've already initialized Replay
const isReplayAlreadyInitialized = typeof window !== 'undefined' && (window as any)[GLOBAL_SENTRY_REPLAY_KEY] === true;

if (dsn && !isReplayAlreadyInitialized) {
  // Set the global flag to prevent duplicate initializations
  if (typeof window !== 'undefined') {
    (window as any)[GLOBAL_SENTRY_REPLAY_KEY] = true;
  }
  
  Sentry.init({
    // Ensure DSN is loaded from environment variables
    dsn: dsn,

    // Set the environment explicitly
    environment: environment,

    // Adjust tracesSampleRate based on environment
    tracesSampleRate: isDevelopment ? 1.0 : 0.1, // Full tracing in dev, 10% in prod

    // Define integrations, including Replay
    integrations: [
      Sentry.replayIntegration({
        // Additional Replay configuration options can go here
        // Ex: maskAllText: true, blockAllMedia: true,
      }),
    ],

    // Define how likely Replay events are sampled.
    // Capture sessions at different rates for dev/prod
    replaysSessionSampleRate: isDevelopment ? 1.0 : 0.1,

    // Define how likely Replay events are sampled when an error occurs.
    // Capture 100% of errors
    replaysOnErrorSampleRate: 1.0,

    // Enable debug mode in development
    debug: isDevelopment,
  });
  
  // Log environment to console in dev mode
  if (isDevelopment) {
    console.log(`[Sentry] Initialized in ${environment} mode with Replay`);
  }
} else if (dsn && isReplayAlreadyInitialized) {
  // Initialize Sentry without Replay integration if it's already been initialized
  Sentry.init({
    dsn: dsn,
    environment: environment,
    tracesSampleRate: isDevelopment ? 1.0 : 0.1,
    debug: isDevelopment,
  });
  
  // Log environment to console in dev mode
  if (isDevelopment) {
    console.log(`[Sentry] Initialized in ${environment} mode without Replay (already initialized)`);
  }
} else {
  console.warn("SENTRY_DSN not found, Sentry client initialization skipped.");
}
