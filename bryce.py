import os
import argparse

def combine_files(project_dir, output_file, exclude_dirs=None, exclude_extensions=None):
    """
    Combine all files in a Next.js project into a single file.
    
    Args:
        project_dir (str): Path to the Next.js project directory
        output_file (str): Path to the output file
        exclude_dirs (list): Directories to exclude
        exclude_extensions (list): File extensions to exclude
    """
    if exclude_dirs is None:
        exclude_dirs = ['node_modules', '.git', '.next', 'out', 'build', '.github', '.vscode']
    
    if exclude_extensions is None:
        exclude_extensions = ['.map', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
    
    with open(output_file, 'w', encoding='utf-8') as outfile:
        outfile.write(f"# Combined Next.js Project Files from: {os.path.abspath(project_dir)}\n\n")
        
        for root, dirs, files in os.walk(project_dir):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, project_dir)
                
                # Skip files with excluded extensions
                if any(file.endswith(ext) for ext in exclude_extensions):
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as infile:
                        content = infile.read()
                        
                        outfile.write(f"\n\n{'=' * 80}\n")
                        outfile.write(f"FILE: {rel_path}\n")
                        outfile.write(f"{'=' * 80}\n\n")
                        outfile.write(content)
                except Exception as e:
                    outfile.write(f"\n\nError reading file {rel_path}: {str(e)}\n")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Combine all files in a Next.js project into a single file.')
    parser.add_argument('project_dir', help='Path to the Next.js project directory')
    parser.add_argument('output_file', help='Path to the output file')
    parser.add_argument('--exclude-dirs', nargs='+', help='Additional directories to exclude')
    parser.add_argument('--exclude-extensions', nargs='+', help='Additional file extensions to exclude')
    
    args = parser.parse_args()
    
    exclude_dirs = ['node_modules', '.git', '.next', 'out', 'build', '.github', '.vscode']
    if args.exclude_dirs:
        exclude_dirs.extend(args.exclude_dirs)
    
    exclude_extensions = ['.map', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
    if args.exclude_extensions:
        exclude_extensions.extend(args.exclude_extensions)
    
    combine_files(args.project_dir, args.output_file, exclude_dirs, exclude_extensions)
    
    print(f"All files combined into {args.output_file}")