{"builds": [{"src": "next.config.js", "use": "@vercel/next"}], "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true}, {"source": "/index", "destination": "/", "permanent": true}, {"source": "/:slug*", "has": [{"type": "host", "value": "www.facetrace.pro"}], "destination": "https://facetrace.pro/:slug*", "permanent": true}, {"source": "/api/clerk-webhook", "destination": "/api/core?action=clerk-webhook", "permanent": false}, {"source": "/api/credits", "destination": "/api/core?action=credits", "permanent": false}, {"source": "/api/db-status", "destination": "/api/core?action=db-status", "permanent": false}, {"source": "/api/ping-db", "destination": "/api/core?action=ping-db", "permanent": false}, {"source": "/api/proxy", "destination": "/api/core?action=proxy", "permanent": false}, {"source": "/api/stripe/create-customer", "destination": "/api/core?action=create-customer", "permanent": false}, {"source": "/api/stripe/create-payment-intent", "destination": "/api/core?action=create-payment-intent", "permanent": false}, {"source": "/api/stripe/create-setup-intent", "destination": "/api/core?action=create-setup-intent", "permanent": false}, {"source": "/api/webhook/stripe", "destination": "/api/core?action=stripe-webhook", "permanent": false}, {"source": "/api/report/:id", "destination": "/api/data?action=report&id=:id", "permanent": false}, {"source": "/api/report/:id/save-results", "destination": "/api/data?action=save-results&id=:id", "permanent": false}, {"source": "/api/report/:id/update", "destination": "/api/data?action=update-report&id=:id", "permanent": false}, {"source": "/api/reports/by-email", "destination": "/api/data?action=reports-by-email", "permanent": false}, {"source": "/api/search-records", "destination": "/api/data?action=search-records", "permanent": false}, {"source": "/api/search/create-guest-transaction", "destination": "/api/data?action=create-transaction", "permanent": false}, {"source": "/api/search/create-report-transaction", "destination": "/api/data?action=create-transaction", "permanent": false}, {"source": "/api/search/create-report", "destination": "/api/data?action=create-report", "permanent": false}, {"source": "/api/transactions/create", "destination": "/api/data?action=create-transaction", "permanent": false}, {"source": "/api/user/check-tokens", "destination": "/api/data?action=user-data", "permanent": false}, {"source": "/api/user/award-initial-tokens", "destination": "/api/data?action=award-tokens", "permanent": false}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-Requested-With, Content-Type, Authorization"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "Cache-Control", "value": "public, max-age=3600, stale-while-revalidate=86400"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/:path*.jpg|.jpeg|.png|.gif|.ico|.svg|.webp", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/:path*.js|.css", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "crons": [{"path": "/api/core?action=credits", "schedule": "*/5 * * * *"}, {"path": "/api/core?action=ping-db", "schedule": "*/5 * * * *"}]}