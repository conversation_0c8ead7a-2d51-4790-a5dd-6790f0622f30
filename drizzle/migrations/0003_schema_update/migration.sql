-- Update schema based on the provided SQL

-- Add unique constraint for report_id in search_reports table
ALTER TABLE search_reports ADD CONSTRAINT search_reports_report_id_key UNIQUE (report_id);

-- Ensure indexes exist for users table
CREATE INDEX IF NOT EXISTS users_clerk_id_key ON users USING BTREE(clerk_id);
CREATE INDEX IF NOT EXISTS users_email_key ON users USING BTREE(email);

-- Ensure stripe_payment_id uniqueness in transactions tables
ALTER TABLE guest_transactions ADD CONSTRAINT guest_transactions_stripe_payment_id_key UNIQUE (stripe_payment_id);
ALTER TABLE report_transactions ADD CONSTRAINT report_transactions_stripe_payment_id_key UNIQUE (stripe_payment_id);

-- Ensure foreign key constraints are properly set
ALTER TABLE search_reports 
    DROP CONSTRAINT IF EXISTS search_reports_user_id_fkey,
    ADD CONSTRAINT search_reports_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE uploads 
    DROP CONSTRAINT IF EXISTS uploads_search_report_id_fkey,
    ADD CONSTRAINT uploads_search_report_id_fkey 
    FOREIGN KEY (search_report_id) REFERENCES search_reports(id) ON DELETE SET NULL;

ALTER TABLE report_transactions 
    DROP CONSTRAINT IF EXISTS report_transactions_user_id_fkey,
    ADD CONSTRAINT report_transactions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE report_transactions 
    DROP CONSTRAINT IF EXISTS report_transactions_report_id_fkey,
    ADD CONSTRAINT report_transactions_report_id_fkey 
    FOREIGN KEY (report_id) REFERENCES search_reports(id) ON DELETE CASCADE;

ALTER TABLE guest_transactions 
    DROP CONSTRAINT IF EXISTS guest_transactions_report_id_fkey,
    ADD CONSTRAINT guest_transactions_report_id_fkey 
    FOREIGN KEY (report_id) REFERENCES search_reports(id) ON DELETE CASCADE;
