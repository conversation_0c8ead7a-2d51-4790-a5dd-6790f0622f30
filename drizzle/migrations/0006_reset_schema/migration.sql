-- Drop all existing tables (in reverse order of dependencies)
DROP TABLE IF EXISTS search_results CASCADE;
DROP TABLE IF EXISTS uploads CASCADE;
DROP TABLE IF EXISTS search_reports CASCADE;
DROP TABLE IF EXISTS guest_transactions CASCADE;
DROP TABLE IF EXISTS search_transactions CASCADE;
DROP TABLE IF EXISTS "user" CASCADE;

-- Create users table
CREATE TABLE IF NOT EXISTS "user" (
  id SERIAL PRIMARY KEY,
  name TEXT,
  email TEXT,
  email_verified TIMESTAMPTZ,
  image TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create search reports table
CREATE TABLE IF NOT EXISTS search_reports (
  id SERIAL PRIMARY KEY,
  report_id TEXT NOT NULL,
  status TEXT DEFAULT 'pending' NOT NULL,
  stripe_pm_id TEXT,
  stripe_customer_id TEXT,
  stripe_payment_intent_id TEXT,
  user_id INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  is_private BOOLEAN DEFAULT false,
  progress INTEGER DEFAULT 0,
  facecheckIdSearch TEXT,
  result_count INTEGER DEFAULT 0,
  expires_at TIMESTAMPTZ
);

-- Create search transactions table
CREATE TABLE IF NOT EXISTS search_transactions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
  report_id INTEGER REFERENCES search_reports(id) ON DELETE SET NULL,
  amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'usd',
  status TEXT NOT NULL DEFAULT 'pending',
  stripe_payment_intent_id TEXT,
  stripe_customer_id TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create guest transactions table
CREATE TABLE IF NOT EXISTS guest_transactions (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL,
  report_id INTEGER REFERENCES search_reports(id) ON DELETE SET NULL,
  amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'usd',
  status TEXT NOT NULL DEFAULT 'pending',
  stripe_payment_intent_id TEXT,
  stripe_customer_id TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create uploads table (careful with base64 fields)
CREATE TABLE IF NOT EXISTS uploads (
  id SERIAL PRIMARY KEY,
  id_search TEXT,
  search_report_id INTEGER REFERENCES search_reports(id) ON DELETE CASCADE,
  -- Don't store full objects with base64 in these fields
  input_0 JSONB,
  input_0_id_pic TEXT,
  input_0_base64 TEXT, -- Not indexed
  input_1 JSONB,
  input_1_id_pic TEXT,
  input_1_base64 TEXT, -- Not indexed
  input_2 JSONB,
  input_2_id_pic TEXT,
  input_2_base64 TEXT, -- Not indexed
  new_seen_count INTEGER DEFAULT 0,
  duplicates INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create search_results table
CREATE TABLE IF NOT EXISTS search_results (
  id SERIAL PRIMARY KEY,
  search_report_id INTEGER REFERENCES search_reports(id) ON DELETE CASCADE,
  upload_id INTEGER REFERENCES uploads(id) ON DELETE SET NULL,
  source_url TEXT,
  thumbnail TEXT,
  title TEXT,
  domain TEXT,
  confidence DECIMAL(10, 2),
  raw_data JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add critical indexes (but avoid indexing base64 fields)
CREATE UNIQUE INDEX IF NOT EXISTS idx_search_reports_report_id ON search_reports(report_id);
CREATE INDEX IF NOT EXISTS idx_search_reports_user_id ON search_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_search_reports_status ON search_reports(status);
CREATE INDEX IF NOT EXISTS idx_uploads_search_report_id ON uploads(search_report_id);
CREATE INDEX IF NOT EXISTS idx_search_results_search_report_id ON search_results(search_report_id);
CREATE INDEX IF NOT EXISTS idx_search_results_upload_id ON search_results(upload_id);

-- Add comment explaining base64 indexing issue
COMMENT ON TABLE uploads IS 'Base64 fields should not be indexed due to PostgreSQL index size limitations'; 