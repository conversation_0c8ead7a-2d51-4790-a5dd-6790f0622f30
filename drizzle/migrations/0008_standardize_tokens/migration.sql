-- Migration to standardize on tokens and remove credits
-- This migration does three things:
-- 1. Removes the credits column from users table
-- 2. Renames creditsUsed to tokensUsed in clerk_transactions table
-- 3. Updates any data that was using credits to use tokens instead

-- Add comment explaining the migration
COMMENT ON DATABASE CURRENT_DATABASE() IS 'Standardizing on tokens as the only currency - removed credits';

-- Step 1: Update users table
-- First ensure any remaining credits are added to tokens
UPDATE users 
SET tokens = tokens + credits 
WHERE credits > 0;

-- Then drop the credits column
ALTER TABLE users DROP COLUMN IF EXISTS credits;

-- Step 2: Rename creditsUsed to tokensUsed in clerk_transactions table
ALTER TABLE clerk_transactions
RENAME COLUMN credits_used TO tokens_used;

-- Step 3: Update comments in the schema to clarify the token system
COMMENT ON COLUMN users.tokens IS 'Tokens used for face searches and other premium features';
COMMENT ON COLUMN clerk_transactions.tokens_used IS 'Number of tokens used in this transaction';

-- Step 4: Add an index on the tokens column for performance
CREATE INDEX IF NOT EXISTS idx_users_tokens ON users(tokens); 