-- Add a new search_results table for storing detailed search results
CREATE TABLE IF NOT EXISTS search_results (
  id SERIAL PRIMARY KEY,
  search_report_id INTEGER REFERENCES search_reports(id) ON DELETE CASCADE,
  upload_id INTEGER REFERENCES uploads(id) ON DELETE SET NULL,
  source_url TEXT,
  thumbnail TEXT,
  title TEXT,
  domain TEXT,
  confidence DECIMAL(10, 2),
  raw_data JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS search_results_search_report_id_idx ON search_results(search_report_id);
CREATE INDEX IF NOT EXISTS search_results_upload_id_idx ON search_results(upload_id);
