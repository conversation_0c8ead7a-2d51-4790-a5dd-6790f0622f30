-- Drop the problematic index that causes the size limitation error
DROP INDEX IF EXISTS idx_uploads_input_0_base64;
DROP INDEX IF EXISTS idx_uploads_input_1_base64;
DROP INDEX IF EXISTS idx_uploads_input_2_base64;

-- Add a comment explaining why this migration exists
COMMENT ON TABLE uploads IS 'Base64 data fields should not be indexed due to PostgreSQL index size limitations';

-- We still want to be able to query by search_report_id, so keep that index
-- CREATE INDEX IF NOT EXISTS idx_uploads_search_report_id ON uploads(search_report_id); 