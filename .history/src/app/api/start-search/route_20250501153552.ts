import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';
import { auth } from '@clerk/nextjs/server';

// Import only the database functions we need for guest flow
import {
  db,
  createSearchReport,
} from '@/lib/db'; // Adjust path as needed
import { searchReports } from '@/lib/db/schema'; // Adjust path as needed

// Initialize Resend
const resendApiKey = process.env.RESEND_API_KEY;
const resendSenderEmail = process.env.RESEND_SENDER_EMAIL;
const resend = resendApiKey ? new Resend(resendApiKey) : null;

if (!resend) {
    console.warn("[start-search] RESEND_API_KEY is not set. Email notifications will be disabled.");
}
if (!resendSenderEmail) {
     console.warn("[start-search] RESEND_SENDER_EMAIL is not set. Defaulting sender, but emails might fail if domain isn't verified.");
}
const senderAddress = `FaceTrace <${resendSenderEmail || '<EMAIL>'}>`;

// Helper function to get expiry date (30 days for guests)
function getExpiryDate(): Date {
    const now = new Date();
    now.setDate(now.getDate() + 30);
    return now;
}

export async function POST(request: NextRequest) {
  try {
    const { id_search, guestEmailForReport, searchImageUrls } = await request.json();
    const { userId } = await auth();

    // --- Basic Input Validation ---
    if (!id_search || typeof id_search !== 'string') {
      return NextResponse.json({ error: 'Missing or invalid id_search' }, { status: 400 });
    }
    
    // Email is mandatory for guest flow
    if (!userId && !guestEmailForReport) {
      return NextResponse.json({ error: 'Email is required for guest searches' }, { status: 400 });
    }

    console.log(`[start-search] Received request: id_search=${id_search}, userId=${userId || 'guest'}`);

    // --- Create Search Report ---
    console.log(`[start-search] Creating search report for ${userId ? 'authenticated user' : 'guest'} ${guestEmailForReport || ''}`);

    // Create search report with appropriate userType
    const reportData = {
      userId: null, // Will be linked to a user ID later if authenticated
      userType: userId ? 'registered' : 'guest',
      email: guestEmailForReport || null, // Store guest email if provided
      facecheckIdSearch: id_search,
      searchImageUrls: searchImageUrls || (Array.isArray(id_search) ? id_search : [id_search]),
      status: 'processing' as const,
      isPrivate: true, // Reports start as private and must be unlocked
      price: null, // Initialize as NULL - price will be set only after payment-based unlock
      expiresAt: getExpiryDate(),
      resultCount: 0,
      progress: 0,
      updatedAt: new Date(), // Set initial update timestamp
      report_id: `r-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`, // Generate unique report_id for URL
      stripe_pm_id: null // Will be set during unlock phase
    };
    
    // Create search report and handle possible TypeScript errors
    const createdReport = await createSearchReport(reportData);
    
    // Handle different possible return types from createSearchReport
    const newReport = Array.isArray(createdReport) 
      ? createdReport[0] 
      : createdReport;
      
    if (!newReport || !newReport.id) {
        throw new Error("Failed to create report record");
    }
    
    console.log(`[start-search] Created search report ${newReport.id}`);

    // We no longer create a transaction record at this point - it will be created during unlock

    // --- Send Confirmation Email ---
    const reportId = newReport.id;
    const report_id = newReport.report_id; // Use report_id for URLs
    const expirationDate = newReport.expiresAt;
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://facetrace.pro'; // Fallback URL
    const reportUrl = `${appUrl}/r/${report_id}`; // Use report_id instead of ID

    // Only attempt email if Resend is configured and we have a guest recipient 
    // (authenticated users don't need the email since they're already on the platform)
    if (resend && guestEmailForReport && expirationDate) {
         const formattedExpiration = expirationDate.toLocaleString('en-US', {
            dateStyle: 'long', timeStyle: 'short'
         });

        const subject = "Your FaceTrace Search is Processing!";
        const bodyHtml = `
          <h1>FaceTrace Search Processing</h1>
          <p>Hello,</p>
          <p>Thank you for using FaceTrace! Your facial recognition search has been successfully initiated and is now processing.</p>
          <p><strong>Report ID:</strong> ${reportId}</p>
          <p>You can view the status and results of your report (once completed) by visiting the following link:</p>
          <p><a href="${reportUrl}" target="_blank" rel="noopener noreferrer">${reportUrl}</a></p>
          <p>Note: You will need to unlock your report to view the detailed results once processing is complete.</p>
          <p>This report link will expire on: <strong>${formattedExpiration}</strong></p>
          <hr>
          <p>Thanks,<br>The FaceTrace Team</p>
        `;

      try {
        console.log(`[start-search] Attempting to send processing email to ${guestEmailForReport} from ${senderAddress}...`);
        const { data, error } = await resend.emails.send({
          from: senderAddress, // Use sender from env/default
          to: guestEmailForReport,
          subject: subject,
          html: bodyHtml,
        });

        if (error) {
            console.error(`[start-search] Resend failed to send email to ${guestEmailForReport} for report ${reportId}:`, error);
        } else {
            console.log(`[start-search] Processing notification email sent successfully to ${guestEmailForReport}. Email ID: ${data?.id}`);
        }
      } catch (emailError) {
        // Catch potential synchronous errors during the call
        console.error(`[start-search] Exception sending email to ${guestEmailForReport} for report ${reportId}:`, emailError);
      }
    } else {
        console.log(`[start-search] Skipping email notification for report ${reportId}. ${userId ? 'Authenticated user' : 'Missing guest email'}`);
    }

    // --- Return Success Response ---
    return NextResponse.json({
      success: true,
      reportId: reportId,
      report_id: report_id // Include report_id in response
    });

  } catch (error: unknown) {
    console.error('[start-search] Critical Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({
      error: 'Failed to start search process.',
      details: errorMessage // Provide details in response only if safe/desired
    }, { status: 500 });
  }
}
