-- Complete Database Schema for FaceTracePro
-- Generated on 2025-04-18

-- <PERSON><PERSON> sequences first
CREATE SEQUENCE public.drizzle_migrations_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1;
CREATE SEQUENCE public.guest_transactions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1;
CREATE SEQUENCE public.report_transactions_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1;
CREATE SEQUENCE public.search_reports_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1;
CREATE SEQUENCE public.search_results_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1;
CREATE SEQUENCE public.uploads_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1;
CREATE SEQUENCE public.users_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE 2147483647 START 1;

-- <PERSON><PERSON> functions
CREATE OR REPLACE FUNCTION public.trigger_set_timestamp()
 R<PERSON>URNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.update_timestamp()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
   NEW.updated_at = NOW(); 
   RETURN NEW;
END;
$function$;

CREATE OR REPLACE FUNCTION public.update_timestamp_column()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
   NEW.updated_at = NOW(); 
   RETURN NEW;
END;
$function$;

-- Create all tables
CREATE TABLE public.clerk_transactions (
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    metadata jsonb, 
    credits_used integer DEFAULT 0 NOT NULL, 
    failure_reason text, 
    report_id text, 
    id text NOT NULL, 
    currency character varying(3) DEFAULT 'usd'::character varying NOT NULL, 
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL, 
    stripe_payment_id text, 
    stripe_customer_id text, 
    user_id integer NOT NULL, 
    amount numeric(10,2) DEFAULT 0.00 NOT NULL, 
    clerk_id text NOT NULL
);

CREATE TABLE public.drizzle_migrations (
    id integer DEFAULT nextval('drizzle_migrations_id_seq'::regclass) NOT NULL, 
    created_at timestamp with time zone DEFAULT now() NOT NULL, 
    hash text NOT NULL
);

CREATE TABLE public.guest_transactions (
    amount numeric(10,2) NOT NULL, 
    currency character varying(3) DEFAULT 'usd'::character varying NOT NULL, 
    id integer DEFAULT nextval('guest_transactions_id_seq'::regclass) NOT NULL, 
    report_id integer NOT NULL, 
    attempts integer DEFAULT 0 NOT NULL, 
    metadata jsonb, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    failure_reason text, 
    stripe_session_id text, 
    stripe_payment_id text, 
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL, 
    email text NOT NULL
);

CREATE TABLE public.guest_users (
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    email text NOT NULL, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    stripe_customer_id text, 
    id text NOT NULL
);

CREATE TABLE public.refunds (
    amount numeric(10,2) NOT NULL, 
    refund_count integer DEFAULT 0 NOT NULL, 
    last_refund_date timestamp with time zone, 
    metadata jsonb, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    stripe_payment_id text NOT NULL, 
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL, 
    currency character varying(3) DEFAULT 'usd'::character varying NOT NULL, 
    reason text, 
    stripe_refund_id text, 
    stripe_customer_id text NOT NULL, 
    id text NOT NULL
);

CREATE TABLE public.report_transactions (
    stripe_session_id text, 
    id integer DEFAULT nextval('report_transactions_id_seq'::regclass) NOT NULL, 
    user_id integer NOT NULL, 
    report_id integer NOT NULL, 
    amount numeric(10,2) NOT NULL, 
    attempts integer DEFAULT 0 NOT NULL, 
    metadata jsonb, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    failure_reason text, 
    currency character varying(3) DEFAULT 'usd'::character varying NOT NULL, 
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL, 
    stripe_payment_id text
);

CREATE TABLE public.search_reports (
    is_private boolean DEFAULT false NOT NULL, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    email text, 
    user_type character varying(50) DEFAULT 'guest'::character varying NOT NULL, 
    facecheck_id_search text, 
    status character varying(50) DEFAULT 'processing'::character varying NOT NULL, 
    user_id integer, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    price numeric(10,2), 
    result_count integer DEFAULT 0 NOT NULL, 
    failure_reason text, 
    progress integer DEFAULT 0 NOT NULL, 
    expires_at timestamp with time zone, 
    id integer DEFAULT nextval('search_reports_id_seq'::regclass) NOT NULL, 
    search_image_urls jsonb, 
    report_id text, 
    stripe_pm_id text
);

CREATE TABLE public.search_results (
    source_url text, 
    domain text, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    raw_data jsonb, 
    confidence numeric(10,2), 
    upload_id integer, 
    search_report_id integer, 
    id integer DEFAULT nextval('search_results_id_seq'::regclass) NOT NULL, 
    thumbnail text, 
    title text
);

CREATE TABLE public.uploads (
    input_2_base64 text, 
    new_seen_count integer DEFAULT 0 NOT NULL, 
    input_2 jsonb, 
    input_1 jsonb, 
    input_0 jsonb, 
    search_report_id integer, 
    input_0_base64 text, 
    input_0_id_pic text, 
    input_1_base64 text, 
    input_1_id_pic text, 
    input_2_id_pic text, 
    id_search text, 
    id integer DEFAULT nextval('uploads_id_seq'::regclass) NOT NULL, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    duplicates integer DEFAULT 0 NOT NULL
);

CREATE TABLE public.users (
    email text, 
    name text, 
    verified boolean DEFAULT false, 
    stripe_customer_id text, 
    id integer DEFAULT nextval('users_id_seq'::regclass) NOT NULL, 
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL, 
    clerk_id character varying(255) NOT NULL, 
    credits integer DEFAULT 0 NOT NULL, 
    tokens integer DEFAULT 0 NOT NULL
);

-- Add primary keys
ALTER TABLE public.clerk_transactions ADD CONSTRAINT clerk_transactions_pkey PRIMARY KEY (id);
ALTER TABLE public.drizzle_migrations ADD CONSTRAINT drizzle_migrations_pkey PRIMARY KEY (id);
ALTER TABLE public.guest_transactions ADD CONSTRAINT guest_transactions_pkey PRIMARY KEY (id);
ALTER TABLE public.guest_users ADD CONSTRAINT guest_users_pkey PRIMARY KEY (id);
ALTER TABLE public.refunds ADD CONSTRAINT refunds_pkey PRIMARY KEY (id);
ALTER TABLE public.report_transactions ADD CONSTRAINT report_transactions_pkey PRIMARY KEY (id);
ALTER TABLE public.search_reports ADD CONSTRAINT search_reports_pkey PRIMARY KEY (id);
ALTER TABLE public.search_results ADD CONSTRAINT search_results_pkey PRIMARY KEY (id);
ALTER TABLE public.uploads ADD CONSTRAINT uploads_pkey PRIMARY KEY (id);
ALTER TABLE public.users ADD CONSTRAINT users_pkey PRIMARY KEY (id);

-- Add unique constraints
ALTER TABLE public.clerk_transactions ADD CONSTRAINT clerk_transactions_stripe_payment_id_key UNIQUE (stripe_payment_id);
ALTER TABLE public.guest_transactions ADD CONSTRAINT guest_transactions_stripe_payment_id_key UNIQUE (stripe_payment_id);
ALTER TABLE public.guest_users ADD CONSTRAINT guest_users_email_key UNIQUE (email);
ALTER TABLE public.guest_users ADD CONSTRAINT guest_users_stripe_customer_id_key UNIQUE (stripe_customer_id);
ALTER TABLE public.refunds ADD CONSTRAINT refunds_stripe_refund_id_key UNIQUE (stripe_refund_id);
ALTER TABLE public.report_transactions ADD CONSTRAINT report_transactions_stripe_payment_id_key UNIQUE (stripe_payment_id);
ALTER TABLE public.search_reports ADD CONSTRAINT search_reports_report_id_key UNIQUE (report_id);
ALTER TABLE public.users ADD CONSTRAINT users_clerk_id_key UNIQUE (clerk_id);
ALTER TABLE public.users ADD CONSTRAINT users_email_key UNIQUE (email);
ALTER TABLE public.users ADD CONSTRAINT users_stripe_customer_id_key UNIQUE (stripe_customer_id);

-- Add check constraints
ALTER TABLE public.clerk_transactions ADD CONSTRAINT clerk_transactions_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'completed_credit'::character varying, 'paid'::character varying, 'failed'::character varying, 'refunded'::character varying])::text[])));
ALTER TABLE public.guest_transactions ADD CONSTRAINT guest_transactions_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'completed'::character varying, 'failed'::character varying, 'refunded'::character varying])::text[])));
ALTER TABLE public.refunds ADD CONSTRAINT refunds_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'succeeded'::character varying, 'failed'::character varying, 'canceled'::character varying])::text[])));
ALTER TABLE public.report_transactions ADD CONSTRAINT report_transactions_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'completed'::character varying, 'failed'::character varying, 'refunded'::character varying])::text[])));
ALTER TABLE public.search_reports ADD CONSTRAINT search_reports_status_check CHECK (((status)::text = ANY ((ARRAY['processing'::character varying, 'completed'::character varying, 'failed'::character varying])::text[])));
ALTER TABLE public.search_reports ADD CONSTRAINT search_reports_user_type_check CHECK (((user_type)::text = ANY ((ARRAY['guest'::character varying, 'registered'::character varying])::text[])));

-- Add foreign keys
ALTER TABLE public.clerk_transactions ADD CONSTRAINT clerk_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users (id) NO ACTION CASCADE;
ALTER TABLE public.guest_transactions ADD CONSTRAINT guest_transactions_report_id_fkey FOREIGN KEY (report_id) REFERENCES public.search_reports (id) NO ACTION CASCADE;
ALTER TABLE public.report_transactions ADD CONSTRAINT report_transactions_report_id_fkey FOREIGN KEY (report_id) REFERENCES public.search_reports (id) NO ACTION CASCADE;
ALTER TABLE public.report_transactions ADD CONSTRAINT report_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users (id) NO ACTION CASCADE;
ALTER TABLE public.search_reports ADD CONSTRAINT search_reports_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users (id) NO ACTION SET NULL;
ALTER TABLE public.search_results ADD CONSTRAINT search_results_search_report_id_fkey FOREIGN KEY (search_report_id) REFERENCES public.search_reports (id) NO ACTION CASCADE;
ALTER TABLE public.search_results ADD CONSTRAINT search_results_upload_id_fkey FOREIGN KEY (upload_id) REFERENCES public.uploads (id) NO ACTION SET NULL;
ALTER TABLE public.uploads ADD CONSTRAINT uploads_search_report_id_fkey FOREIGN KEY (search_report_id) REFERENCES public.search_reports (id) NO ACTION CASCADE;

-- Create indexes
CREATE INDEX clerk_transactions_clerk_id_idx ON public.clerk_transactions USING btree (clerk_id);
CREATE INDEX clerk_transactions_user_id_idx ON public.clerk_transactions USING btree (user_id);
CREATE INDEX guest_transactions_report_id_idx ON public.guest_transactions USING btree (report_id);
CREATE INDEX guest_transactions_email_idx ON public.guest_transactions USING btree (email);
CREATE INDEX guest_users_email_idx ON public.guest_users USING btree (email);
CREATE INDEX refunds_stripe_payment_id_idx ON public.refunds USING btree (stripe_payment_id);
CREATE INDEX refunds_stripe_customer_id_idx ON public.refunds USING btree (stripe_customer_id);
CREATE INDEX report_transactions_user_id_idx ON public.report_transactions USING btree (user_id);
CREATE INDEX report_transactions_report_id_idx ON public.report_transactions USING btree (report_id);
CREATE INDEX search_reports_user_id_idx ON public.search_reports USING btree (user_id);
CREATE INDEX search_reports_status_idx ON public.search_reports USING btree (status);
CREATE INDEX search_reports_report_id_idx ON public.search_reports USING btree (report_id);
CREATE INDEX search_results_upload_id_idx ON public.search_results USING btree (upload_id);
CREATE INDEX search_results_search_report_id_idx ON public.search_results USING btree (search_report_id);
CREATE INDEX uploads_search_report_id_idx ON public.uploads USING btree (search_report_id);
CREATE INDEX users_email_idx ON public.users USING btree (email);
CREATE INDEX users_clerk_id_idx ON public.users USING btree (clerk_id);

-- Create triggers
CREATE TRIGGER set_timestamp_clerk_transactions BEFORE UPDATE ON public.clerk_transactions FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_guest_transactions BEFORE UPDATE ON public.guest_transactions FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_guest_users BEFORE UPDATE ON public.guest_users FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_refunds BEFORE UPDATE ON public.refunds FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_report_transactions BEFORE UPDATE ON public.report_transactions FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_search_reports BEFORE UPDATE ON public.search_reports FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_search_results BEFORE UPDATE ON public.search_results FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_uploads BEFORE UPDATE ON public.uploads FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
CREATE TRIGGER set_timestamp_users BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();