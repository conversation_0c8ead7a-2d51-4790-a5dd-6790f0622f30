import type { Config } from 'drizzle-kit';
import * as dotenv from 'dotenv';
dotenv.config();

export default {
  schema: './src/lib/db/schema.ts',
  out: './drizzle/migrations',
  driver: 'pg',
  dbCredentials: {
    connectionString: process.env.DATABASE_URL!,
  },
  // Explicitly set directory for migration files
  breakpoints: true, // Enable breakpoints for more manageable migrations
  verbose: true, // More detailed output
} satisfies Config;
